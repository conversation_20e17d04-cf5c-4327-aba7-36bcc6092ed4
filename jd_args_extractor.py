#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购商品args参数提取工具
"""

import json
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import requests
from urllib.parse import urlparse, parse_qs
from webdriver_manager.chrome import ChromeDriverManager

class JDArgsExtractor:
    def __init__(self):
        self.driver = None
        self.network_logs = []
        self.args_data = []
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')

        # 设置用户代理，模拟移动端
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')

        # 启用性能日志来捕获网络请求
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})

        # 使用ChromeDriverManager自动下载和管理驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_network_logs(self):
        """提取网络日志"""
        logs = self.driver.get_log('performance')
        for log in logs:
            message = json.loads(log['message'])
            if message['message']['method'] == 'Network.responseReceived':
                url = message['message']['params']['response']['url']
                if any(keyword in url for keyword in ['api', 'ajax', 'json', 'reserve', 'book', 'args']):
                    self.network_logs.append({
                        'url': url,
                        'headers': message['message']['params']['response']['headers'],
                        'timestamp': log['timestamp']
                    })
                    
    def analyze_page_source(self):
        """分析页面源码，寻找args相关数据"""
        page_source = self.driver.page_source
        
        # 寻找JavaScript中的args数据
        args_patterns = [
            r'args\s*[:=]\s*({[^}]+})',
            r'roleId\s*[:=]\s*["\']([^"\']+)["\']',
            r'key\s*[:=]\s*["\']([^"\']+)["\']',
            r'strengthenKey\s*[:=]\s*["\']([^"\']+)["\']',
            r'activityId\s*[:=]\s*["\']([^"\']+)["\']',
            r'venderId\s*[:=]\s*["\']([^"\']+)["\']'
        ]
        
        found_data = {}
        for pattern in args_patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            if matches:
                found_data[pattern] = matches
                
        return found_data
        
    def find_reserve_buttons(self):
        """查找预约按钮并尝试点击获取请求"""
        try:
            # 等待页面加载完成
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 寻找可能的预约按钮
            button_selectors = [
                "button[class*='reserve']",
                "button[class*='book']",
                "div[class*='reserve']",
                "div[class*='book']",
                "a[class*='reserve']",
                "a[class*='book']",
                ".reserve-btn",
                ".book-btn",
                "[data-role*='reserve']",
                "[data-role*='book']"
            ]
            
            buttons = []
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    buttons.extend(elements)
                except:
                    continue
                    
            print(f"找到 {len(buttons)} 个可能的预约按钮")
            
            # 尝试点击按钮并监控网络请求
            for i, button in enumerate(buttons[:4]):  # 只处理前4个
                try:
                    print(f"尝试点击第 {i+1} 个按钮...")
                    self.driver.execute_script("arguments[0].scrollIntoView();", button)
                    time.sleep(1)
                    
                    # 记录点击前的网络日志数量
                    logs_before = len(self.driver.get_log('performance'))
                    
                    # 点击按钮
                    self.driver.execute_script("arguments[0].click();", button)
                    time.sleep(2)
                    
                    # 提取新的网络日志
                    self.extract_network_logs()
                    
                except Exception as e:
                    print(f"点击按钮 {i+1} 时出错: {e}")
                    continue
                    
        except TimeoutException:
            print("页面加载超时")
            
    def extract_from_scripts(self):
        """从页面脚本中提取args数据"""
        script_elements = self.driver.find_elements(By.TAG_NAME, "script")
        
        for script in script_elements:
            try:
                script_content = script.get_attribute('innerHTML')
                if script_content and any(keyword in script_content for keyword in ['args', 'roleId', 'key', 'strengthenKey']):
                    # 尝试提取JSON数据
                    json_patterns = [
                        r'({[^{}]*(?:"args"|"roleId"|"key"|"strengthenKey")[^{}]*})',
                        r'args\s*[:=]\s*({[^}]+})',
                        r'window\.__INITIAL_STATE__\s*=\s*({.+?});',
                        r'window\.__APP_DATA__\s*=\s*({.+?});'
                    ]
                    
                    for pattern in json_patterns:
                        matches = re.findall(pattern, script_content, re.DOTALL)
                        for match in matches:
                            try:
                                data = json.loads(match)
                                if isinstance(data, dict):
                                    self.args_data.append({
                                        'source': 'script',
                                        'data': data
                                    })
                            except:
                                continue
                                
            except Exception as e:
                continue
                
    def make_api_requests(self):
        """尝试直接调用可能的API接口"""
        # 从URL中提取活动ID
        current_url = self.driver.current_url
        activity_id_match = re.search(r'/active/([^/]+)/', current_url)
        activity_id = activity_id_match.group(1) if activity_id_match else None
        
        if activity_id:
            # 常见的京东API接口
            api_endpoints = [
                f'https://api.m.jd.com/api?appid=jd-cphdeveloper-spa&functionId=getReserveInfo&body={{"activityId":"{activity_id}"}}',
                f'https://api.m.jd.com/api?appid=jd-cphdeveloper-spa&functionId=getActivityInfo&body={{"activityId":"{activity_id}"}}',
                f'https://pro.m.jd.com/mall/active/{activity_id}/index.html?_format=json',
            ]
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1',
                'Referer': current_url,
                'Accept': 'application/json, text/plain, */*'
            }
            
            for endpoint in api_endpoints:
                try:
                    response = requests.get(endpoint, headers=headers, timeout=10)
                    if response.status_code == 200:
                        try:
                            data = response.json()
                            self.args_data.append({
                                'source': 'api',
                                'url': endpoint,
                                'data': data
                            })
                        except:
                            pass
                except:
                    continue
                    
    def run(self, url):
        """运行提取器"""
        try:
            print("正在设置浏览器...")
            self.setup_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(5)
            
            print("正在分析页面源码...")
            page_data = self.analyze_page_source()
            if page_data:
                self.args_data.append({
                    'source': 'page_source',
                    'data': page_data
                })
            
            print("正在提取脚本数据...")
            self.extract_from_scripts()
            
            print("正在查找预约按钮...")
            self.find_reserve_buttons()
            
            print("正在尝试API请求...")
            self.make_api_requests()
            
            print("正在提取网络日志...")
            self.extract_network_logs()
            
            return {
                'args_data': self.args_data,
                'network_logs': self.network_logs
            }
            
        except Exception as e:
            print(f"运行时出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    extractor = JDArgsExtractor()
    result = extractor.run(url)
    
    if result:
        print("\n=== 提取结果 ===")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        # 保存结果到文件
        with open('jd_args_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("\n结果已保存到 jd_args_result.json")
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
