#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东h5st签名生成器 - 简化版
输入body和h5st，返回新的h5st
"""

import json
import time
import hashlib
from urllib.parse import unquote, quote

def generate_h5st(body, old_h5st):
    """
    生成新的h5st签名
    
    参数:
        body: 请求body (JSON字符串)
        old_h5st: 现有的h5st参数
        
    返回:
        新的h5st签名
    """
    
    # 解析现有h5st获取指纹等信息
    try:
        decoded = unquote(old_h5st)
        parts = decoded.split(';')
        fingerprint = parts[1] if len(parts) > 1 else 'p9xwi33igjhmpm05'
        version = parts[2] if len(parts) > 2 else '35fa0'
    except:
        fingerprint = 'p9xwi33igjhmpm05'
        version = '35fa0'
    
    # 生成新时间戳和token
    timestamp = int(time.time() * 1000)
    token_hash = hashlib.md5(f"{timestamp}newBabelAwardCollection".encode()).hexdigest()[:10]
    token = f"tk05w{token_hash}"
    
    # 生成签名
    sign_str = f"{timestamp};{fingerprint};{version};{token};newBabelAwardCollection;{body}"
    signature1 = hashlib.md5(sign_str.encode()).hexdigest()
    
    # 生成加密数据
    encrypted_hash = hashlib.md5(f"{fingerprint}{timestamp}{token}".encode()).hexdigest()
    encrypted_data = f"ri_ux{encrypted_hash[:50]}"
    
    # 生成第二个签名
    signature2 = hashlib.sha256(f"{signature1}{encrypted_data}{body}".encode()).hexdigest()
    
    # 生成设备ID
    device_hash = hashlib.md5(f"{fingerprint}{timestamp}".encode()).hexdigest()
    device_id = f"ri_u{device_hash[:30]}"
    
    # 构造h5st
    h5st_parts = [
        str(timestamp)[:14],
        fingerprint,
        version,
        token,
        signature1,
        '5.1',
        str(timestamp),
        encrypted_data,
        signature2,
        device_id
    ]
    
    return quote(';'.join(h5st_parts))

# 测试示例
if __name__ == "__main__":
    # 示例数据
    test_body = '{"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554838"}'
    test_h5st = "1749061453285%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wc261032869%3Bb018b6f40ecfc21d8ba7b48f2f2b9246%3B5.1%3B1749061453285%3Bri_uxf17ef90ed3379163727ba5a6e16a6856%3B586369ee79ec35544e7588382f5aed28a730f4e5161c2967dc022a582962f854%3Bri_uf17ef90ed3379163727ba5a6e16a68"
    
    # 生成新h5st
    new_h5st = generate_h5st(test_body, test_h5st)
    
    print("=== h5st生成器测试 ===")
    print(f"输入body: {test_body}")
    print(f"输入h5st: {test_h5st[:50]}...")
    print(f"输出h5st: {new_h5st}")
    
    # 4个商品的示例
    floor_ids = ["117554838", "117554839", "117554841", "117554842"]
    
    print(f"\n=== 4个商品的h5st ===")
    for i, floor_id in enumerate(floor_ids, 1):
        body = f'{{"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"{floor_id}"}}'
        h5st = generate_h5st(body, test_h5st)
        print(f"商品{i} (floorId={floor_id}): {h5st}")
        
        # 生成完整cURL
        print(f"\ncURL命令{i}:")
        print(f"curl -X POST 'https://api.m.jd.com/client.action' \\")
        print(f"  -H 'Content-Type: application/x-www-form-urlencoded' \\")
        print(f"  -H 'Cookie: YOUR_COOKIES' \\")
        print(f"  -d 'functionId=newBabelAwardCollection' \\")
        print(f"  -d 'client=wh5' \\")
        print(f"  -d 'body={body}' \\")
        print(f"  -d 'h5st={h5st}' \\")
        print(f"  -d 'appid=babelh5'\n")
