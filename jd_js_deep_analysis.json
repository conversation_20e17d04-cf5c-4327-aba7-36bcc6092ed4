{"analysis_results": [{"url": "https://storage11.360buyimg.com/tower/babelnode/js/templates.cd9d0a0e.js", "file_size": 840671, "args_generation_logic": ["0===c||null===(i=c.args)||void 0===i||null===(a=i.split(\"", "u.default)({activityId:x.encodeActivityId,gridInfo:x.gridInfo,scene:\"1\",args:R,from:y.props.floorData&&y.props.floorData.cpChannelKey},n)", "c.default)({activityId:te.encodeActivityId,gridInfo:te.gridInfo,transParam:te.transParam,scene:m.scene||\"\",args:m.args,from:null===(d=P.props)||void 0===d||null===(v=d.floorData)||void 0===v?v", "key:\"render\"", "key:\"getContext\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"render\"", "key:e.name.replace(/^perf:/", "key:\"scriptResolveStart\"", "key:\"domResolveStart\"", "key:\"domResolveEnd\"", "key:\"firstMainChunkResolveStart\"", "key:\"docResolveStart\"", "key:\"pvReport\"", "key:\"scriptRunEnd\"", "key:\"scriptResolve\"", "key:\"cssResolve\"", "key:\"cssResolveStart\"", "key:\"domResolve\"", "key:\"scriptRun\"", "key:\"ihubSdk\"", "key:\"commonScriptResolve\"", "key:\"firstscreenrenderend\"", "key:\"activeType\"", "key:\"nodeProcess\"", "key:\"ifloorSSRProcess\"", "key:\"mainProcess\"", "key:\"serverChunk\".concat(h[g]", "key:\"\".concat(S", "key:\"\".concat(S", "key:\"fcp\"", "key:\"pipe_fcp_risk\"", "key:\"pipe_fcp\"", "key:\"general_fcp\"", "key:\"csr_fcp\"", "key:\"ssr_fcp\"", "key:\"innerLink_fcp\"", "key:\"noInnerLink_fcp\"", "key:\"ssrDomResolve\"", "key:\"earliest_img_fetchStart\"", "key:\"earliest_img_ResEnd\"", "key:\"total_img_ResEnd\"", "key:\"max_area_img_ResEnd\"", "key:\"max_area_img_loadTime\"", "key:\"firstpage_height\"", "key:\"duration#t7-t6\"", "key:\"duration#t6-t1\"", "key:\"duration#t6-t2\"", "key:\"duration#t5-t3\"", "key:\"newLoadHtmlProcess\"", "key:\"render\"", "key:\"getList\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"isSuccessRenderInServer\"", "key:\"runtimeCode\"", "key:\"shouldComponentUpdate\"", "key:\"removeDuplicateIhubAssets\"", "key:\"componentWillUnmount\"", "key:\"componentDidMount\"", "key:\"getContext\"", "key:\"fetchCodeText\"", "key:\"fetchCdnStorage\"", "key:\"fetchFloorCustomCode\"", "key=\").concat(s)", "key:\"fetchSharedCustomCode\"", "key=\").concat(f)", "key:\"loadScripts\"", "key:\"getCodeText\"", "key:\"ihubAutoReport\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"setMountNode\"", "key:\"domain\"", "key:\"resize\"", "key:\"suffix\"", "key:\"end\"", "Key:\"wh5\"", "key:\"handleIntersection\"", "key:\"watch\"", "key:\"unwatch\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"renderActions\"", "Key:\"returnurl\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"onAppAppearanceChange\"", "key:\"observe\"", "key:\"unobserve\"", "key:\"fetchTime\"", "key:\"fetchTime\"", "key:\"security_duration\"", "key:\"security_duration_first\"", "Key=\"controllers.activity.main.risk\"):(n.subCode=a.def", "Key=\"controllers.activity.checkActivityData.invalidate", "Key=\"controllers.activity.checkActivityData.invalidate", "Key=\"controllers.activity.checkActivityData.invalidate", "Key=\"controllers.activity.main.risk\"", "Key=\"controllers.activity.invalidAppUA\"", "Key=\"controllers.activity.main.emptyFloorList\"", "Key=\"controllers.activity.main.emptyFloorList\"", "Key:e.mBaseParams?e.mBaseParams.keplerSystemId:\"H5node", "Key=function(e){return M(M(M({", "key:\"images\"", "key:\"text\"", "key:\"image\"", "key:\"text\"", "key:\"indicator-\"+t", "key:e.materialId", "key:\"flex-scrollbox\"", "key:\"flex-block-\"+n", "key:t.materialId", "key:\"flex-seemore\"", "key:\"video-wrap\"", "key:e.videoId", "key:n+\"0\"", "key:n+\"1\"", "key:n+\"0\"", "key:n+\"1\"", "key:n+\"0\"", "key:n+\"1\"", "Key=\"hx_\"+a+\"_\"+o", "Key:this.floor<PERSON><PERSON>", "key:\"componentDidMount\"", "key:\"componentWillUnmount\"", "key:\"getJDFollowPlugin\"", "key:\"onvisibilitychange\"", "key:\"onIOSPageHide\"", "key:\"onIOSPageShow\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"getSnapshotBeforeUpdate\"", "key:\"render\"", "key:\"getContext\"", "key:\"render\"", "key:\"render\"", "Key=<PERSON><PERSON>visit<PERSON><PERSON>", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"getDerivedStateFromProps\"", "key:\"render\"", "key:\"UNSAFE_componentWillReceiveProps\"", "key:\"componentWillUnmount\"", "key:\"componentDidMount\"", "key:\"deactivateAndUnrender\"", "key:\"renderAndActivate\"", "key:\"render\"", "key:\"preventDefault\"", "key:\"dispatch\"", "key:\"dispatchAsync\"", "key:\"checkReminder\"", "key:\"setR<PERSON>inder\"", "key:\"getHomeIconParams\"", "key:\"configBtn\"", "key:\"setConfigNew\"", "key:\"enableTransparent\"", "key:\"setPicTitle\"", "key:\"setWebControlBack\"", "key:\"setWebGoBack\"", "key:\"setNaviBackground\"", "key:\"getPhoneBasicInfo\"", "key:\"isUseNewPV\"", "key:\"onWebviewVisible\"", "key:\"onWebviewVisibleChange\"", "key:\"getVisible\"", "key:\"invokeXrenderMethod\"", "key:\"getCpsMtaData\"", "key:\"newLoginCallback\"", "key:\"getMsgFromNative\"", "key:\"componentDidMount\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"b_webp\"", "key:\"b_avif\"", "key:\"b_dw\"", "key:\"b_dh\"", "key:\"b_dpr\"", "key:\"init\"", "key:\"observe\"", "key:\"unobserve\"", "key:\"triggerDimensionCallback\"", "key:\"disconnect\"", "key:\"currentContext\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"refresh\"", "key:\"getWrapStyle\"", "key:\"render\"", "key:<PERSON>.requestId", "key:\"renderNotices\"", "key:\"addNotice\"", "key=e.key?e.key:\"notice-\".concat((new Date).getTime()", "key===e.key", "key:\"removeN<PERSON>e\"", "key:\"render\"", "key:\"newInstance\"", "key:\"componentDidCatch\"", "key:\"render\"", "Key:3===g?m.cpId:void 0", "Key:\"returnUrl\"", "Key:\"returnUrl\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"staticRender\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"getState\"", "key:\"getTimeData\"", "key:\"initTimer\"", "key:\"clearTimer\"", "key:\"skeleton-content-\".concat(t)", "key:\"componentDidMount\"", "key:\"render\"", "key:\"getTimes\"", "key:\"setTimes\"", "key:\"clickBanner\"", "key:\"clickClose\"", "key:\"getList\"", "key:\"componentDidMount\"", "key:\"componentDidUpdate\"", "key:\"render\"", "key:\"masonryCard_\".concat(n)", "key:\"initState\"", "key:\"diffrence\"", "key:\"fill\"", "key:\"getMinCol\"", "key:\"setRef\"", "Key:\"fc_initial\"", "key:\"componentDidMount\"", "Key:\"fc_refresh\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:this.state.fcKey", "key:\"renderFcFloor\"", "key:t.moduleId", "key:\"getContext\"", "key:\"prepareFloorProps\"", "Key:1===(null===y||void 0===y||null===(c=y.app)||void ", "key:\"handleSrv\"", "key:\"getFloorHeight\"", "key:\"lang\"", "key:\"isEN\"", "key:\"isZhTW\"", "key:\"render\"", "key:\"swiper-prev-\".concat(n)", "key:\"swiper-\".concat(i)", "key:\"indicator-\".concat(t)", "key:\"render\"", "key:\"componentDidMount\"", "key:\"UNSAFE_componentWillReceiveProps\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"goLogin\"", "key:\"ipaasBridge\"", "key:\"ignoreAutoLogin\"", "key:\"_isAlive\"", "key:\"_ticktack\"", "key:\"start\"", "key:\"stop\"", "key:\"timeout\"", "key:\"reset\"", "key:\"hotzone_\".concat(t)", "key:\"hotzone_\".concat(t)", "key:\"getContext\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"anchorToBabelFloor\"", "key:\"resizeNumber\"", "key:\"\".concat(n.floorNum", "key:\"currentContext\"", "key:\"render\"", "key:\"masonry-\".concat(i)", "key:\"getContext\"", "key:\"componentDidMount\"", "key:\"setViewHeightState\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"render\"", "key:\"getStatus\"", "key:\"count\"", "key:\"isRunning\"", "key:\"generateId\"", "key:\"add\"", "key:\"start\"", "key:\"waitCurrent\"", "key:\"get<PERSON>urrent\"", "key:\"getAll\"", "key:\"_start\"", "key:\"pause\"", "key:\"componentDidMount\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"_init\"", "key:\"observe\"", "key:\"unobserve\"", "key:\"componentDidUpdate\"", "key:\"shedule\"", "key:\"componentDidMount\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"render\"", "key:e.template+\"_\"+e.floorNum+\"_\"+e.moduleId", "key:\"get<PERSON><PERSON>dContext\"", "key:\"getNextClentPaginationFloors\"", "key:\"fetchNextClientPaginationFloors\"", "key:\"componentDidMount\"", "key:\"injectIpaasFloorRuntime\"", "key:\"fillAppNode\"", "key:\"componentDidUpdate\"", "key:\"componentWillUnmount\"", "key:\"render\"", "key:null===(t=r.activityData)||void 0===t?void 0:t.__k", "key:\"report\"", "key:\"newRightCalc\"", "key:\"anchorToTargetFloor\"", "key:\"loadTs\"", "key:\"tapXViewCallBack\"", "key:\"render\"", "key:\"componentDidMount\"", "key:\"render\""], "api_call_logic": ["E=o(\"baNewBabelAwardCollection\"),x=y.context.activityInfo,O=Object.values(b),C=[],T=0", "e);if(\"newBabelAwardCollection,receiveXFCoupon,batchReceiveXFCoupon\".indexOf(n)>-1)return t", "$=o(m.functionId||\"newBabelAwardCollection\"),ee=P.context,te=ee.activityInfo,ne=ee.parseUrl,!(\"receiveXFCoupon\"===m.functionId))", "\"api.m.jd.com\"", "\"beta-api.m.jd.com\"", "\"https://api.m.jd.com/client.action\"", "\"//beta-api.m.jd.com\"", "\"//api.m.jd.com\"", "\"api.m.jd.com\"", "\"api.m.jd.com?functionId=interfaceDetect&appid=babelh5\"", "\"https://api.m.jd.com/api?appid=cyber&functionId=getFloorInfo\"", "\"beta-api.m.jd.com\"", "\"api.m.jd.com\"", "\"https://api.m.jd.com/client.action\"", "\"/client.action?functionId=\"", "body=JSON.stringify(N)", "body=JSON.stringify(ue)", "body:JSON.stringify({transParam:r.activityInfo.transParam,floorInfoList:o})", "body:JSON.stringify({serial:(0,U.getQueryString)", "body:JSON.stringify(I)", "{return function(t){try{var n=(0,m.getQueryStringFromUrl)(\"functionId\",e);if(\"newBabelAwardCollection,receiveXFCoupon,batchReceiveXFCoupon\".indexOf(n)>-1)return t;var r=\"fetch url=\".concat(e,\" exception. code or subCode not equal 0, code=\").concat(null===t||void 0===t?void 0:t.code,\", subCode=\").concat(null===t||void 0===t?void 0:t.subCode,\", msg=\").concat(null===t||void 0===t?void 0:t.msg,\", returnMsg=\").concat(null===t||void 0===t?void 0:t.returnMsg,\", queryCode=\").concat(t.queryCode,\", echo=\").concat(null===t||void 0===t?void 0:t.echo);if(\"queryBabelFeeds\"===n){if(t&&\"0\"===t.code&&(\"0\"===t.queryCode||\"1\"===t.queryCode))return t;(0,m.reportNetworkException)(r)}", "postMessage(JSON.stringify({source:\"ttt\"", "postMessage)||void 0===u||u.call(l", "postMessage({filterFloorList:e", "poster:\"data:image/png", "PostCors=function(e){return F.apply(this", "Post is client only api\")))", "Post: url is required\")))", "PostCors json parser \\u5f02\\u5e38", "poster:\"data:image/png", "postText&&u.a.createElement(kt", "postMessage)||void 0===u?void 0:u.call(l", "postMessage)||void 0===o||o.call(r", "postMessage)||void 0===o||o.call(r", "postMessage)||void 0===u||u.call(l", "postMessage)||void 0===n||n.call(t", "postMessage)||void 0===i||i.call(o", "postMessage)||void 0===d||d.call(c", "postMessage)||void 0===i||i.call(o", "postMessage)||void 0===i||i.call(o", "postMessage)||void 0===s||s.call(u", "postMessage)||void 0===o||o.call(r", "PostCors)(o.context.getFrontInterfaceUrl(\"babelExceptionPage\")", "PostCors)(r.getFrontInterfaceUrl(\"feedbackPageFrontendInfo\")", "postMessage(JSON.stringify({method:\"miniProgram\"", "PostCors)(this.context.getFrontInterfaceUrl(u)", "PostCors)(S.getFrontInterfaceUrl(\"qryLoginState\")", "PostCors)(o.context.getFrontInterfaceUrl(l)", "postMessage)||void 0===r||r.call(n", "postMessage({method:\"notifyMessageToNative\"", "PostCors)(n(\"qryLoginState\")", "postMessage)||void 0===t||t.call(n", "postMessage)||void 0===t||t.call(n", "postMessage)||void 0===g||g.call(h", "postReduceNotify", "postReduceNotify:function(){return A(arguments.length>0&&void 0!==arguments[0]?arguments[0]:{", "PostCors)(\"https://api.m.jd.com/api?appid=cyber&functionId=getFloorInfo\"", "postMessage)||void 0===r||r.call(n", "postReduceNotify:_.default.func.isRequired", "PostCors)(r(\"queryPagePopWindow\")", "PostCors)(\"\".concat(x).concat(_)", "postMessage)||void 0===n||n.call(t"], "parameter_generation": {"activityId": ["activityId:l", "activityId:null===(v=w.activityInfo)||void 0===v?void 0:v.activityId", "activityId:i.activityId", "activityId:x.encodeActivityId", "ActivityId=function(e){var t=M.exec(e||\"\")", "activityId:e.activityId", "ActivityId:e.encodeActivityId", "ActivityId:I", "activityId:e.encodeActivityId", "activityId:g", "activityId:E.activityId", "activityId:l.activityId", "ActivityId:n", "ActivityId:n", "activityId:te.encodeActivityId", "activityId:n&&n[2]||\"\"", "activityId=\"\")", "activityId:o", "activityId:null===(g=y.activityInfo)||void 0===g?void 0:g.activityId", "activityId:t.encodeActivityId", "ActivityId:n.pageParam.encodeActivityId", "ActivityId:n.pageParam.encodeActivityId", "activityId:t.activityId", "ActivityId:t.encodeActivityId", "activityId:null===n||void 0===n?void 0:n.encodeActivityId", "activityId:v"], "scene": ["scene:\"1\"", "scene:m.scene||\"\""], "floorId": ["floorId:a", "floorId:t", "floorId:r.ofn", "floorid=o.slice(0", "floorid=d.tabord+\"-\"+d.subfloorid)", "floorId=(JSON.parse(e.event_param)||{", "floorId=r.floorId||t.mid)"], "from": ["from:\"babel-v2\"", "from:\"babel-v2-m-cache\"", "from:\"babel-v2-m-comp\"", "from:y.props.floorData&&y.props.floorData.cpChannelKey", "From=t.safeInsertBottom=t.safeAreaBottomClass=t.rawWxAppName=t.query=t.protocol=t.pageUrl=t.osVersionDigitsInJdApp=t.npRate=t.noJump=t.lbsAnthText=t.lang=t.jdOs=t.jdMiniVersionGreaterOrEqual=t.jdAppVersionWithDot=t.jdAppNetworkType=t.isqqMini=t.isYhdUrl=t.isYhdApp=t.isXView=t.isWxKeplerApp=t.isWxApp=t.isWkWebview=t.isWeChat=t.isWQUrl=t.isUniversalSite=t.isTTMini=t.isStatic=t.isSevenfreshMini=t.isServer=t.isSafariAndIphoneOsVersionGte9=t.isSafari=t.isQQ=t.isPreview=t.isPerformenceSizeAvailable=t.isMobilePhone=t.isMobile=t.isMiniUrl=t.isMiniProgram=t.isJdStore=t.isJdMini=t.isJdMallMini=t.isJdFinanceApp=t.isJdAppOverWifi=t.isJdApp=t.isIphoneX=t.isIphoneJdApp=t.isIphone=t.isIpadJdApp=t.isIpad=t.isHotAct=t.isHarmonyJdApp=t.isGroupBuyApp=t.isGetEntriesApiAvailable=t.isDark=t.isCsr=t.isBeta=t.isBestPay=t.isBaiduMini=t.isBaiduAppLight=t.isAndroidJdApp=t.isAndroid=t.isAMD=t.is7FreshUrl=t.is7FreshApp=t.iphoneOsVersionDigitsInSafari=t.iosVersion=t.frontInterfaceHost=t.embedMTab=t.cornerStyle=t.channelName=t.activityType=t.activityEncodedId=t.activityBu=t.XViewVersion=t.CHANNEL_ENUM=void 0", "From=pe", "From=pe=null===(He=window.jmfe)||void 0===He?void 0:He.getUrlQuery(\"shareFrom\")", "from:null===(d=P.props)||void 0===d||null===(v=d.floorData)||void 0===v?void 0:v.cp<PERSON><PERSON><PERSON><PERSON>", "From:Q"], "pageClick": ["pageClick:\"Babel_Coupon\"", "pageClick:\"Babel_Coupon\""], "platform": ["platform:\"3\"", "platform:I", "platform:K", "platform:g.isMiniUrl?\"miniWq\":g.isWQUrl?\"wq\":\"\"", "platform=\"\""], "client": ["Client=i", "client:\"wh5\"", "client=f.isAndroid?\"android\":\"apple\"", "client:v", "Client=Me", "Client=r.siteClient)", "Client=!c.isServer", "client:\"wh5\"", "client=wh5&clientVersion=1.0.0&body=\".concat(encodeURIComponent(JSON.stringify(g)))", "Client:f", "client=wh5\")&&(x+=\"&client=wh5&clientVersion=1.0.0\")", "client=wh5&clientVersion=1.0.0&functionId=\").concat(w"], "clientVersion": ["clientVersion:\"1.0.0\"", "clientVersion=void 0", "clientVersion:k", "ClientVersion=De", "clientVersion:\"1.0.0\"", "clientVersion=1.0.0&body=\".concat(encodeURIComponent(JSON.stringify(g)))", "clientVersion=1.0.0\")", "clientVersion=1.0.0&functionId=\").concat(w"], "appid": ["appid=t.LBS_SOURCE=void 0", "appid=\"4828437ed8ff805a3a25548ed0e1da0c\"", "appid=300&returnurl=\".concat(encodeURIComponent(e))", "appid=o)", "appid:y", "appid:f.appid", "appid:f.appid", "appid:\"2b8ad271e577175adc9f0e7b93e76592\"", "appid:\"2b8ad271e577175adc9f0e7b93e76592\"", "appid=100&tgId=w6Sumwy6ci4\"))", "appid:e", "appid:\"50100\"", "appid:\"babelh5\"", "appid:p.appid", "appid:p.appid", "appid=babelh5\")", "appId:\"\"", "appid=cyber&functionId=getFloorInfo\"", "appid = \"SCLQZX_tongtianta\""]}, "function_definitions": [], "string_constants": {"urls": ["\"//beta-api.m.jd.com\"", "\"api.m.jd.com?functionId=interfaceDetect&appid=babelh5\"", "\"api.m.jd.com\"", "\"https://api.m.jd.com/api?appid=cyber&functionId=getFloorInfo\"", "\"beta-api.m.jd.com\"", "\"https://api.m.jd.com/client.action\"", "\"//api.m.jd.com\"", "\"/client.action?functionId=\""], "function_ids": ["\"babel-pointer fc_block_nofloor\"", "\"Babel_FreeCouponGuideToastClose\"", "\"Babel_FreeCouponBusinessRegister\"", "\"),window.futureBridge=window.futureBridge||new Promise((function(e){window._bridgeResolve=e})));var b=function(){if(!d.isServer){var e=(0,f.getBabelUrlParam)({})||{};return(0,u.default)((0,u.default)((0,u.default)({},e),{body:e.body?JSON.parse(e.body):{}}),{},{tttparams:d.tttparams})}return{}}();function w(e){return g[e]}function _(e){if(!e)return[];var t=[];return e.forEach((function(e){if(\"", "\"},t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{success:function(){},fail:function(){}},n=arguments.length>3&&void 0!==arguments[3]&&arguments[3],r=window.babelsdk&&window.babelsdk.navigation.goLogin||l.default;(0,i.loadCartJs)().then((function(){window.AddcartToolObj.addCart((0,o.default)((0,o.default)({source:e.source||\"", "\"===r,l=window.__api_data__,(y=window.__react_data__).app=Object.assign({},y.app,h),b=window.babelI18nText=(0,x.getI18nText)(y.app.lang),e.next=10,F(y,t);case 10:if(M(),P={width:(0,S.getDeviceInfo)().width,height:(0,S.getDeviceInfo)().height,defaultDeviceWidth:375,isWebpSupport:!1,supportAvif:y.app.supportAvif,isHybridImgSupport:S.canUseHybridImage},R||!A||N){e.next=19;break}if(l){e.next=19;break}return e.next=16,(0,k.muteException)(t.fetchActivityData());case 16:return l=e.sent,e.next=19,(0,k.muteException)((0,k.loadScript)(null===(C=l)||void 0===C?void 0:C.pageExtFileUrl));case 19:if(!(T=(0,w.getQueryString)(\"", "\"Babel_CombiExpo\"", "\"Babel_TipsExpo\"", "\"babelDebugMode\"", "\"babelh5_getAddress_mini_entry_process\"", "\",Z=window.babelI18nText,$=o(m.functionId||\"", "\"babelStayWindow\"", "\",$),e.next=21,k.default;case 21:if(e.t2=i=e.sent,e.t1=null===e.t2,e.t1){e.next=25;break}e.t1=void 0===i;case 25:if(!e.t1){e.next=29;break}e.t3=void 0,e.next=30;break;case 29:e.t3=i.resolveGiaAndFinger;case 30:if(e.t0=e.t3,e.t0){e.next=33;break}e.t0={};case 33:return ie=e.t0,ie.eid,ae=(0,s.default)(ie,x),le=(0,f.getBabelUrlParam)((0,c.default)((0,c.default)((0,c.default)({activityId:te.encodeActivityId,gridInfo:te.gridInfo,transParam:te.transParam,scene:m.scene||\"", "\"!==typeof document);o||(r=window.babelSecurity=window.babelSecurity||Promise.race([new Promise((function(e){window.babelSecurityResolver=e})),new Promise((function(e){setTimeout(e,1e4)}))]));var i=r;t.default=i},eIPV:function(e,t,n){\"", "\",document.head.appendChild(e)}}catch(t){}}function j(e,t,n){if(e&&!t.activityData){e.requestId=window.__ssrData__.requestId;var r=t.configure||{};t.activityData=y.prepareActivityData(e,t.app,r,{getFromQuery:w.getQueryString,getFromCookie:b.default.get,logger:console,floorComponents:n.channel.floors,ssrCodeData:window.ssrFloorsProps})}}function B(){var e=!1;return new Promise((function(t){if(window.babelShareData&&window.babelShareData.delayHydrate&&window.babelShareData.delayHydrateTime>0)setTimeout(r,window.babelShareData.delayHydrateTime);else if(_.sharedStore){if(!_.sharedStore.getState().container.delayHydrate)return r();_.sharedStore.subscribe((function(){_.sharedStore.getState().container.delayHydrate||r()}));var n=5e3;window.babelShareData&&window.babelShareData.maxHydrateDelayTime&&window.babelShareData.maxHydrateDelayTime<=15e3&&(n=window.babelShareData.maxHydrateDelayTime),setTimeout(r,n)}else r();function r(){e||(e=!0,t())}}))}function F(e,t){return U.apply(this,arguments)}function U(){return U=(0,l.default)(i.default.mark((function e(t,n){var r,o,a,l,u,s,c,d,f,p,v,g,m,b,_,I,k,E,x,O,C;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(u=(l=t||{}).app,s=l.activityData,c=l.activityInfo,u&&s&&s.floorList&&u.isStatic){e.next=3;break}return e.abrupt(\"", "\"},o.isBabelError&&(r.platform=\"", "\"babel-v2-m-cache\"", "\"babel-notice animated\"", "\"babel-toast babel-toast-\"", "\"babel_pageFinishPVCallback\"", "\"Babel_FreeCouponGuideToastExpo\"", "\"Babel_Loctip\"", "\"Babel_MapArea\"", "\"},getBabelUrl:function(e,t,n,r){var o=Boolean(t.pageUrl.match(/babelbeta/)),i=t.protocol,a=i+\"", "\"//m.360buyimg.com/babel/jfs/t1/22836/9/8650/2737/5c77af84E9fce75b7/272c9ac424427845.png\"", "\"===(null===(e=p.props.data.activityData.head)||void 0===e?void 0:e.globalMaskingLayer))},p.subscribeFlexCubRefresh=function(){var e=p.getChildContext(),t=(0,fe.default)(e),n=function(){var e=(0,d.default)(a.default.mark((function e(t){var n,r,o;return a.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return e.next=2,(0,re.getBabelWareInfo)({context:{config:null===(n=p.props.channel)||void 0===n?void 0:n.config,getFrontInterfaceUrl:p.props.channel.getFrontInterfaceUrl,transParam:null===(r=p.props.data.activityInfo)||void 0===r?void 0:r.transParam},data:t});case 2:if((o=e.sent)&&0!==Object.keys(o).length){e.next=5;break}return e.abrupt(\"", "\"babelSkeletop\"", "\"babelfloor\"", "\"babel_configBtnSince610\"", "\"newBabelAwardCollection\"", "\"!==e)return String(e)}var b={};if(!d.isServer){var w={isMpingload:!1,funArry:[],isInit:!1,extension:null,pageParam:{},nwld:(0,f.getnwld)(),setExtension:function(e){this.extension=e,e.babelTracking=this},init:function(e,t){var n=this;if(!n.isInit){if(n.setInitialFields(e,t),n.isInit=!0,\"", "\",babelChannel:c}),\"", "\".babel-app>.module_\"", "\"!==typeof e.babelChannel;o&&delete e.babelChannel;var i=Object.assign({},e),a=i.event_level;i.event_level=void 0===a?t:a;var u,s=o?r:(0,f.getQueryString)(\"", "\"anchorToBabelFloor\"", "\"babelcode\"", "\"Babel_CostCommonExpo\"", "\"https://storage.360buyimg.com/tower/babelnode/eruda.js?v=3.3.4\"", "\"__babel_avif_support__\"", "\",value:function(){var e=this,t=this.props,n=t.forwardRef,r=t.component,o=(t.eventId,t.eventParams,t.babelChannel,t.eventLevel,t.delay,t.newEventId,t.newEventParams,t.clickUrl,t.jsonParams,t.composeParams,t.trafficMapParam,t.trafficMapOptions,(0,i.default)(t,b));return f.default.createElement(m.default.Consumer,null,(function(t){var i=t.ownerFloorData;return f.default.createElement(r,(0,a.default)((0,a.default)({ref:n},o),{},{onClick:function(t){return e._onClick(t,i)}}))}))}}]),n}(f.PureComponent);t.default=S,S.contextTypes={tracking:p.default.object,emitCompEventListener:p.default.func,getActivityData:p.default.func},S.propTypes={component:p.default.oneOfType([p.default.element.isRequired,p.default.string.isRequired]),eventId:p.default.string.isRequired,eventParams:p.default.string,babelChannel:p.default.string,trafficMapParam:p.default.object,trafficMapOptions:p.default.object,eventLevel:p.default.string,delay:p.default.number,onClick:p.default.func};var I=f.default.forwardRef((function(e,t){return f.default.createElement(S,(0,a.default)({},e,{forwardRef:t}))}));t.RefTracking=I},NB0E:function(e,t,n){\"", "\"J_babelOpt\"", "\";if(!window.babelsdk&&(f.isJdApp||f.isGroupBuyApp)){var i=f.jdOs;\"", "\"queryBabelSingleFloor\"", "\"babel_firstscreen\"", "\",isLogin:k.isLogin,siteTransResult:k.siteTransResult,systemReqParam:(0,l.default)((0,l.default)({},w),{body:JSON.parse(w.body)}),embedMTab:d.embedMTab,isInMultiTab:d.embedMTab||!d.isJdApp&&S.hasMultiTab,cornerStyle:d.cornerStyle,isDark:d.isDark},payload:(0,l.default)({materialPromiseData:p,materialData:f,materialParams:a.materialParams,providerData:a.providerData,advIds:a.advIds,godIds:a.godIds,cmpIds:a.cmpIds,props:a.boardParams},k.shieldResult?{shieldResult:k.shieldResult}:{}),bridge:t,babelSecurity:s.default,tunnel:{onVisible:(0,c.ifloorFilter)(a),profilerModuleReport:(0,c.profilerModuleReport)(a),jsagentReport:(0,c.jsagentReport)(a),imageNotFoundReport:(0,c.imageNotFoundReport)(a),bridge:t,babelSecurity:s.default}})).then((function(){v(y),window.performance&&window.performance.mark&&(window.__Ipass_Perf__=window.__Ipass_Perf__||[]).push({id:i,materialQryType:g,perf:{t0:window.performance.getEntriesByName(\"", "\"Babel_dev\"", "\"getBabelProductPaged4Wq\"", "\"https://storage.360buyimg.com/babelview/00035212/json/asset-manifest.json?t=\"", "\"Babel_CombineCouponExpo\"", "\"Babel_GashaponExpo\"", "\"babelExceptionPage\"", "\"Babel_xviewrequest_expo\"", "\":return e.stop()}}),e)}))),A.apply(this,arguments)}function R(e,t,n){var r=arguments.length>3&&void 0!==arguments[3]?arguments[3]:{},o=arguments.length>4&&void 0!==arguments[4]?arguments[4]:{},a=arguments.length>5?arguments[5]:void 0,l=window.babelI18nText,u=document.createElement(\"", "\"getBabelProductPaged4MiniWq\"", "\"Babel_PreSaleExpo\"", "\")>-1)return!1;var l=(null===(t=window.babelShareData)||void 0===t?void 0:t.hybridRequestFunctions)||(null===(n=window.__react_data__)||void 0===n||null===(r=n.activityInfo)||void 0===r?void 0:r.hybridRequestFunctions)||[],u=(0,c.default)(l,3),s=u[0],d=void 0===s?0:s,p=u[1],v=u[2];return v=v||[],Math.random()<d&&(p||[]).indexOf(i)>-1&&(\"", "\"Babel_CombineCouponBusinessRegister\"", "\"Babel_TipsClose\"", "\",babelUserVoteSituation:\"", "\",(0,s.retry)(s.doPostCors,l)(n.getFrontInterfaceUrl(u),(0,s.getBabelUrlParam)((0,a.default)((0,a.default)({},r||{}),{},{transParam:n.getActivityData().transParam}),u)).then((function(e){return\"", "\"Babel_BottomSuctionTabExpo\"", "\".babel-app>.bab_opt_mod\"", "\"Babel_LiveShowExpo\"", "\"Babel_Coupon\"", "\"Babel_GuessYouLikeExpo\"", "\"Babel_FreeCouponBusinessClose\"", "\"===e){var j=t.taskResolve,B=t.taskReject,F=t.showMoreClickEvent;if(F&&P.resolveTracking.bind((0,p.default)(o))(F),j&&B){var U,W,H={queryParam:null===F||void 0===F||null===(U=F.params)||void 0===U?void 0:U.showMoreQueryParam};if(o.props.ipaasContext)H.topActivityEncodeId=null===(W=l.activityInfo)||void 0===W?void 0:W.encodeActivityId;(0,A.fetchBabelSingleFloor)({context:l,params:H}).then((function(e){var t,n,r,o,i,a,u,s,c=e.floorData,d=e.replaceElementList,f=null!==(t=null===c||void 0===c||null===(n=c.materialGroupList)||void 0===n||null===(r=n[0])||void 0===r?void 0:r.groupInfoList)&&void 0!==t?t:[],p=null===c||void 0===c||null===(o=c.materialGroupList)||void 0===o||null===(i=o[0])||void 0===i?void 0:i.timestamp;(p&&f.forEach((function(e){e.timestamp=p})),(0,A.collectRefreshSku)(Z(c)),0===f.length)&&(w.default.info(l.i18nText.t(\"", "\"Babel_TipsEntrance\"", "\".concat(n.moduleId)))||void 0===t?void 0:t.offsetHeight,h=Math.floor(v/a*100)||0,m={height:h,heightVw:Math.floor(v/window.innerWidth*100)||0},window.__babelSkeleton__=window.__babelSkeleton__||{},window.__babelSkeleton__[n.identityId]=m,!g||!d.attachShadow){e.next=15;break}return(y=document.querySelector(\"", "\"Babel_BannerExpo\"", "\"Babel_InventoryExpo\"", "\").concat(parseInt(window.innerHeight*n)),o=b.babelH5List.includes(t)?\"", "\"babel-notification\"", "\".concat(encodeURIComponent(h))}window.wx.miniProgram[p].apply(null,v)}}catch(m){console.log(m)}})),window.babelPageJump=c)},Wm6m:function(e,t,n){\"", "\"babel<PERSON>beta\"", "\":return e.stop()}}),e)})))()},setBusinessExtField:function(e){this.babelExtField=e},setInitialFields:function(e,t){var n,r;this.setBusinessExtField(t),this.pageParam=e,null===(n=this.extension)||void 0===n||null===(r=n.onInit)||void 0===r||r.call(n)},_mpingLoad:function(){var e=this,t=document.getElementById(\"", "\"babel-v2\"", "\")).default)(),u=l.onReady,s=l.emitReady,c={qryH5BabelFloors:301},d=u;t.onModuleReady=d},QOk5:function(e,t,n){\"", "\"babelh5_renderFloatNativeNav_entry_process\"", "\"Babel_FloorGapExpo\"", "\"//storage.360buyimg.com/babelnode/jd-jssdk/4.2.1/jd-jssdk.js\"", "\"babelh5_getAddress_\"", "\"),(0,<PERSON><PERSON><PERSON>)({},\"", "\",p._paginationScrollListener),p.setState({fetchNextError:!0,showLoading:!1})},p._addFloors=function(e,t){var n=e.floorList;if(n&&n.length)return(0,ee.preLoadAssets)(e,p.props.channel.floors).then((function(){return oe.flatFloorList(n).forEach((function(t){window.babelFloorMap[t.componentId||t.moduleId]={requestId:e.requestId}})),new Promise((function(t){p.setState({floorList:[].concat((0,u.default)(p.state.floorList),(0,u.default)(n)),filterFloorList:[].concat((0,u.default)(p.state.filterFloorList),(0,u.default)(e.filterFloorList||[]))},(function(){t()}))})).then((function(){p.emitCompEventListener(\"", "\"Babel_xviewiframeloadsuc_expo\"", "\"//m.360buyimg.com/babel/jfs/t1/32472/25/3294/10935/5c7393aeE1223a700/156dcb6590341826.png\"", "\"babel-pointer\"", "\",babelChannel:c},r.tracking.getTrafficMapParam((0,a.default)({target:E},e.props.trafficMapParam),S,_));var T=e.props,A=T.href,R=T.component,L=T.target;if(f&&(0,v.doJsonP)(f,{}).then(),p&&r.tracking.tracking({event_id:p,event_param:k,json_param:I,event_level:d||\"", "\"#J_babelOpt\"", "\"Babel_InfiniteExpo\"", "\")||(null===(e=window.eruda)||void 0===e?void 0:e._isInit))&&(t=window.console).log.apply(t,arguments)}function D(){var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.href)||window.location.href}var M=/\\/\\/(?:prodev|beta-babelser|babelserv|h5static|pro|[a-z]+-pro)(?:\\.m)?(?:\\.jd\\.com|\\.jdtest\\.net|\\.jd\\.local)\\/(?:mall|mini|hm|(?:pages|service)\\/preview\\/(?:mall|mini|hm))\\/active\\/([a-zA-Z0-9]+)\\/(?:index\\.html|[0-9]+)/;function j(){var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.search)||window.location.search}t.triggerLoadCheck=function(){var e=document.createEvent(\"", "\"Babel_FinanceCouponExpo\"", "\"Babel_FloatingIconExpo\"", "\"===e})).length>0,!j.isServer&&!e.usePipe){var H=window.__ssrData__.requestId;window.babelFloorMap=oe.flatFloorList(m.floorList||[]).reduce((function(e,t){return e[t.componentId||t.moduleId]={requestId:H},e}),{})}return p}return(0,p.default)(r,[{key:\"", "\"Babel_PageFunctionVirtual\"", "\"close_botton babel-pointer\"", "\",value:function(){if(window.babelShareData.autoLogin&&!this.ignoreAutoLogin){var e=this.props.data.activityData;e&&!e.isLogin&&this.context.goLogin()}}},{key:\"", "\"queryPreviewBabelFeeds\"", "\"Babel_MovingGraphArea\"", "\"newBabelAwardCollection,receiveXFCoupon,batchReceiveXFCoupon\"", "\"babelviewbeta\"", "\"Babel_FallsExpo\"", "\":return e.stop()}}),e,null,[[2,18]])})))).apply(this,arguments)}function S(e){var t=window.babelShareData||{},n=t.activityId,r=t.pageId,o=window.__Ipass_Perf__||[],l=o.push.bind(o);function s(t){if(Math.random()<.2){var o=t.id,l=t.perf,s=void 0===l?{}:l,c=t.materialQryType;if(o){var f,p=o.split(\"", "\"//h5.m.jd.com/babelDiy/Zeus/2xH4E7N1SsqyGxABDt2RMMAhumvo/index.html\"", "\");case 10:return o=Object.assign(window.babelShareData,g.getBasicActivityInfo(r,{getFromQuery:v.getQueryString,env:_})),t.next=13,(0,S.preLoadAssets)(r);case 13:document.body.style.backgroundColor=g.getPageBackgroundColor(_,o,r),document.title=g.getPageTitle(L,o,r),l=document.getElementById(\"", "\"__babel_webp_support__\"", "\".babel-webview-nav\"", "\"Babel_VideoExpo\"", "\"};r.isUniversalSite&&(ye.babelSite=r.isMiniUrl?r.rawWxAppName:r.channel,ye.bsChannel=r.isMiniUrl?\"", "\"babelh5_ihub_process\"", "\"babel\"", "\"getPreviewBabelWareInfo\"", "\"Babel_AuctionExpo\"", "\",k=window.babelI18nText,E=o(\"", "\"Babel_FreeCouponGuideToastOpen\"", "\"Babel_CombineCouponBusinessClose\"", "\"Babel_FloatingXview\"", "\"Babel_CombineCouponBrandToastOpen\"", "\"===d.activityEncodedId,u=window.__babel_pv_reported__,s=function(e){var n,r,i=\"", "\"Babel_H5FirstClick\"", "\"Babel_ShopExpo\"", "\"Babel_MultipleExpo\"", "\"babel-dark\"", "\");return n.pageParam.channelPoint?(u=n.pageParam.channelPoint,o&&(u=JSON.stringify((0,l.default)((0,l.default)({},JSON.parse(u)),{},{babelChannel:r})))):u=\"", "\"getBabelWareInfo\"", "\"Babel_LuckyBagEntranceExpo\"", "\"baNewBabelAwardCollection\"", "\"Babel_staytimeExpo\"", "\"Babel_AnchorExpo\"", "\"J_babelOptPage\"", "\"babelview\"", "\"//babelbeta.jd.com/activity/preview/\"", "\"babelTower\"", "\"Babel_WordsExpo\"", "\"babelh5\"", "\"fc-slider-control fc-slider-control-left babel-pointer\"", "\"babelCsrLoading\"", "\")):t(o)}))}))}))},t.doPostCors=function(e){return F.apply(this,arguments)},t.getBabelUrlParam=function(e,t){var n=window.devicePixelRatio||3,r=\"", "\");function c(){return(c=(0,i.default)(o.default.mark((function e(t){var n,r,i;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=(0,a.default)(u.default.channelExtraData),e.next=3,(0,l.default)(t);case 3:return r=e.sent,i=(0,s.getBabelUrlParam)({activityId:t.encodeActivityId,pageId:t.pageId,transParam:t.transParam,riskParam:r},u.default.channelExtraData),e.abrupt(\"", "\"Babel_ShowMoreUnfoldExpo\"", "\"Babel_TitleTopLogoExpo\"", "\"Babel_TryExpo\"", "\",getBabelWareInfo:(o.isPreview,\"", "\"Babel_RankingExpo\"", "\"https://m.360buyimg.com/babel/jfs/t1/103616/10/35113/1161/6364b591E00f1abf6/e58b9a866a3dd738.png\"", "\"Babel_LoctipExpo\"", "\"fetchBabelSingleFloor \\u8bf7\\u6c42\\u5f02\\u5e38\\uff1a\"", "\"Babel_CombineCouponGuideToastClose\"", "\"Babel_PageheightExpo\"", "\"Babel_xviewshow_expo\"", "\",babelModifyLotteryAddress:\"", "\"//m.360buyimg.com/babel/jfs/t1/26310/35/8555/1636/5c77af84Eb90872b1/1229bbebf7c00265.png\"", "\",n=new window.MPing.inputs.RmCart(t,r);break;default:n=new window.MPing.inputs.AddCart(t,r)}for(var o in e=e||{})n[o]=e[o];var i=new window.MPing;Object.assign(n,window.babelsdk&&window.babelsdk.getUnifyExtField&&window.babelsdk.getUnifyExtField()),i.send(n)},_clickTracking:function(e){var t,n,r=e.__floorInfo__||{};e.__floorInfo__&&delete e.__floorInfo__;for(var o=arguments.length,i=new Array(o>1?o-1:0),a=1;a<o;a++)i[a-1]=arguments[a];this.checkTracking.apply(this,[r,e].concat(i));var l=!1;if((null===i||void 0===i?void 0:i.length)>0&&\"", "\"Babel_CombineCouponBrandToastClose\"", "\");function d(e,t){(null==t||t>e.length)&&(t=e.length);for(var n=0,r=new Array(t);n<t;n++)r[n]=e[n];return r}function f(){return(f=(0,u.default)(o.default.mark((function e(t){var n,r,i,l,u;return o.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.context,r=t.params,i=t.retryCount,l=void 0===i?1:i,u=n.config.queryBabelSingleFloor,e.abrupt(\"", "\"Babel_H5PullDownVirtual\"", "\"Babel_PromoPdExpo\"", "\".babel-app\"", "\"BABEL\"", "\"Babel_TitleTopLogo\"", "\"Babel_ErrorPageExpo\"", "\"!==s?JSON.stringify({pageId:n.pageParam.page_param,encodeActivityId:n.pageParam.encodeActivityId,babelChannel:s,cv:\"", "\"babel-corner-\"", "\"),L=(0,c.getBabelUrlParam)((0,u.default)((0,u.default)({activityId:x.encodeActivityId,gridInfo:x.gridInfo,scene:\"", "\".babel-app > .bab_opt_mod\"", "\"//storage11.360buyimg.com/tower/babelnode/zepto.js\"", "\"Babel_DIYExpo\"", "\"api.m.jd.com?functionId=interfaceDetect&appid=babelh5\"", "\"Babel_DiyFloorVirtual\"", "\"),(0,v.getBabelUrlParam)({stayWindow:\"", "\"babel_updateCheckOutBar_\"", "\"),window.location.reload());var _=(0,a.useMemo)((function(){var e,t,r,a,l,u,s,v,h,y,b,_,S,I,k,E;if(d.isServer)return{};var x=n.moduleId,O={babelSdk:null===(e=(t=window).getBabelSdk)||void 0===e?void 0:e.call(t,n),env:{moduleId:x,ofn:n.ofn,position:n.position,requestId:null===(r=(window.babelFloorMap||{})[x])||void 0===r?void 0:r.requestId,supportTran:null===(a=w.activityInfo)||void 0===a?void 0:a.isOpacityTitle,firstScreen:n.__first_screen__||!1,pageType:d.isStatic?\"", "\",babel:\"", "\"//storage11.360buyimg.com/tower/babelnode/jssdk.js\"", "\"Babel_GroupExpo\"", "\",{value:!0}),t.collectRefreshSku=function(e){p.push.apply(p,(0,l.default)(e))},t.fetchBabelSingleFloor=function(e){return f.apply(this,arguments)},t.getAuctionAutoRefreshTimer=function e(t){if(e.instance)return e.instance;var n=t.maxCalls,r=void 0===n?6:n,o=t.duration,i=void 0===o?2e4:o,a=t.ctx,l=0,u=null;(0,c.onPageVisibleChange)((function(t){t?e.instance.start():e.instance.stop()}));return e.instance={start:function(){var e=this;this.stop(),u=setInterval((function(){l++,a.emitCompEventListener(\"", "\"getBabelProductPaged\"", "\"queryBabelFeeds\"", "\"getBabelChannelExtraData\"", "\"//m.360buyimg.com/babel/jfs/t1/25177/37/8130/8067/5c7393aeE378b6700/6b8ac70bc516100a.png\"", "\",{value:!0}),t.init=function(){var e=window._codeFloors=[];window.babelFloorMap={},window.__ssrData__=window.__ssrData__||{},window.iJsonp=function(e){window.performance&&window.performance.mark&&window.performance.mark(\"", "\"===(0,i.default)(e.extraData)&&(e.extraData.babelVersion=r))}catch(u){}if(e&&e.errType===t.JSAGENT_EXCEPTION_TYPE.net&&e.errMsg){var n=l.exec(e.errMsg);if(n&&n[2]){var o=n[2],a=c[o]||e.errCode;e.errCode=a}}return e}));t.jsagentAddInterceptor((function(e){var t,n=(t=null===e||void 0===e?void 0:e.errMsg,[/ifloors\\/([^/]+)\\/static/,/ifloors\\/taro-ihub\\/([^/]+)\\//].reduce((function(e,n){if(e)return e;var r=n.exec(t)||[];return(0,o.default)(r,2)[1]}),\"", "\"Babel_EntranceExpo\"", "\"Babel_LeavepageExpo\"", "\"babelh5_app_qryH5BabelFloors\"", "\",{value:!0}),t.babelH5List=void 0;t.babelH5List=[\"", "\"),(0,a.getBabelUrlParam)({},\"", "\"),r=Object.assign({},e.params);r.subChannel=2;var u=(0,o.getBabelUrlParam)(r,\"", "\"Babel_PackageExpo\"", "\"Babel_CarouselExpo\"", "\"Babel_RushExpo\"", "\"batchBabelReceiveCoupon\"", "\"),e.yhd_ut=window._ut;var se=(0,y.get)()&&(0,y.get)().overrideBabelColorApiBody;se&&\"", "\"Babel_FreeCouponBusinessLogin\"", "\"babel-error\"", "\"babelProductAppointment\"", "\"xview \\u5f39\\u51fa\\u5f02\\u5e38, \\u515c\\u5e95babelSdk.jump.toM\\u8df3\\u8f6c:\"", "\"Babel_MatchBuyExpo\"", "\"),r=(0,a.default)(n,2),o=r[0],u=r[1],s=v(o),c=p(o,s),d=(0,a.default)(c,2),h=d[0],y=d[1];return(0,l.useEffect)((function(){window.babel_trigger_anchor_action=function(e){try{if(\"", "\"Babel_CountdownExpo\"", "\"babelGetGuideTips\"", "\"Babel_AgilityExpo\"", "\"Babel_NoTestResExpo\"", "\"Babel_FreeCouponBrandToastOpen\"", "\"Babel_FreeCouponOneStepGet\"", "\"babel-pointer \"", "\",p.filter((function(e){var t;return((null===(t=e[2])||void 0===t?void 0:t.skuGroups)||[]).indexOf(c)>-1})).forEach((function(e){var t=(0,i.default)(e,2),n=t[0],r=t[1],o=u.find((function(e){return e.skuId===n}));if(!o)return o={skuId:n,babelParam:[r]},void u.push(o);o.babelParam=Array.from(new Set([r].concat((0,l.default)(o.babelParam))))})),f=n.config.getBabelWareInfo,h=n.getFrontInterfaceUrl(f),g=n.transParam,m=[],y=0;case 11:if(!(y<u.length)){e.next=20;break}if((b=u.slice(y,y+100)).length){e.next=15;break}return e.abrupt(\"", "\"defaultwindow_close babel-pointer\"", "\"),babelChannel:t(\"", "\"),(0,f.getBabelUrlParam)({isFollow:\"", "\"Babel_CombineCouponBusinessLogin\"", "\"),w=m&&!y||p||f||h;return o.isBabelError=m,o.state={floorList:[],errorPageInfo:{},newTpl:w},o}return(0,u.default)(n,[{key:\"", "\",{value:!0}),t.ExpoElement=function(e){var t=e.eventId,n=e.srv,r=e.srvData,s=e.babel<PERSON>hannel,c=void 0===s?\"", "\"Babel_InfoshoppingguideExpo\"", "\")||i.has<PERSON>eb<PERSON>iewNav))){e.next=28;break}return e.next=5,Promise.resolve(window.babelsdk&&window.babelsdk.native&&(null===(l=(u=window.babelsdk.native).getActualNaviStatusHeight)||void 0===l?void 0:l.call(u)));case 5:if(!(s=e.sent)){e.next=13;break}c=Number(s.navigationHeight),d=Number(s.statusBarHeight),f=c+d,p.setState({statusHeight:isNaN(f)?0:f,navigationHeight:c,statusBarHeight:d}),e.next=28;break;case 13:if(!j.isJdApp||!j.versionGreaterOrEqual(\"", "\"babel_fcp\"", "\":return n.stop()}}),n,null,[[18,24]])})))()},pvTracking:function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},t=arguments.length>1?arguments[1]:void 0,n=this;if(n.isMpingload)try{var r,o,i=new window.MPing.inputs.PV({aid:n.pageParam.activityId});i.page_name=n.pageParam.page_name,i.page_param=n.pageParam.page_param,n.nwld&&(i.nwld=n.nwld),n.pageParam.parent_encode_aid&&(i.parent_encode_aid=n.pageParam.parent_encode_aid);var a=p.default.getCpsMtaData();Object.assign(i,{businfo:this.babelExtField},e,a,window.babelsdk&&window.babelsdk.getUnifyExtField&&window.babelsdk.getUnifyExtField());var l=new window.MPing;null===(r=this.extension)||void 0===r||null===(o=r.onPvTracking)||void 0===o||o.call(r,i),t||l.send(i)}catch(u){console.error(\"", "\"https://storage.360buyimg.com/babel/activityFallbackData/\"", "\"Babel_GoodsExpo\"", "\"===e){var J=t||{},q=<PERSON>.scrollAsyncLoadEvent,z=J.taskResolve,G=<PERSON>.taskReject;(0,A.fetchBabelSingleFloor)({context:l,params:null===q||void 0===q?void 0:q.params,retryCount:2}).then((function(e){var t,n,r=e.floorData,o=e.replaceElementList,i={groupInfoList:null!==(t=null===r||void 0===r||null===(n=r.materialGroupList[0])||void 0===n?void 0:n.groupInfoList)&&void 0!==t?t:[]};\"", "\"Babel_BannerVideoExpo\"", "\"Babel_dev_expo\"", "\"Babel_M\"", "\"babel-app\"", "\"},t.hashTime33=P,t.isBabelUrl=function(e){var t;return null===(t=M.exec(e))||void 0===t?void 0:t[1]},t.jsonSafeBase64=function(t){var n=!(arguments.length>1&&void 0!==arguments[1])||arguments[1];if(!t)return t;try{JSON.parse(t)}catch(o){return t}var r=\"", "\")}));try{window.babel_pageFinishPVCallback=function(e){(0,f.xLog)(\"", "\",{value:!0}),t.default=function(){var e;return null===(e=window)||void 0===e?void 0:e.getBabelSdk({styleId:\"", "\",isWebpSupport:null===(l=w.deviceInfo)||void 0===l?void 0:l.isWebpSupport,isAvifSupport:null===(u=w.deviceInfo)||void 0===u?void 0:u.supportAvif,enc:n.identification||n.enc,channel:n.channel,pageId:null===(s=w.activityInfo)||void 0===s?void 0:s.pageId,activityId:null===(v=w.activityInfo)||void 0===v?void 0:v.activityId,activityInfo:w.activityInfo,systemReqParam:c.systemReqParam,isLogin:null===w||void 0===w||null===(h=w.getActivityData())||void 0===h?void 0:h.isLogin,siteTransResult:null===w||void 0===w||null===(y=w.getActivityData())||void 0===y?void 0:y.siteTransResult,lang:d.lang,deviceInfo:{floorWidthVw:o||100,orient:null===(b=w.app)||void 0===b||null===(_=b.screenMode)||void 0===_?void 0:_.orient,screenWidth:(null===(S=w.deviceInfo)||void 0===S?void 0:S.width)||375},embedMTab:d.embedMTab,isInMultiTab:d.embedMTab||!d.isJdApp&&(null===(I=w.activityInfo)||void 0===I?void 0:I.hasMultiTab),cornerStyle:d.cornerStyle,isDark:d.isDark&&(null===(k=w.activityInfo)||void 0===k?void 0:k.actDark)},payload:{materialData:n.materialData,providerData:n.providerData,materialParams:n.materialParams,advIds:n.advIds,godIds:n.godIds,cmpIds:n.cmpIds,props:n.boardParams},bridge:w.ipaasBridge,babelSecurity:p.default,tunnel:{onVisible:(0,f.ifloorFilter)(n),profilerModuleReport:(0,f.profilerModuleReport)(n),jsagentReport:(0,f.jsagentReport)(n),imageNotFoundReport:(0,f.imageNotFoundReport)(n),bridge:w.ipaasBridge,babelSecurity:p.default},tabContainer:g,parentContext:m};return null!==(E=window)&&void 0!==E&&E.ifloorSharedInternals&&window.ifloorSharedInternals.sharedStore.dispatch({type:\"", "\"+y.getScreenType((0,S.getDeviceInfo)().width),u.screenMode=y.getScreenMode({width:(0,S.getDeviceInfo)().width,orient:null===(o=h.tttparams)||void 0===o?void 0:o.orient}),s&&s.channelPoint&&(s.channelPoint.babelChannel=(0,w.getQueryString)(\"", "\"Babel_LBS\"", "\"babelChannel\"", "\"X-Babel-ActId\"", "\"===g)x({materialData:f,materialPromiseData:Promise.resolve(f),requestId:null===(b=window.babelFloorMap[o])||void 0===b?void 0:b.requestId});else x({requestId:null===(w=window.babelFloorMap[o])||void 0===w?void 0:w.requestId})}function x(e){var n,f=e.materialData,p=e.materialPromiseData,b=e.requestId,w=(0,u.getBabelUrlParam)({});Promise.resolve(h[r][0]({containerElement:_,env:{activityInfo:(0,l.default)((0,l.default)({},S),{isOpacityTitle:d.isJdApp&&S.isOpacityTitle}),moduleId:o,requestId:b,deviceInfo:{orient:null===(n=d.tttparams)||void 0===n?void 0:n.orient,screenWidth:window.innerWidth},enc:a.identification||a.enc,channel:a.channel,isWebpSupport:I.isWebpSupport,isHybridImgSupport:I.isHybridImgSupport,pageId:E.pageId,activityId:E.activityId,ofn:a.ofn,transParam:E.transParam,supportTran:S.isOpacityTitle,firstScreen:a.__first_screen__||!1,pageType:\"", "\"babel-notification-wrap\"", "').concat(e.babe<PERSON>,'", "\"Babel_CarouselProExpo\"", "\",17);case 15:w=(0,s.doPostCors)(h,(0,s.getBabelUrlParam)({skuList:b,transParam:g,refreshTag:d},f)).then((function(e){if(\"", "\"Babel_\"", "\"babel_\"", "\"Babel_FreeCouponBrandToastClose\"", "\"feeds-extra-close babel-pointer\"", "\"Babel_FlexCube_Close\"", "\"Babel_CommonExpo\"", "\",url:p,fullUrl:v+p,babelDebugMode:t(\"", "\"Babel_LoctipClose\"", "\"Babel_PVFinish\"", "\"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,d.default)(e);if(t){var o=(0,d.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,c.default)(this,n)}}var S=function(e){(0,s.default)(n,e);var t=_(n);function n(){var e;(0,l.default)(this,n);for(var r=arguments.length,o=new Array(r),i=0;i<r;i++)o[i]=arguments[i];return(e=t.call.apply(t,[this].concat(o)))._onClick=function(t,n){var r=e.getContext(),o=e.props,i=o.eventId,l=o.onClick,u=o.delay,s=o.eventParams,c=o.babelChannel,d=o.eventLevel,f=o.clickUrl,p=o.newEventId,m=o.newEventParams,b=o.jsonParams,w=o.composeParams,_=o.trafficMapOptions,S=s,I=b,k=m,E=t.currentTarget||t.target;if(w&&n){var x=n.moduleId,O=n.floorSrvInfo,P=r.getActivityData().pageSrvInfo,C=void 0===P?{}:P;S=(0,g.mapTrackParams)(C[x],O,s||w.jump&&w.jump.srv),I=b||w.jump&&w.jump.srvData,p&&(k=(0,g.mapTrackParams)(C[x],O,m||w.costSrv))}\"", "\"Babel_CombineCouponGuideToastOpen\"", "\"Babel_TopBack\"", "\"Babel_CombineCouponGuideToastExpo\"", "\",e.next=7,(0,<PERSON><PERSON>doPostCors)(o.context.getFrontInterfaceUrl(l),(0,R.getBabelUrlParam)(a,l));case 7:(u=e.sent)&&\"", "\"Babel_MapAreaExpo\"", "\"fc-slider-control fc-slider-control-right babel-pointer\"", "\"Babel_ifloorVirtual\"", "\"tips expo bottom_tips_wrapper babel-pointer\"", "\"),queryBabelSingleFloor:o.isPreview?\"", "\"}),l>=r&&e.stop()}),i)},stop:function(){clearInterval(u),u=null,l=0}}},t.getBabelWareInfo=function(e){return h.apply(this,arguments)},t.removeRefreshSku=function(e){if(!e||!e.length)return;var t,n=function(e,t){var n=\"", "\",r)}}}),[o,y,h]),null}s.isServer||(window.babel_trigger_anchor_action=f)},cAj5:function(e,t,n){\"", "\"===typeof r?null===(s=r)||void 0===s?void 0:s():r,b?(0,u.storeExpo)({eventId:t,srv:n,srvData:r,client_exposal_url:f,extension_id:v}):l.default.tracking({event_id:t,event_param:n,json_param:r,babelChannel:c},l.default.getTrafficMapParam((0,o.default)((0,o.default)({},_),{},{target:a,isExpo:!0}),n,I)))}},\"", "\"babel-pointer fc-showmore\"", "\"babel-v2-m-comp\"", "\"!==u.hasIdentification||!window.optionScriptAssets){e.next=11;break}return e.next=11,Promise.all(window.optionScriptAssets.map((function(e){return(0,b.loadScript)(e).catch((function(){}))})));case 11:return window.babelShareData=s,window.__ihubData__={appEnv:_,shieldResult:u.shieldResult,codeFloors:(u.floorList||[]).filter((function(e){return\"", "\"Babel_NewRightsExpo\"", "\"Babel_SlideExpo\"", "\"babelGetLottery\"", "\"Babel_WheelExpo\"", "\"Babel_LoginCallbackExpo\""], "parameters": ["\"),e.yhd_ut=window._ut;var se=(0,y.get)()&&(0,y.get)().overrideBabelColorApiBody;se&&\"", "\"dialog-body\"", "\"),window.futureBridge=window.futureBridge||new Promise((function(e){window._bridgeResolve=e})));var b=function(){if(!d.isServer){var e=(0,f.getBabelUrlParam)({})||{};return(0,u.default)((0,u.default)((0,u.default)({},e),{body:e.body?JSON.parse(e.body):{}}),{},{tttparams:d.tttparams})}return{}}();function w(e){return g[e]}function _(e){if(!e)return[];var t=[];return e.forEach((function(e){if(\"", "\"),this.props.active&&!e.active&&(document.body.style.overflow=\"", "\",{src:i.originSrc,data:i.props.data,env:null===(r=null===(n=i.context)||void 0===n?void 0:n.payload)||void 0===r?void 0:r.env})},i.loadOriginImage=function(){var e=i.originSrc;Qt(e).then((function(){i.setState({src:e})})).catch((function(){i.loadImageError()}))};var a=t.payload,l=t.xpath;return i.originSrc=i.getOriginSrc(),i.optimizedSrc=i.getOptimizedImgSrc(n),i.imgRef=null,i.disableLazyloadImg=!(l&&l[0]&&l[0].index>((null===(r=null===n||void 0===n?void 0:n.controls)||void 0===r?void 0:r.fcImagePreloadSize)||20))&&((null===a||void 0===a?void 0:a.disableLazyloadImg)||(null===(o=null===n||void 0===n?void 0:n.controls)||void 0===o?void 0:o.disableLazyloadImg)),i.state={src:i.disableLazyloadImg?i.optimizedSrc:he.DefaultImgSrc},i}return D(t,e),t.prototype.componentDidMount=function(){if(this.disableLazyloadImg&&this.imgRef){var e=this.imgRef,t=e.naturalWidth,n=e.naturalHeight;!e.complete||t||n||this.loadImageError()}},t.prototype.componentDidUpdate=function(){var e=this.getOriginSrc();e&&e!==this.originSrc&&(this.originSrc=e,this.optimizedSrc=this.getOptimizedImgSrc(),this.setState({src:this.optimizedSrc}))},t.prototype.render=function(){return this.disableLazyloadImg?this.renderImageBody():this.renderLazyload()},t.prototype.getOriginSrc=function(){var e=this.props,t=e.cfg.dataPath,n=e.data,r=e.isDark,o=r&&t.srcDarkMode||t.src,i=ae(o,n);return(o||\"", "\"),x=I.body,O=(0,s.default)(I,E);g=JSON.parse(x),y=(0,u.default)((0,u.default)({},y),O)}var C={url:\"", "\"))},t.restBodyPosition=function(){document.body.style.overflow=\"", "\"===o&&a&&(s=l,c={root:a,rootMargin:i}),u.a.createElement(tt,{once:!0,groupName:s,options:c,visibilityChange:e.imageVisibilityChange,fallback:function(){return e.imageVisibilityChange(!0)}},e.renderImageBody())}))},t.prototype.renderImageBody=function(){var e,t=this,n=this.props,r=n.cfg,o=n.posMode,i=n.data,a=r.type,l=r.draggable,s=b[a],c={draggable:l,onError:this.loadImageError};this.hasClickEvent()&&(c.onClick=this.clickEventResolver);var d={};return(null===i||void 0===i?void 0:i.longPressEvent)&&(d={attr:(e={},e[\"", "',document.body.appendChild(e);var t=e.querySelector(\"", "\",document.body.appendChild(n)}else t&&(t.style.display=\"", "\"),I=S.body,k=S.query;s.isPreview&&(I.previewTime=(0,u.getPreviewTime)(),I.pageType=\"", "\"==document.body.style.position&&(document.body.style.position=\"", "\")[1]||(document.body.style.overflow=\"", "\",[t,n]);case 7:return o=r.sign<PERSON><PERSON><PERSON>,a=r.resolveGiaAnd<PERSON>inger,l=r.riskHandlerHeaders,e.next=10,Promise.all([o(t),a,l]);case 10:s=e.sent,d=(0,c.default)(s,3),f=d[0].h5st,p=d[1],v=p.eid,h=p.jsToken,m=d[2],y=void 0===m?{}:m,t=(0,u.default)((0,u.default)({},t),{},{h5st:f,eid:v,\"", "\"===f.payload.passthrough&&!A&&(0,g.restBodyPosition)(),R(!1)}),[f,A]),D=(0,d.useCallback)((function(){var e,t;\"", "\",value:function(){this.props.active&&(document.body.style.overflow=\"", "\".concat(x).concat(_),Object.assign({body:JSON.stringify(I)},k))}};var i=r(n(\"", "\",{value:!0}),t.default=function(e,t){return function(n,r){var o=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=o.childrenActivityId,l=o.childrenActivityInfo,d=o.overrideBody,v=i||p();if(!v)throw Promise.reject(new Error(\"", "\",cpId:2===g?m.cpId:void 0,actKey:3===g?m.cpId:void 0,couponSource:m.couponSource,couponSourceDetail:m.couponSourceDetail,channel:m.channel,batchId:m.batchId,platform:K,headArea:q,couponTemplateFrom:Q}),oe),ue=JSON.parse(le.body),se=w.getRiskResult({id:\"", "\",document.body.appendChild(n));var r=t||\"", "\"),{body:JSON.stringify({transParam:r.activityInfo.transParam,floorInfoList:o}),appid:\"", "\"html, body{-webkit-overflow-scrolling: touch;overscroll-behavior-y: none;}\"", "\"body\"", "\",fieldValue:this.state.statusHeight}),this.state.floorList.length!==n.floorList.length&&this.fillAppNode(),this.state.floorList.length>=(null===(r=this.props.data.activityData.floorList)||void 0===r?void 0:r.length)&&this.bindPaginationScroll(),null!==(o=this.props.data)&&void 0!==o&&o.activityData&&null!==(i=t.data)&&void 0!==i&&i.activityData&&(null===(l=t.data)||void 0===l?void 0:l.activityData)!==(null===(u=this.props.data)||void 0===u?void 0:u.activityData)&&(R.default.setInitialFields({page_name:(0,U.getLocationHref)(),page_param:this.props.data.activityData.pageId,channelPoint:JSON.stringify(this.props.data.activityData.channelPoint)}),this._currentPageIndex=0,this.setState({floorList:this.props.data.activityData.floorList||[]},(function(){v._fetchPageIfNeedAfterInitial()}))),this.state.isDark!==n.isDark&&(f=oe.getPageBackgroundImage({isDark:this.state.isDark},this.props.data.activityInfo,this.props.data.activityData),(p=oe.getPageBackgroundColor({isDark:this.state.isDark},this.props.data.activityInfo,this.props.data.activityData))&&(document.body.style.backgroundColor=p),f&&f&&(document.body.style.backgroundImage=\"", "\")||667;return{body:ye,query:(0,o.default)({screen:\"", "\"):[],ge=(0,c.default)(he,2),me=ge[0],ye=ge[1];e.homeLng=me,e.homeLat=ye,e.xLbsActivity||(e.geo.lng=me,e.geo.lat=ye)}}}e.gLng1=U,e.gLat1=B,e.head_area=X,e.receiverLng=G,e.receiverLat=q,e.fullUrl=window.location.href;var be={body:e=JSON.stringify(e),screen:r,client:\"", "\"!==e.message&&(0,U.xLog)(e)}))}},p.obsererPageResize=function(){var e=1.2*A.getDeviceInfo().height,t=p.getAppElement();if(window.ResizeObserver){var n=new ResizeObserver((function(t){var n,r,o,i,a,l=(null===(n=t[0])||void 0===n||null===(r=n.contentBoxSize)||void 0===r||null===(o=r[0])||void 0===o?void 0:o.blockSize)||(null===(i=t[0])||void 0===i||null===(a=i.contentRect)||void 0===a?void 0:a.height);l&&l<e&&p._fetchPageIfNeedAfterInitial()}));return n.observe(t),function(){n.unobserve(t)}}var r=setInterval((function(){t.offsetHeight<e&&p._fetchPageIfNeedAfterInitial()}),500);return function(){clearInterval(r)}},p._paginationScrollListener=function(){p.clearResizeObserver&&(p.clearResizeObserver(),p.clearResizeObserver=null);var e=window.scrollTop||window.pageYOffset,t=window.innerHeight,n=document.body.scrollHeight,r=j.npRate||1,o=p.props.data.app.pageLoadSize;n-t-e<=(!p.props.clientPagination&&o>0?o:r*t)&&(p.props.clientPagination?!p.state.showLoading&&p.fetchNextClientPaginationFloors():p._loadNextPage().catch((function(e){\"", "\",1,!1)),u.isStatic&&(c=c.replace(/h5static.m.jd.com\\/(next\\/)?/,\"", "\",body:t,headers:{\"", "\"},body:\"", "\",document.body.appendChild(t);var n=\"", "\"https://h5static.m.jd.com/\"", "\"!==document.body.style.position&&(r=window.pageYOffset||document.documentElement.scrollTop,document.body.style.position=\"", "\"),x=y.context.activityInfo,O=Object.values(b),C=[],T=0;T<O.length;T+=m)A=O.slice(T,T+m),R=A.map((function(e){return e.args})).join(\"", "\"===typeof e?e():e,c.default.findDOMNode(e)||t}(e,document.body)}}]),n}(s.default.PureComponent);t.default=p,p.propTypes={children:d.default.node.isRequired,container:d.default.oneOfType([d.default.object,d.default.func]),disablePortal:d.default.bool,onRendered:d.default.func},p.defaultProps={disablePortal:!1}},\"", "\",functionId:v,body:g,headerType:\"", "\",body:o,credentials:\"", "\",document.body.style.overflow=\"", "\");document.body.appendChild(u),i=(0,E.hideSystemNav)(),m.default.render(y.default.createElement(g.<PERSON><PERSON>,{size:\"", "\",document.body.appendChild(y)),(w=document.createElement(\"", "\";var S=f({activityId:v,transParam:b,clientInfo:r,overrideBody:d},y,n,decodeURI((0,a.getLocationHref)()),\"", "\"])){var b=d.default.parse((null===p||void 0===p?void 0:p.body)||\"", "\"),N=JSON.parse(L.body),D=v.getRiskResult({id:\"", "\")||(null===(e=window.eruda)||void 0===e?void 0:e._isInit))&&(t=window.console).log.apply(t,arguments)}function D(){var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.href)||window.location.href}var M=/\\/\\/(?:prodev|beta-babelser|babelserv|h5static|pro|[a-z]+-pro)(?:\\.m)?(?:\\.jd\\.com|\\.jdtest\\.net|\\.jd\\.local)\\/(?:mall|mini|hm|(?:pages|service)\\/preview\\/(?:mall|mini|hm))\\/active\\/([a-zA-Z0-9]+)\\/(?:index\\.html|[0-9]+)/;function j(){var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.search)||window.location.search}t.triggerLoadCheck=function(){var e=document.createEvent(\"", "\",{decodeURIComponent:decodeURIComponent}),w=b.body,_=(0,s.default)(b,k);g=JSON.parse(w),y=(0,u.default)((0,u.default)({},y),_)}if(\"", "\",index:t}])})})),g=h;if(r===I.BANNERVERTICAL){for(var m=o.columns,y=void 0===m?1:m,b=[],w=Math.floor(h.length/y),_=0;_<w;_++)b.push(u.a.createElement(To,{key:_},h.slice(_*y,(_+1)*y)));g=b}if(r===I.BANNER){var S=o.columns,k=(y=void 0===S?1:S,o.rows),E=void 0===k?1:k;if(y>1||E>1){b=[];var x=E*y,O=Math.floor(h.length/x);for(_=0;_<O;_++)b.push(u.a.createElement(Ao,{key:_},h.slice(_*x,(_+1)*x)));g=b}}return g.slice(0,Co)},t.onSwiperMove=function(e){t.setState({activeIndex:e});var n=t.props.bannerType,r=t.props.floorConfig.bannerConfig,o=void 0===r?Ro:r,i=n===I.BANNERFOCUS,a=o.autoPlay;i?setTimeout((function(){t.handleFocusBannerVideos()}),A):a===p&&setTimeout((function(){xo()}),A)},t}return D(t,e),t.prototype.componentDidMount=function(){this.handleFocusBannerVideos()},t.prototype.render=function(){var e,t,n=this.props.materialGroupList,r=(null!==(t=null===(e=(void 0===n?[]:n)[0])||void 0===e?void 0:e.groupInfoList)&&void 0!==t?t:[]).slice(0,100);return r.length<=0?null:this.renderSwiperBody(r)},t.prototype.renderSwiperBody=function(e){var t,n,r,o,i,a,l,s,c=this.context,d=c.styleUtil,f=c.controls,h=this.props,g=h.bannerType,m=h.floorConfig,y=h.canvasConfig,b=h.disabled,w=void 0!==b&&b,_=m.bannerConfig,S=void 0===_?Ro:_,k=m.rows,E=void 0===k?1:k,x=S.interval,O=S.selectedColor,P=S.unselectedColor,C=void 0===P?\"", "\"),o.noticeIframe(),o.showBottomFloatNav=(0,m.hideSystemNav)())},o.closeWindow=function(){o.setState({active:!1},(function(){var e;document.body.style.overflow=\"", "\",bodyExtraData:function(){},settings:i.default,apiCDNKey:\"", "\",onClick:function(){var e;r.onClick&&r.onClick(),a&&a(),m.default.unmountComponentAtNode(u),document.body.removeChild(u),null===(e=i)||void 0===e||e.then((function(e){e()}))}},(0,c.default)((0,c.default)({},e),{},{onClick:function(){var t;(e.onClick(),o.needClose)&&(m.default.unmountComponentAtNode(u),document.body.removeChild(u),null===(t=i)||void 0===t||t.then((function(e){e()})))}})],expo:o.tracking},y.default.createElement(\"", "\",args:R,from:y.props.floorData&&y.props.floorData.cpChannelKey},n),{},{pageClick:\"", "\",ue.random=fe,Object.assign(ue,G),le.body=JSON.stringify(ue),e.abrupt(\"", "\",N.random=B,Object.assign(N,s),L.body=JSON.stringify(N),<PERSON>.push((0,c.doPostCors)(E,L).catch((function(e){return{fetchError:!0,error:e}})));return e.next=12,Promise.all(C);case 12:if(F=e.sent,g&&g(!1),!F.every((function(e){return e&&e.fetchError}))){e.next=19;break}return P(),null===(U=window.jmfe)||void 0===U||null===(W=U.jsagentReport)||void 0===W||W.call(U,4,751,\"", "\"body{-moz-text-size-adjust:none !important;-webkit-text-size-adjust:none !important;text-size-adjust:none !important}\"", "\"])){var I=JSON.parse((null===p||void 0===p?void 0:p.body)||\"", "\");case 10:return o=Object.assign(window.babelShareData,g.getBasicActivityInfo(r,{getFromQuery:v.getQueryString,env:_})),t.next=13,(0,S.preLoadAssets)(r);case 13:document.body.style.backgroundColor=g.getPageBackgroundColor(_,o,r),document.title=g.getPageTitle(L,o,r),l=document.getElementById(\"", "\"),e.next=21,r();case 21:c=e.sent,f=c.channelFromSdk,p=c.sealedSdk,q((function(e){return(0,a.default)((0,a.default)({},e),{channel:Object.assign({},e.channel,f),className:(0,d.default)({mini:p.env.isMiniUrl},p.env.universalSiteName)})}));case 25:return e.next=27,(0,S.preLoadAssets)(u);case 27:W(!0),document.body.style.backgroundColor=g.getPageBackgroundColor(_,s,u),u.head&&u.head.backgroundImage&&(document.body.style.backgroundImage=\"", "\",isLogin:k.isLogin,siteTransResult:k.siteTransResult,systemReqParam:(0,l.default)((0,l.default)({},w),{body:JSON.parse(w.body)}),embedMTab:d.embedMTab,isInMultiTab:d.embedMTab||!d.isJdApp&&S.hasMultiTab,cornerStyle:d.cornerStyle,isDark:d.isDark},payload:(0,l.default)({materialPromiseData:p,materialData:f,materialParams:a.materialParams,providerData:a.providerData,advIds:a.advIds,godIds:a.godIds,cmpIds:a.cmpIds,props:a.boardParams},k.shieldResult?{shieldResult:k.shieldResult}:{}),bridge:t,babelSecurity:s.default,tunnel:{onVisible:(0,c.ifloorFilter)(a),profilerModuleReport:(0,c.profilerModuleReport)(a),jsagentReport:(0,c.jsagentReport)(a),imageNotFoundReport:(0,c.imageNotFoundReport)(a),bridge:t,babelSecurity:s.default}})).then((function(){v(y),window.performance&&window.performance.mark&&(window.__Ipass_Perf__=window.__Ipass_Perf__||[]).push({id:i,materialQryType:g,perf:{t0:window.performance.getEntriesByName(\"", "\",{value:!0}),t.fixBody=function(){\"", "\"===window.getComputedStyle(t).fontSize;document.body.removeChild(t),e(n)})):Promise.resolve(!0))}var on,an=39,ln=21,un=/^\\d+(\\.\\d+)?$/,sn=[\"", "\",topActivityEncodeId:b.activityEncodedId},t.next=7,e.props.fetchActivityData(null,null,{childrenActivityId:n,overrideBody:a});case 7:return(r=t.sent).floorList.forEach((function(e,t){e.floorNum=\"", "\"//h5static.m.jd.com/\"", "\",args:m.args,from:null===(d=P.props)||void 0===d||null===(v=d.floorData)||void 0===v?void 0:v.cp<PERSON><PERSON><PERSON><PERSON>},n),ae),{},{pageClick:\"", "\"!=typeof WeakMap)return null;var t=new WeakMap,n=new WeakMap;return(g=function(e){return e?n:t})(e)}var m=10;function y(){return y=(0,s.default)(i.default.mark((function e(t,n,r,o){var s,g,y,b,w,_,S,I,k,E,x,O,P,C,T,A,R,L,N,D,M,j,B,F,U,W,H,V,J,q,z,G,Y,X=arguments;return i.default.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:for(J=function(e,t,n){if(e)for(var r=0,o=Object.entries(n);r<o.length;r++){var i,a,u=(0,l.default)(o[r],2),s=u[0],c=u[1],f=null===c||void 0===c||null===(i=c.args)||void 0===i||null===(a=i.split(\"", "\"===typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(<PERSON><PERSON><PERSON>,[],(function(){}))),!0}catch(e){return!1}}();return function(){var n,r=(0,s.default)(e);if(t){var o=(0,s.default)(this).constructor;n=Reflect.construct(r,arguments,o)}else n=r.apply(this,arguments);return(0,u.default)(this,n)}}var w=function(e){(0,l.default)(n,e);var t=b(n);function n(e,r){var o;return(0,i.default)(this,n),(o=t.call(this,e,r)).iframeRef=(0,d.createRef)(),o.setOverFlowHidden=function(){o.state.active&&o.state.isIframeLoad&&(document.body.style.overflow=\"", "\",document.body.style.top=\"", "\"bodyMaps\"", "\"}),e.props.onClick&&e.props.onClick()},e.handleScroll=function(){e.scrollTimer&&clearTimeout(e.scrollTimer);var t=e.props.visibilityHeight,n=window.pageYOffset||document.body.scrollTop;e.setState({active:n>t})},e.state={active:!1},e.scrollTimer=null,e}return(0,u.default)(n,[{key:\"", "\"),r=document.body.getAttribute(\"", "\");document.body.appendChild(r);v.default.render(p.default.createElement(n,(0,a.default)({ref:function(e){t({notice:function(t){e.addNotice(t)}})},disablePortal:!0},e)),r)}}]),n}(p.PureComponent);t.default=S,S.propTypes={notices:h.default.array,disablePortal:h.default.bool,maxCount:h.default.number},S.defaultProps={disablePortal:!1,maxCount:10}},\"", "\"===f.payload.passthrough&&!A&&(0,g.fixBody)(),null===(e=(t=f.payload).onSuccess)||void 0===e||e.call(t)}),[f,A]);return(0,d.useEffect)((function(){if(f&&!r&&S)return window.addEventListener(\"", "\",{body:JSON.stringify({serial:(0,U.getQueryString)(\"", "\",{value:!0}),t.getIndexRequestParamFunc=function(e,t){return function(n,r,f,p,v,h){var g=n.activityId,m=n.transParam,y=n.isMain,b=n.floorCount,w=n.overrideBody,_=n.clientInfo,S=function(e,t,n){var r={getPosLngLat:function(){var t=e(\"", "\"encodedBodySize\"", "\",onOverlayClick:function(){var e;a&&a(),m.default.unmountComponentAtNode(u),document.body.removeChild(u),null===(e=i)||void 0===e||e.then((function(e){e()}))},actions:[{label:r.cancelText||(null===l||void 0===l?void 0:l.t(\"", "\":return e.stop()}}),e)})))).apply(this,arguments)}c.isServer||(I=(0,v.getSafeBottom)());var T=function(){var e=(0,l.useContext)(u.GlobalContext),t=e.safeAreaInsetBottom>0?e.safeAreaInsetBottom+0:I,n=(0,l.useCallback)((function(){var e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:0;document.body.style.paddingBottom=\""], "endpoints": ["\"newBabelAwardCollection,receiveXFCoupon,batchReceiveXFCoupon\"", "\"newBabelAwardCollection\"", "\"babelGetGuideTips\"", "\"baNewBabelAwardCollection\""]}, "critical_code_blocks": [{"type": "pattern_2", "content": "return e.args})).join(\";\"),L=(0,c.getBabelUrlParam)((0,u.default)((0,u.default)({activityId:x.encodeActivityId,gridInfo:x.gridInfo,scene:\"1\",args:R,from:y.props.floorData&&y.props.floorData.cpChannelKey}", "length": 203}, {"type": "pattern_3", "content": "var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.href)||window.location.href}var M=/\\/\\/(?:prodev|beta-babelser|babelserv|h5static|pro|[a-z]+-pro)(?:\\.m)?(?:\\.jd\\.com|\\.jdtest\\.net|\\.jd\\.local)\\/(?:mall|mini|hm|(?:pages|service)\\/preview\\/(?:mall|mini|hm))\\/active\\/([a-zA-Z0-9]+)\\/(?:index\\.html|[0-9]+)/;function j(){var e;return(null===(e=window.xrender_location)||void 0===e?void 0:e.search)||window.location.search}", "length": 440}, {"type": "pattern_4", "content": "pageID:\"babel\"}}))),window.webkit&&(null===(a=window.webkit.messageHandlers)||void 0===a||null===(l=a.JDAppUnite)||void 0===l||null===(u=l.postMessage)||void 0===u||u.call(l,{method:\"callSyncRouterModuleWithParams\",params:JSON.stringify({routerURL:\"router://JDUIUserManagerModule/needPopUpLoginAnimation\",routerParam:{source:\"babel\"}", "length": 333}, {"type": "pattern_4", "content": "method:\"POST\",headers:s,mode:\"cors\",body:o,credentials:\"include\"}).then(U(t)).then(W(t)));case 46:case\"end\":return e.stop()}}),e)}))),F.apply(this,arguments)}function U(e){return function(t){var n=t.status,r=t.headers.get(\"x-api-request-id\");return t.text().then((function(o){try{t=JSON.parse(o)}", "length": 296}, {"type": "pattern_4", "content": "spacing:Number(t.config.spacing)},t.config.postText))})))},ia[a.IMAGE]=function(e){var t=Object(l.useContext)(ze).controls,n=e.cfg,r=e.data;return ae(n.dataPath.src,r)?u.a.createElement(en,M({isDark:t.isDark}", "length": 208}, {"type": "pattern_4", "content": "var o,i,a,l,u,s,c,d,p;if(f.isWkWebview)return null===(o=window)||void 0===o||null===(i=o.webkit)||void 0===i||null===(a=i.messageHandlers)||void 0===a||null===(l=a.JDAppUnite)||void 0===l||null===(u=l.postMessage)||void 0===u?void 0:u.call(l,{method:\"checkReminder\",params:JSON.stringify({businessType:e.type,beginTime:e.beginTime,uniqueId:e.uniqueId,callBackName:e.callBackName,callBackId:e.callBackId}", "length": 403}, {"type": "pattern_4", "content": "var t,n,r,o;null===(t=window.webkit)||void 0===t||null===(n=t.messageHandlers)||void 0===n||null===(r=n.JDAppUnite)||void 0===r||null===(o=r.postMessage)||void 0===o||o.call(r,{method:\"addReminder\",params:JSON.stringify({businessType:e.businessType,showTag:e.showTag||\"\",remindTitle:e.remindTitle||\"\",beginTime:e.beginTime,uniqueId:e.uniqueId,jumpStr:JSON.stringify(e.jumpStr)||\"\",imgUrl:e.imgUrl||\"\",notifyTime:e.notifyTime||\"\",extraStr1:e.extra1||\"\",extraStr2:e.extra2||\"\",callBackName:e.callBackName,callBackId:e.callBackId}", "length": 527}, {"type": "pattern_4", "content": "type:\"clear_js\"}}),callBackName:null,callBackId:null})),!f.embedMTab&&window.webkit&&(null===(a=window.webkit.messageHandlers)||void 0===a||null===(l=a.MobileNavi)||void 0===l||null===(u=l.postMessage)||void 0===u||u.call(l,{method:\"configBtnSince610\",params:JSON.stringify(g),callBackName:\"babel_configBtnSince610\",callBackId:null}", "length": 332}, {"type": "pattern_4", "content": "var u,s,c,d;null===(u=window.webkit)||void 0===u||null===(s=u.messageHandlers)||void 0===s||null===(c=s.JDAppUnite)||void 0===c||null===(d=c.postMessage)||void 0===d||d.call(c,{method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/enableWebControlBack\",routerParam:{businessType:\"enableWebControlBack\",controlType:\"2\"}", "length": 352}, {"type": "pattern_4", "content": "var n,r,o,i;null===(n=window.webkit)||void 0===n||null===(r=n.messageHandlers)||void 0===r||null===(o=r.JDAppUnite)||void 0===o||null===(i=o.postMessage)||void 0===i||i.call(o,{method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/enableWebControlBack\",routerParam:{businessType:\"goBack\"}", "length": 322}, {"type": "pattern_4", "content": "}).log||\"-1\",ue.random=fe,Object.assign(ue,G),le.body=JSON.stringify(ue),e.abrupt(\"return\",(0,f.doPostCors)($,le).then((function(e){if(\"0\"===e.code){if(\"authCode\"in e&&\"0\"!==e.authCode){var t,i,a,l=null===(t=(0,S.get)())||void 0===t||null===(i=t.listeners)||void 0===i||null===(a=i.onUserCheck)||void 0===a?void 0:a.call(i,e.authCode);return F&&F(!1),void(!l&&h.default.warn(e.authMsg||(null===Z||void 0===Z?void 0:Z.t(\"collect_later\"))||\"\\u6b64\\u65f6\\u6392\\u961f\\u9886\\u5238\\u7684\\u4eba\\u592a\\u591a,\\u4f11\\u606f\\u4e00\\u4f1a\\u513f\\u518d\\u8bd5\\u8bd5\\u5427\"))}", "length": 558}, {"type": "pattern_4", "content": "tipsBusinessId:n,activityId:o,plusExpoTimes:a.plusExpoTimes,plusClickTimes:a.plusClickTimes,plusCloseTimes:a.plusCloseTimes,channel:i},u=\"babelGetGuideTips\";(0,h.doPostCors)(this.context.getFrontInterfaceUrl(u),(0,h.getBabelUrlParam)(l,u)).then((function(t){var n=t.code,r=t.subCode,o=t.isShow,i=t.tipsInfo;\"0\"===n&&\"0\"===r&&\"1\"===o&&setTimeout((function(){e.setState({tipsInfo:i,showTips:!0,tipsPosition:e.props.calcPosition()}", "length": 428}, {"type": "pattern_4", "content": "},e.params);r.subChannel=2;var u=(0,o.getBabelUrlParam)(r,\"priceReduceNotify\");(0,o.doPostCors)(n,u).then((function(n){3===n.code?(0,i.default)((0,l.getLocationHref)(),t):0===n.code?e.successFun&&e.successFun(n.msg):e.errorFun&&e.errorFun(n.msg)}", "length": 246}, {"type": "pattern_4", "content": "window.performance.mark(\"perf:firstscreenend\");var e=(window.navigator.userAgent||\"\").toLowerCase();if(0!==e.indexOf(\"jdapp\")||(window.location.search||\"\").indexOf(\"xrender=2\")>-1)return;var t,n,r,o=String(+new Date);if(-1!==e.indexOf(\"iphone\")||-1!==e.indexOf(\"ipad\"))window.webkit&&(null===(t=window.webkit.messageHandlers)||void 0===t||null===(n=t.JDAppUnite)||void 0===n||null===(r=n.postMessage)||void 0===r||r.call(n,{method:\"notifyMessageToNative\",params:JSON.stringify({businessType:\"transferRenderTime\",params:{renderTime:o}", "length": 533}, {"type": "pattern_4", "content": "var e=(window.navigator.userAgent||\"\").toLowerCase();if(0!==e.indexOf(\"jdapp\"))return;var n=String(+new Date);-1!==e.indexOf(\"iphone\")||-1!==e.indexOf(\"ipad\")||(window.location.search||\"\").indexOf(\"xrender=2\")>-1?window.webkit&&window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"notifyMessageToNative\",params:JSON.stringify({businessType:\"transferRenderTime\",params:{renderTime:n}", "length": 391}, {"type": "pattern_4", "content": "return e.classList&&e.classList.value.includes(\"slide_ios\")}))&&(null===(t=(n=window.webkit.messageHandlers.MobileNavi).postMessage)||void 0===t||t.call(n,{method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/processH5SlideState\",routerParam:{slideDirection:\"horizontal\",state:\"start\"}", "length": 320}, {"type": "pattern_4", "content": "return e.classList&&e.classList.value.includes(\"slide_ios\")}))&&(null===(t=(n=window.webkit.messageHandlers.MobileNavi).postMessage)||void 0===t||t.call(n,{method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/processH5SlideState\",routerParam:{slideDirection:\"horizontal\",state:\"end\"}", "length": 318}, {"type": "pattern_4", "content": "statusHeight:e})})),(0,U.triggerPromptFilterMsgToCMS)(i.filterFloorList),\"cyber\"===(0,U.getQueryString)(\"from\")&&(0,F.doPostCors)(\"https://api.m.jd.com/api?appid=cyber&functionId=getFloorInfo\",{body:JSON.stringify({serial:(0,U.getQueryString)(\"serial\"),floorInfo:JSON.stringify(i.filterFloorList)}", "length": 297}, {"type": "pattern_4", "content": "e.next=12;break}return e.prev=2,r=this.context.getFrontInterfaceUrl,e.next=6,(0,v.doPostCors)(r(\"queryPagePopWindow\"),(0,v.getBabelUrlParam)({stayWindow:\"1\",activityId:null===n||void 0===n?void 0:n.encodeActivityId,transParam:this.context.activityInfo.transParam}", "length": 263}], "summary": {"args_patterns_count": 342, "api_patterns_count": 62, "parameter_types_count": 9, "function_count": 0, "constant_categories": 4, "critical_blocks_count": 19}}, {"url": "https://storage11.360buyimg.com/tower/babelnode/babellib-ab1c1b95fb.js", "file_size": 327441, "args_generation_logic": ["Args=function(e){var t", "Args: \"+n.join(\"", "Error(\"`lowPriorityWarningWithoutStack(condition, format, ...args)` requires a warning message argument\")", "Error(\"An error occurred. See https://git.io/JUIaE#\"+e+\" for more information.\"+(0<n.length?\" Args: \"+n.join(\", \"):\"\"))", "Key:function(e", "key===t)return n", "key:\"values\"===t?n.value:[n.key", "key:function(){return\"\"", "key===e.key?\"\":(\"\"+e.key).replace(Se", "key===o?n.type===Vo?c(e", "key===o?u(e", "key===i){if(7===o.tag?n.type===Vo:o.elementType===n.ty", "key===o){if(4===t.tag&&t.stateNode.containerInfo===n.c", "key:null==o?null:\"\"+o", "Key:\"Unidentified\"", "key:function(e){if(e.key){var t=Ei[e.key]||e.key", "key:\"componentDidCatch\"", "key:\"render\"", "Key:\"babel\"", "key:\"reduce\"", "key:\"add\"", "key:\"remove\"", "Key=function(){return i(this.props)", "key:\"sc-0-0\""], "api_call_logic": ["\"https://api.m.jd.com/client.action?functionId=isvObfuscator\"", "\"//api.m.jd.com/client.action?functionId=getBabelAdvertInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=getBabelProductInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelActivityLuckDraw&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelRedOpenRed&client=wh5&clientVersion=1.0.0\"", "\"api.m.jd.com\"", "\"https://api.m.jd.com/client.action?functionId=isvObfuscator\"", "\"//api.m.jd.com/client.action?functionId=getBabelAdvertInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=getBabelProductInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelActivityLuckDraw&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelRedOpenRed&client=wh5&clientVersion=1.0.0\"", "body:JSON.stringify({url:r,id:\"babelh5\"})", "body=JSON.stringify(e.body)", "postMessage(k(e)", "postMessage)&&!c.importScripts&&a&&\"file:\"!==a.protocol&&!p(i)?(u=i", "postMessage(void 0))):g=!1", "postMessage(void 0):g||(g=!0", "poster|preload|profile|radioGroup|readOnly|referrerPolicy|rel|required|reversed|role|rows|rowSpan|sandbo"], "parameter_generation": {"activityId": ["activityId=i)", "activityId:t.aid", "activityId:t.aid", "activityId:t.aid"], "from": ["from:o", "from:n(267)", "from:function(e){var t", "from:\"search\""], "client": ["client:\"m\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client=wh5&clientVersion=1.0.0\"", "client:\"wh5\""], "clientVersion": ["clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion=1.0.0\"", "clientVersion:\"1.0.0\""], "appid": ["appid:\"babelh5\"", "appid:\"babelh5\""]}, "function_definitions": [], "string_constants": {"urls": ["\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\"", "\"https://api.m.jd.com/client.action?functionId=isvObfuscator\"", "\"//api.m.jd.com/client.action?functionId=getBabelProductInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=getBabelAdvertInfo&client=wh5&clientVersion=1.0.0\"", "\"//api.m.jd.com/client.action?functionId=babelRedOpenRed&client=wh5&clientVersion=1.0.0\"", "\"api.m.jd.com\"", "\"//api.m.jd.com/client.action?functionId=babelActivityLuckDraw&client=wh5&clientVersion=1.0.0\""], "function_ids": ["\")).src=e,f.parentNode.insertBefore(a,f)})):Promise.reject()}function f(e,t,n,r){var o=window.getBabelSdk&&window.getBabelSdk({styleId:\"", "\"+JSON.stringify({sid:t.sid,activityId:t.aid,pageId:t.pid,moduleId:t.mid})},n,e)},babelRedOpenRed:function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error;return l(i(\"", "\";var t=/[&?]env=beta/.test(location.href),n=/[&?]env=test/.test(location.href),r={getBabelAdvertInfo:\"", "\"getBabelProductInfoNew\"", "\"babelh5\"", "\"window?.getBabelSdk()返回空\"", "\"babelapi0\"", "\"SSR requires `@loadable/babel-plugin`, please install it\"", "\"),m[t]||(m[t]=(0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({http:(0,u.default)(e),jump:(0,c.default)(),hooks:(0,p.getHooks)(e)},(0,s.default)(e)),(0,f.default)()),(0,l.default)()),(0,d.default)()),i),(0,h.default)())),m[t]):null},window.globalBabelSdk=window.getBabelSdk({styleId:\"", "\")}return{babelAdvertInfo:s(function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error,r=i(\"", "\"!=typeof document)?r:window.babelSecurity=window.babelSecurity||Promise.race([new Promise(function(e){window.babelSecurityResolver=e}),new Promise(function(e){setTimeout(e,1e4)})]);t.default=r},function(e,t,n){\"", "\",babelActivityLuckDraw:\"", "\",r),n,e)},babelAwardCollection:function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error;return l(i(\"", "\",babelRedOpenRed:\"", "\",babelAwardCollection:\"", "\",getBabelProductInfoNew:\"", "\"https://storage11.360buyimg.com/tower/babelnode/lottie.min.js\"", "\",!1),n,e)},babelProductInfo:s(function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error,r=arguments.length<=1||void 0===arguments[1]||arguments[1],o=i(\"", "\"},e),n&&(e.loginType=n),window.babelShareData&&(i=(n=window.babelShareData).activityId,n=n.pageId,i&&(e.body.activityId=i),n)&&(e.body.pageId=n),e.body.qryParam=(o=t,i=r,JSON.stringify(i.split(\"", "\",getBabelAdvertInfoNew:\"", "\"getBabelAdvertInfoNew\"", "\",getBabelProductInfo:\"", "\"+JSON.stringify({sid:t.sid,activityId:t.aid,pageId:t.pid,moduleId:t.mid})},n,e)},babelActivityLuckDraw:function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error;return l(i(\"", "\")}else window.babelPageJump(e);else e.match(a)?window.jmfe.callMp(e,r,o):function(e,t){var t=1<arguments.length&&void 0!==t?t:{},n=t.extraOpenAppParams,t=t.extra;e?(e=window.jmfe.completeProtocol(e),v({openAppParams:(0,m.default)({action:\"", "\"通天塔内置的商品组接口[babelProductInfo]已废弃，请使用新接口:[babelProductInfoNew], 如不能满足，请使用复合素材接口!\"", "\"):n?window.babelPageJump(window.jmfe.completeProtocol(n),e):console.log(\"", "\"x-babel-ihub\"", "\"通天塔内置的广告组接口[babelAdvertInfo]已废弃，请使用新接口:[babelAdvertInfoNew], 如不能满足，请使用复合素材接口!\"", "\"babel\"", "\"!=typeof WeakMap?null:(t=new WeakMap,n=new WeakMap,(y=function(e){return e?n:t})(e))}var m={};window.getBabelSdk=function(e){var t;return e&&e.styleId?(t=\"", "\"==typeof define&&define.amd?define(t):e.babel=t()}(this,function(){\"", "\"):window.babelPageJump(window.jmfe.completeProtocol(e),t)}else console.log(\""], "parameters": ["\"!=typeof e?String(e):e}function p(t){var e={next:function(){var e=t.shift();return{done:void 0===e,value:e}}};return n&&(e[Symbol.iterator]=function(){return e}),e}function h(t){this.map={},t instanceof h?t.forEach(function(e,t){this.append(t,e)},this):Array.isArray(t)?t.forEach(function(e){this.append(e[0],e[1])},this):t&&Object.getOwnPropertyNames(t).forEach(function(e){this.append(e,t[e])},this)}function y(e){if(e.bodyUsed)return Promise.reject(new TypeError(\"", "\"],w.prototype.clone=function(){return new w(this,{body:this._bodyInit})},b.call(w.prototype),b.call(S.prototype),S.prototype.clone=function(){return new S(this._bodyInit,{status:this.status,statusText:this.statusText,headers:new h(this.headers),url:this.url})},S.error=function(){var e=new S(null,{status:0,statusText:\"", "\"),t=p(t);return l(r,{body:\"", "\"),t.headers.forEach(function(e,t){o.setRequestHeader(t,e)}),o.send(void 0===t._bodyInit?null:t._bodyInit)})},e.fetch.polyfill=!0)}(\"", "\"}).then(function(){return window.bodymovin})}},function(e,t,n){n=n(284)();e.exports=n;try{regeneratorRuntime=n}catch(e){\"", "\"==typeof(t=a.formatters[t]))&&(e=t.call(n,r[o]),r.splice(o,1),o--),e}),a.formatArgs.call(n,r),(i.log||a.log||console.log.bind(console)).apply(n,r)}}return i.namespace=e,i.enabled=a.enabled(e),i.useColors=a.useColors(),i.color=function(e){var t,n=0;for(t in e)n=(n<<5)-n+e.charCodeAt(t),n|=0;return a.colors[Math.abs(n)%a.colors.length]}(e),\"", "\"),babelProductInfoNew:function(e){var t=e.body,n=e.success,e=e.error,r=arguments.length<=1||void 0===arguments[1]||arguments[1];return f(i(\"", "\");this._bodyArrayBuffer=g(e)}else this._bodyText=\"", "\"`lowPriorityWarningWithoutStack(condition, format, ...args)` requires a warning message argument\"", "\"))},a&&(this.blob=function(){var e=y(this);if(e)return e;if(this._bodyBlob)return Promise.resolve(this._bodyBlob);if(this._bodyArrayBuffer)return Promise.resolve(new Blob([this._bodyArrayBuffer]));if(this._bodyFormData)throw new Error(\"", "\"body\"", "\"]),e=c({body:{applyKey:\"", "\"),{body:\"", "\"!=c.overflow&&(u=m(o)),u&&(s=u,l=r,d=f=u=c=void 0,c=Math.max(s.top,l.top),u=Math.min(s.bottom,l.bottom),f=Math.max(s.left,l.left),s=Math.min(s.right,l.right),d=u-c,r=0<=(l=s-f)&&0<=d?{top:c,bottom:u,left:f,right:s,width:l,height:d}:null),!r)break;o=o&&g(o)}return r}var s,l,f,d},e.prototype._getRootRect=function(){var e,t;return t=this.root&&!c(this.root)?m(this.root):(t=(e=c(this.root)?this.root:p).documentElement,e=e.body,{top:0,left:0,right:t.clientWidth||e.clientWidth,width:t.clientWidth||e.clientWidth,bottom:t.clientHeight||e.clientHeight,height:t.clientHeight||e.clientHeight}),this._expandRectByRootMargin(t)},e.prototype._expandRectByRootMargin=function(n){var e=this._rootMarginValues.map(function(e,t){return\"", "\"),t=p(t);return d&&r&&(t.topInfo=d),l(o,{body:\"", "\");return Promise.resolve(new Blob([this._bodyText]))},this.arrayBuffer=function(){return this._bodyArrayBuffer?y(this)||Promise.resolve(this._bodyArrayBuffer):this.blob().then(v)}),this.text=function(){var e,t,n=y(this);if(n)return n;if(this._bodyBlob)return n=this._bodyBlob,e=new FileReader,t=m(e),e.readAsText(n),t;if(this._bodyArrayBuffer)return Promise.resolve(function(e){for(var t=new Uint8Array(e),n=new Array(t.length),r=0;r<t.length;r++)n[r]=String.fromCharCode(t[r]);return n.join(\"", "\",body:JSON.stringify({url:r,id:\"", "\");this.url=e.url,this.credentials=e.credentials,t.headers||(this.headers=new h(e.headers)),this.method=e.method,this.mode=e.mode,r||null==e._bodyInit||(r=e._bodyInit,e.bodyUsed=!0)}else this.url=String(e);if(this.credentials=t.credentials||this.credentials||\"", "\"tbody\"", "\"},e),n&&(e.loginType=n),window.babelShareData&&(i=(n=window.babelShareData).activityId,n=n.pageId,i&&(e.body.activityId=i),n)&&(e.body.pageId=n),e.body.qryParam=(o=t,i=r,JSON.stringify(i.split(\"", "\"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},o.formatArgs=function(e){var t,n,r=this.useColors;e[0]=(r?\"", "\"));e.bodyUsed=!0}function m(n){return new Promise(function(e,t){n.onload=function(){e(n.result)},n.onerror=function(){t(n.error)}})}function v(e){var t=new FileReader,n=m(t);return t.readAsArrayBuffer(e),n}function g(e){var t;return e.slice?e.slice(0):((t=new Uint8Array(e.byteLength)).set(new Uint8Array(e)),t.buffer)}function b(){return this.bodyUsed=!1,this._initBody=function(e){if(this._bodyInit=e)if(\"", "\")),t}))),e.body=JSON.stringify(e.body),e;throw Error(\"", "\"),babelAdvertInfoNew:function(e){var t=e.body,n=e.success,e=e.error;return f(i(\"", "\"&args[]=\"", "\" Args: \"", "\"==typeof e)this._bodyText=e;else if(a&&Blob.prototype.isPrototypeOf(e))this._bodyBlob=e;else if(r&&FormData.prototype.isPrototypeOf(e))this._bodyFormData=e;else if(t&&URLSearchParams.prototype.isPrototypeOf(e))this._bodyText=e.toString();else if(o&&a&&u(e))this._bodyArrayBuffer=g(e.buffer),this._bodyInit=new Blob([this._bodyArrayBuffer]);else{if(!o||!ArrayBuffer.prototype.isPrototypeOf(e)&&!c(e))throw new Error(\"", "\",this._initBody(e)}e.fetch||(t=\"", "\")}(this._bodyArrayBuffer));if(this._bodyFormData)throw new Error(\"", "\"h5static.m.jd.com\"", "\"Body not allowed for GET or HEAD requests\"", "\");return Promise.resolve(this._bodyText)},r&&(this.formData=function(){return this.text().then(x)}),this.json=function(){return this.text().then(JSON.parse)},this}function w(e,t){var n,r=(t=t||{}).body;if(e instanceof w){if(e.bodyUsed)throw new TypeError(\"", "\",this._bodyBlob.type):t&&URLSearchParams.prototype.isPrototypeOf(e)&&this.headers.set(\"", "\"):this._bodyBlob&&this._bodyBlob.type?this.headers.set(\"", "\"!=typeof document?document:void 0)))return null;try{return t.activeElement||t.body}catch(e){return t.body}}function rt(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function ot(e,t){var n,r=rt(e);for(e=0;r;){if(3===r.nodeType){if(n=e+r.textContent.length,e<=t&&t<=n)return{node:r,offset:t-e};e=n}e:{for(;r;){if(r.nextSibling){r=r.nextSibling;break e}r=r.parentNode}r=void 0}r=rt(r)}}function it(){for(var e=window,t=nt();t instanceof e.HTMLIFrameElement;){try{var n=\"", "\",body:i,credentials:\"", "\"==c.display)return null;if(o==this.root||9==o.nodeType?(i=!0,o==this.root||o==p?h&&!this.root?!y||0==y.width&&0==y.height?r=u=o=null:u=y:u=n:(s=(a=g(o))&&m(a),l=a&&this._computeTargetAndRootIntersection(a,s,n),s&&l?(o=a,u=v(s,l)):r=o=null)):o!=(a=o.ownerDocument).body&&o!=a.documentElement&&\""], "endpoints": ["\",babelAwardCollection:\"", "\",r),n,e)},babelAwardCollection:function(e){var t=e.data,t=void 0===t?{}:t,n=e.success,e=e.error;return l(i(\""]}, "critical_code_blocks": [{"type": "pattern_2", "content": "return\"object\"==typeof console&&console.log&&Function.prototype.apply.call(console.log,console,arguments)},o.formatArgs=function(e){var t,n,r=this.useColors;e[0]=(r?\"%c\":\"\")+this.namespace+(r?\" %c\":\" \")+e[0]+(r?\"%c \":\" \")+\"+\"+o.humanize(this.diff),r&&(r=\"color: \"+this.color,e.splice(1,0,r,\"color: inherit\"),e[n=t=0].replace(/%[a-zA-Z%]/g,function(e){\"%%\"!==e&&(t++,\"%c\"===e)&&(n=t)}", "length": 383}, {"type": "pattern_2", "content": "if(void 0===t)throw Error(\"`lowPriorityWarningWithoutStack(condition, format, ...args)` requires a warning message argument\");if(!e){for(var n=arguments.length,r=Array(2<n?n-2:0),o=2;o<n;o++)r[o-2]=arguments[o];!function(e){for(var t=arguments.length,n=Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var o=0,i=\"Warning: \"+e.replace(/%s/g,function(){return n[o++]}", "length": 365}], "summary": {"args_patterns_count": 24, "api_patterns_count": 24, "parameter_types_count": 5, "function_count": 0, "constant_categories": 4, "critical_blocks_count": 2}}, {"url": "https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js", "file_size": 195815, "args_generation_logic": ["Rp(t=[this]).call(t,r.args))", "key:\"pushLimit\"", "key:\"addItem\"", "key:\"init\"", "key:\"getActionInfo\"", "key:\"getInstance\"", "key:\"init\"", "key:\"unset\"", "key:\"init\"", "key:\"getInstance\"", "key:\"unset\"", "key:\"init\"", "key:\"getInstance\"", "key:\"protocol\"", "key:\"domain\"", "key:\"path\"", "key:\"functionId\"", "key:\"query\"", "key:\"body\"", "key:\"headers\"", "key:\"status\"", "key:\"headers\"", "key:\"body\"", "key:\"trace\"", "key:\"init\"", "key:\"reportError\"", "key:\"getCaputureRequestType\"", "key:\"init\"", "key:\"setConfig\"", "key:\"_commonCofig\"", "key:\"addPlugin\"", "key:\"addInterceptor\"", "key:\"removeInterceptor\"", "key:\"_installPlugins\"", "key:\"reportException\"", "key:\"report\"", "key:\"reportByJSbridge\"", "key:\"_getCacheData\"", "key:\"_setCacheData\"", "key:\"_clearCacheData\"", "key:\"_formatItemData\"", "key:\"_trySend\"", "key:\"_trySendByJsbrige\"", "key:\"wrap\"", "key:\"_wrapBuiltIns\"", "key:\"start\"", "key:\"_formatData\"", "key:\"_interceptorsMap\"", "key:\"_send\"", "key:\"_sendByJSbridge\"", "key:\"_ignoreNextOnError\"", "key:\"setFPS\"", "key:\"setFP\"", "key:\"setFCP\"", "key:\"setLCP\"", "key:\"setFID\"", "key:\"setCLS\"", "key:\"setCLSInfo\"", "key:\"setTTI\"", "key:\"setLongTask\"", "key:\"setTBT\"", "key:\"buildServerInfoParam\"", "key:\"buildPerformanceParam\"", "key:\"report\"", "key:\"getInstance\"", "Key:function(e", "key==t)return r", "key:\"values\"==t?r.value:[r.key", "key:\"getFirstConsistentlyInteractive\"", "key:\"startSchedulingTimerTasks\"", "key:\"setMinValue\"", "key:\"rescheduleTimer\"", "key:\"disable\"", "key:\"_registerPerformanceObserver\"", "key:\"_registerListeners\"", "key:\"_unregisterListeners\"", "key:\"_beforeJSInitiatedRequestCallback\"", "key:\"_afterJSInitiatedRequestCallback\"", "key:\"_networkRequestFinishedCallback\"", "key:\"_longTaskFinishedCallback\"", "key:\"_mutationObserverCallback\"", "key:\"_getMinValue\"", "key:\"_incompleteRequestStarts\"", "key:\"_checkTTI\""], "api_call_logic": ["postMessage(hy(e)", "postMessage)&&!Kg.importScripts&&Ig&&\"file:\"!==Ig.protocol&&!ty(Sy)?(Cg=Sy", "postMessage({method:\"getNetWorkType\"", "POST\"===s){if(Im(o[0])){var f=IA(o[0]", "POST\"===a.method&&a.body){var y=(i=qA(sj.mark((function e(){var t", "POST\"===s&&(h||(f=IA(f", "POST\"!==A||OA(v)||OA(v.functionId)||(E=v.functionId", "POST\"===o.toUpperCase()&&null!==n&&uv(n).length>s&&u(new Error({message:\"请求数据超过最大长度限制\"", "postMessage({method:\"notifyMessageToNative\"", "postMessage({method:\"getNetWorkType\"", "postMessage\"))window.webkit.messageHandlers.JDHybrid.postMessage({method:\"getPreloadData\"", "postMessage({method:\"getPreloadData\"", "postMessage({method:\"getPreloadData\"", "postMessage(r)", "postMessage方法执行错误\")", "postMessage(c)", "postMessage)||void 0===s||s.call(c", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"configNavigationBar\"", "postMessage({method:\"configBtnSince610\"", "postMessage({method:\"getPhoneBasicInfo\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage\":fS()&&(s=\"JDHybridPreload.fetchPreloadData\")", "postMessage\":fS()&&(c=\"JDHybrid.fetchPreloadData\")", "postMessage({method:\"notifyMessageToNative\"", "postMessage({method:\"isAppLogin\"", "postMessage({method:\"notifyMessageToNative\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"requestIsvToken\"", "postMessage({method:\"saveImageToPhoteAlbum\"", "postMessage({method:\"notifyMessageToNative\"", "postMessage({method:\"showNativeBarcodeScan\"", "postMessage({method:\"notifyMessageToNative\"", "postMessage({method:\"configThemeNavigator\"", "postMessage({method:\"callRouterModuleWithParams\"", "postMessage({method:\"showCloseBtn\"", "postMessage({method:\"callSyncRouterModuleWithParams\"", "postMessage({method:\"close\"", "postMessage({method:\"configXView\"", "postMessage({method:\"configCloseButton\""], "parameter_generation": {"from": ["from:Kd", "from:\"couponBatch\""], "client": ["CLIENT:\"QQfriends\"", "CLIENT:\"QQzone\""], "appid": ["appId:i", "appId:e", "appId:e", "appid:r"]}, "function_definitions": [], "string_constants": {"parameters": ["\"body\"", "\"===a.method&&a.body){var y=(i=qA(sj.mark((function e(){var t,r,n,o,i,c,s,u,l,f=arguments;return sj.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=f.length>0&&void 0!==f[0]?f[0]:100,r=a.clone&&a.clone()){e.next=4;break}throw new Error(\"", "\"Request.body.getReader().read()不存在\"", "\":s,l=o.timeout,f=void 0===l?0:l,d=o.costTime,p=void 0===d?0:d,h=o.body,v=void 0===h?{}:h,m=o.query,g=void 0===m?{}:m,y=o.requestHeaders,w=void 0===y?{}:y,b=o.responseHeaders,S=void 0===b?{}:b,k=o.method,A=void 0===k?\"", "\"==typeof e&&e.name&&!this._plugins[e.name]){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];this._plugins[e.name]={args:r,fn:e,installed:!1},this._installed&&this._installPlugins()}return this}},{key:\"", "\",value:function(){for(var e in this._plugins){var t,r=this._plugins[e];if(!r.installed)r.fn.apply(this,Rp(t=[this]).call(t,r.args)),r.installed=!0}}},{key:\"", "\"===s)Gk(r._record,{body:{},requestStamp:Lv()});else if(\"", "\",p={method:a.method,headers:a.headers,body:{}},y(100).then((function(e){if(!OA(e)){var t=IA(e,d);OA(t)||Gk(v,{body:t})}})).catch((function(e){console.warn(\"", "\",method:s,url:u,query:l,body:f,requestHeaders:d,requestStamp:Lv(),pageUrl:window.location.href,canceled:!1})}}catch(e){m=!1}return t.apply(window,n).then((function(t){try{if(m){var r,n={},o=uj(t.headers);try{for(o.s();!(r=o.n()).done;){var i=tp(r.value,2),a=i[0],c=i[1];n[a]=c}}catch(e){o.e(e)}finally{o.f()}var s=Lv(),u=s-v.requestStamp,l=t.status;Gk(v,{status:l,responseHeaders:n,responseStamp:s,costTime:u});var f=_A(n,\"", "\"]),OA(g)||(g.h5st&&delete g.h5st,g[\"", "\");case 4:if(n=r&&r.body&&r.body.getReader&&r.body.getReader()){e.next=7;break}throw new Error(\"", "\",e)}))}}else u=String(a);if(m=TA(u,Rp(g=[]).call(g,lk(RA),lk(e.options.captureApiHostList||[])))){if(!OA(c)&&kA(c)&&(p=dj(dj({},p),c)),!OA(p)){var w=p,b=w.headers,S=w.body,k=w.method;if(b)if(\"", "\"===s){if(Im(o[0])){var f=IA(o[0],l);Gk(r._record,{body:f})}Gk(r._record,{requestStamp:Lv()})}var d=function(){try{if(4===r.readyState){if(!r._needTrack)return;r._record||(r._record={});var t,n=r.getAllResponseHeaders()||\"", "\"]),OA(v)||(v.h5st&&delete v.h5st,v[\""]}, "critical_code_blocks": [{"type": "pattern_2", "content": "args:r,fn:e,installed:!1},this._installed&&this._installPlugins()}return this}},{key:\"addInterceptor\",value:function(e){\"function\"==typeof e&&(this._interceptors||(this._interceptors=[]),this._interceptors.push(e))}", "length": 215}, {"type": "pattern_2", "content": "var t,r=this._plugins[e];if(!r.installed)r.fn.apply(this,Rp(t=[this]).call(t,r.args)),r.installed=!0}}},{key:\"reportException\",value:function(e){var t,r,n=e.err,o=e.extra,i=e.errLevel,a=e.errType,c=e.errCdoe,s=e.is<PERSON><PERSON><PERSON>ort,u=void 0!==s&&s,l={type:n.name,stack:Xp(t=Jj(n)).call(t,0,null===(r=this._config)||void 0===r?void 0:r.stackLimit)}", "length": 340}, {"type": "pattern_4", "content": "Kg.postMessage(hy(e),Ig.protocol+\"//\"+Ig.host)};sy&&uy||(sy=function(e){iy(arguments.length,1);var t=Zg(e)?e:dy(e),r=ny(arguments,1);return my[++vy]=function(){Qg(t,void 0,r)}", "length": 175}, {"type": "pattern_4", "content": "if(\"POST\"===a.method&&a.body){var y=(i=qA(sj.mark((function e(){var t,r,n,o,i,c,s,u,l,f=arguments;return sj.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(t=f.length>0&&void 0!==f[0]?f[0]:100,r=a.clone&&a.clone()){e.next=4;break}", "length": 239}, {"type": "pattern_4", "content": "}:\"POST\"===s&&(h||(f=IA(f,d))),Gk(v,{requestType:\"Fetch\",method:s,url:u,query:l,body:f,requestHeaders:d,requestStamp:Lv(),pageUrl:window.location.href,canceled:!1}", "length": 163}, {"type": "pattern_4", "content": "throw new n(\"window.webkit.messageHandlers.JDAppUnite.postMessage方法执行错误\")}},j.prototype.createJdReminderIOSSchema=function(e,t){return b&&\"add\"==e?this.createIOSSchemaForRouter(e,t):h?this.createIOSSchemaForWKWebview(e,t):S?this.createAllSameSchemaUsingJsBridge(e,t):this.createIOSSchemaForLowVersion(e,t)}", "length": 306}, {"type": "pattern_4", "content": "status:\"-100\",msg:\"bridge result JSON parse error\",data:e})}delete window[n]},window.jdWebviewClickBackCallBack=e,lS()?window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/enableWebControlBack\",routerParam:{businessType:\"enableWebControlBack\"}", "length": 332}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:!0};if(!1!==$O(r,t)){if(lS())return window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/closeWebview\",callBackName:null,callBackId:null}", "length": 286}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:\"JDAppUnite.getPhoneBasicInfo\"},t)){var r=XO(\"getPhoneBasicInfo\");window[r]=function(n){var o;try{if((o=JSON.parse(n))&&Jp(o.data)){var i=o.data,a={}", "length": 209}, {"type": "pattern_4", "content": "if(!1===QO(\"jd\",\"9.1.6\",t))return void delete window[r];window.webkit.messageHandlers.MobileNavi.postMessage({method:\"callRouterModuleWithParams\",params:{routerURL:\"router://JDWebViewBusinessModule/getActualNaviStatusHeight\",callBackName:r}", "length": 240}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:\"JDAppUnite.notifyMessageToNative\"};if(!1!==$O(r,t))return lS()?(window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"callRouterModuleWithParams\",params:uv({routerURL:\"router://JDWebViewBusinessModule/enableWebControlBack\",routerParam:{businessType:\"goBack\"}", "length": 327}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:\"JDPerformance.transferRenderTime\"};if(!1!==$O(n,r))return lS()?(window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"notifyMessageToNative\",params:uv({businessType:\"transferRenderTime\",params:{renderTime:String(e)}", "length": 284}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:\"JDAppUnite.notifyMessageToNative\"},r)&&(lS()?window.webkit.messageHandlers.JDAppUnite.postMessage({method:\"callRouterModuleWithParams\",params:uv({routerURL:\"router://JDWebViewAbilityModule/openUrlInSafari\",routerParam:{url:e}", "length": 286}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.MobileNavi.postMessage\",android:\"JDAppUnite.saveImageToPhoteAlbum\"},r)){var n=XO(\"saveImageToPhoteAlbum\");window[n]=function(o){var i;try{\"0\"==(i=JSON.parse(o)).code?(t({status:\"0\",msg:\"下载成功\"}", "length": 220}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.JDAppUnite.postMessage\",android:\"JDAppUnite.notifyMessageToNative\"},r)){var n=XO(\"saveVideo\");window[n]=function(o){try{var i=JSON.parse(o);\"0\"==i.status||\"-1\"===i.status?(t(i),delete window[n]):Mp(e.processing)&&e.processing(i)}", "length": 257}, {"type": "pattern_4", "content": "ios:\"webkit.messageHandlers.MobileNavi.postMessage\",android:\"MobileNavi.showNativeBarcodeScan\"},t)){var r=XO(\"scanOrderNo\");window[r]=function(n){try{var o=JSON.parse(n);e({status:\"0\",msg:\"success\",data:o}", "length": 205}], "summary": {"args_patterns_count": 84, "api_patterns_count": 43, "parameter_types_count": 3, "function_count": 0, "constant_categories": 1, "critical_blocks_count": 16}}], "summary": {"analyzed_files": 3, "total_args_patterns": 450, "total_api_patterns": 129}}