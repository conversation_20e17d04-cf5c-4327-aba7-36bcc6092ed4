#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东状态修改器
查找页面中的status=2并修改为status=5，然后捕获真实的API请求
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class JDStatusModifier:
    def __init__(self):
        self.driver = None
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_driver(self):
        """设置浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用性能日志
        chrome_options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL'
        })
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def inject_comprehensive_monitor(self):
        """注入全面的监控器"""
        monitor_script = """
        window._COMPREHENSIVE_MONITOR = {
            requests: [],
            responses: [],
            statusModifications: [],
            apiCalls: [],
            originalData: null
        };
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            this._startTime = Date.now();
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            
            // 记录请求
            const requestInfo = {
                type: 'xhr',
                method: this._method,
                url: this._url,
                data: data,
                timestamp: Date.now(),
                headers: {}
            };
            
            window._COMPREHENSIVE_MONITOR.requests.push(requestInfo);
            
            // 如果是API请求，特别记录
            if (this._url && (this._url.includes('api.m.jd.com') || this._url.includes('newBabelAwardCollection'))) {
                window._COMPREHENSIVE_MONITOR.apiCalls.push(requestInfo);
                console.log('🔥 API请求:', this._url, data);
            }
            
            // 拦截响应
            const originalOnReadyStateChange = xhr.onreadystatechange;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    const responseInfo = {
                        type: 'xhr',
                        url: xhr._url,
                        status: xhr.status,
                        response: xhr.responseText,
                        timestamp: Date.now(),
                        duration: Date.now() - xhr._startTime
                    };
                    
                    window._COMPREHENSIVE_MONITOR.responses.push(responseInfo);
                    
                    // 检查响应中是否包含status字段
                    if (xhr.responseText && xhr.responseText.includes('"status"')) {
                        console.log('📊 发现包含status的响应:', xhr._url);
                        try {
                            const responseJson = JSON.parse(xhr.responseText);
                            analyzeStatusInResponse(responseJson, xhr._url);
                        } catch (e) {
                            console.log('解析响应JSON失败:', e);
                        }
                    }
                }
                
                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.apply(xhr, arguments);
                }
            };
            
            return originalXHRSend.apply(this, [data]);
        };
        
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            const requestInfo = {
                type: 'fetch',
                url: url,
                options: options,
                timestamp: Date.now()
            };
            
            window._COMPREHENSIVE_MONITOR.requests.push(requestInfo);
            
            if (url.includes('api.m.jd.com') || url.includes('newBabelAwardCollection')) {
                window._COMPREHENSIVE_MONITOR.apiCalls.push(requestInfo);
                console.log('🔥 Fetch API请求:', url, options);
            }
            
            return originalFetch.apply(this, arguments).then(response => {
                response.clone().text().then(text => {
                    const responseInfo = {
                        type: 'fetch',
                        url: url,
                        status: response.status,
                        response: text,
                        timestamp: Date.now()
                    };
                    
                    window._COMPREHENSIVE_MONITOR.responses.push(responseInfo);
                    
                    if (text && text.includes('"status"')) {
                        console.log('📊 Fetch发现包含status的响应:', url);
                        try {
                            const responseJson = JSON.parse(text);
                            analyzeStatusInResponse(responseJson, url);
                        } catch (e) {
                            console.log('解析Fetch响应JSON失败:', e);
                        }
                    }
                });
                return response;
            });
        };
        
        // 分析响应中的status字段
        function analyzeStatusInResponse(obj, url) {
            function findStatus(item, path = '') {
                if (typeof item === 'object' && item !== null) {
                    if (Array.isArray(item)) {
                        item.forEach((subItem, index) => {
                            findStatus(subItem, path + '[' + index + ']');
                        });
                    } else {
                        for (const key in item) {
                            if (key === 'status') {
                                console.log('🎯 发现status字段:', path + '.' + key, '=', item[key]);
                                window._COMPREHENSIVE_MONITOR.statusModifications.push({
                                    url: url,
                                    path: path + '.' + key,
                                    originalValue: item[key],
                                    timestamp: Date.now()
                                });
                            } else if (typeof item[key] === 'object') {
                                findStatus(item[key], path + '.' + key);
                            }
                        }
                    }
                }
            }
            
            findStatus(obj);
        }
        
        // 查找页面中的数据对象
        function findPageData() {
            const possibleDataKeys = [
                '__react_data__',
                'window.pageData',
                'window.activityData',
                'window.babelData',
                'window.floorData'
            ];
            
            for (const key of possibleDataKeys) {
                try {
                    let data = null;
                    if (key.startsWith('window.')) {
                        const prop = key.replace('window.', '');
                        data = window[prop];
                    } else {
                        data = window[key];
                    }
                    
                    if (data && typeof data === 'object') {
                        console.log('📦 发现页面数据:', key);
                        window._COMPREHENSIVE_MONITOR.originalData = {
                            key: key,
                            data: data
                        };
                        
                        // 分析数据中的status字段
                        analyzeStatusInResponse(data, 'pageData:' + key);
                        return data;
                    }
                } catch (e) {
                    continue;
                }
            }
            
            return null;
        }
        
        // 修改页面数据中的status值
        function modifyPageStatus() {
            const pageData = findPageData();
            if (pageData) {
                function modifyStatus(obj) {
                    if (typeof obj === 'object' && obj !== null) {
                        if (Array.isArray(obj)) {
                            obj.forEach(modifyStatus);
                        } else {
                            for (const key in obj) {
                                if (key === 'status' && obj[key] === 2) {
                                    console.log('✅ 修改页面status: 2 -> 5');
                                    obj[key] = 5;
                                    window._COMPREHENSIVE_MONITOR.statusModifications.push({
                                        type: 'pageData',
                                        path: key,
                                        originalValue: 2,
                                        newValue: 5,
                                        timestamp: Date.now()
                                    });
                                } else if (typeof obj[key] === 'object') {
                                    modifyStatus(obj[key]);
                                }
                            }
                        }
                    }
                }
                
                modifyStatus(pageData);
            }
        }
        
        // 触发页面重新渲染
        function triggerRerender() {
            // 触发React重新渲染
            if (window.React && window.ReactDOM) {
                console.log('🔄 触发React重新渲染');
                // 触发状态更新事件
                window.dispatchEvent(new Event('resize'));
                window.dispatchEvent(new Event('scroll'));
            }
            
            // 触发Vue重新渲染
            if (window.Vue) {
                console.log('🔄 触发Vue重新渲染');
                window.dispatchEvent(new Event('vue-update'));
            }
            
            // 通用DOM更新
            const event = new CustomEvent('statusChanged', {
                detail: { status: 5 }
            });
            document.dispatchEvent(event);
        }
        
        // 获取监控数据
        window.getComprehensiveData = function() {
            return window._COMPREHENSIVE_MONITOR;
        };
        
        // 执行状态修改
        window.modifyStatusAndTrigger = function() {
            modifyPageStatus();
            triggerRerender();
            
            // 延迟触发点击事件
            setTimeout(() => {
                console.log('🖱️ 触发点击事件');
                document.body.click();
            }, 1000);
            
            return window._COMPREHENSIVE_MONITOR.statusModifications.length;
        };
        
        // 初始化
        setTimeout(() => {
            findPageData();
            console.log('📡 全面监控器已启动');
        }, 2000);
        
        console.log('🚀 全面监控器已注入');
        """
        
        self.driver.execute_script(monitor_script)
        
    def wait_and_analyze_page(self):
        """等待并分析页面"""
        try:
            # 等待页面加载
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 滚动页面确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(3)
            
            # 获取初始监控数据
            initial_data = self.driver.execute_script("return window.getComprehensiveData ? window.getComprehensiveData() : null;")
            
            print(f"初始监控数据:")
            if initial_data:
                print(f"  请求数: {len(initial_data['requests'])}")
                print(f"  响应数: {len(initial_data['responses'])}")
                print(f"  API调用数: {len(initial_data['apiCalls'])}")
                print(f"  状态修改数: {len(initial_data['statusModifications'])}")
                
                # 显示发现的status字段
                if initial_data['statusModifications']:
                    print("  发现的status字段:")
                    for mod in initial_data['statusModifications']:
                        print(f"    {mod['path']}: {mod['originalValue']}")
                        
            return initial_data
            
        except Exception as e:
            print(f"等待和分析页面失败: {e}")
            return None
            
    def modify_status_and_capture(self):
        """修改status并捕获请求"""
        try:
            print("执行状态修改...")
            
            # 执行状态修改
            modifications_count = self.driver.execute_script("return window.modifyStatusAndTrigger ? window.modifyStatusAndTrigger() : 0;")
            print(f"修改了 {modifications_count} 个status字段")
            
            # 等待响应
            time.sleep(5)
            
            # 尝试点击页面上的元素
            self.trigger_additional_clicks()
            
            # 等待更多响应
            time.sleep(5)
            
            # 获取最终数据
            final_data = self.driver.execute_script("return window.getComprehensiveData ? window.getComprehensiveData() : null;")
            
            return final_data
            
        except Exception as e:
            print(f"修改状态和捕获请求失败: {e}")
            return None
            
    def trigger_additional_clicks(self):
        """触发额外的点击"""
        try:
            # 查找可能的预约/领券按钮
            selectors = [
                "button", "a", "[onclick]", ".btn", ".button",
                "[class*='reserve']", "[class*='book']", "[class*='award']",
                "[class*='collection']", "[class*='coupon']"
            ]
            
            clicked_count = 0
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements[:2]:  # 每种选择器最多点击2个
                        try:
                            if element.is_displayed() and element.is_enabled():
                                print(f"点击元素: {selector}")
                                
                                # 获取点击前的数据
                                before_data = self.driver.execute_script("return window.getComprehensiveData ? window.getComprehensiveData() : {requests: [], apiCalls: []};")
                                
                                # 点击
                                try:
                                    element.click()
                                except:
                                    self.driver.execute_script("arguments[0].click();", element)
                                
                                time.sleep(2)
                                
                                # 获取点击后的数据
                                after_data = self.driver.execute_script("return window.getComprehensiveData ? window.getComprehensiveData() : {requests: [], apiCalls: []};")
                                
                                # 检查是否有新的API请求
                                new_api_calls = len(after_data['apiCalls']) - len(before_data['apiCalls'])
                                if new_api_calls > 0:
                                    print(f"  触发了 {new_api_calls} 个新的API请求!")
                                
                                clicked_count += 1
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
                    
            print(f"总共点击了 {clicked_count} 个元素")
            
        except Exception as e:
            print(f"触发额外点击失败: {e}")
            
    def run_status_modification(self, url):
        """运行状态修改"""
        try:
            print("=== 启动状态修改器 ===")
            
            # 设置驱动
            self.setup_driver()
            
            # 加载页面
            print(f"正在加载页面: {url}")
            self.driver.get(url)
            
            # 注入监控器
            print("注入全面监控器...")
            self.inject_comprehensive_monitor()
            
            # 等待并分析页面
            initial_data = self.wait_and_analyze_page()
            
            # 修改状态并捕获请求
            final_data = self.modify_status_and_capture()
            
            if final_data:
                result = {
                    'initial_data': initial_data,
                    'final_data': final_data,
                    'summary': {
                        'total_requests': len(final_data['requests']),
                        'total_responses': len(final_data['responses']),
                        'api_calls': len(final_data['apiCalls']),
                        'status_modifications': len(final_data['statusModifications'])
                    }
                }
                
                return result
            else:
                return None
                
        except Exception as e:
            print(f"状态修改过程中出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    modifier = JDStatusModifier()
    result = modifier.run_status_modification(url)
    
    if result:
        print("\n=== 状态修改结果 ===")
        print(f"总请求数: {result['summary']['total_requests']}")
        print(f"总响应数: {result['summary']['total_responses']}")
        print(f"API调用数: {result['summary']['api_calls']}")
        print(f"状态修改数: {result['summary']['status_modifications']}")
        
        # 保存结果
        with open('jd_status_modification_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("详细结果已保存到 jd_status_modification_result.json")
        
        # 显示API调用
        if result['final_data']['apiCalls']:
            print(f"\n=== API调用 ===")
            for i, call in enumerate(result['final_data']['apiCalls'], 1):
                print(f"{i}. {call['type']}: {call['url']}")
                if 'data' in call and call['data']:
                    print(f"   数据: {str(call['data'])[:100]}...")
                    
        # 显示状态修改
        if result['final_data']['statusModifications']:
            print(f"\n=== 状态修改 ===")
            for i, mod in enumerate(result['final_data']['statusModifications'], 1):
                print(f"{i}. {mod.get('path', 'unknown')}: {mod.get('originalValue', 'unknown')} -> {mod.get('newValue', 'modified')}")
                
    else:
        print("状态修改失败")

if __name__ == "__main__":
    main()
