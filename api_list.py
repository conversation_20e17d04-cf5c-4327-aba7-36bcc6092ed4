#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购API列表
所有可用的API接口和方法
"""

from jd_ready_api import JDReadyAPI

def show_api_list():
    """显示所有可用的API"""
    print("=" * 60)
    print("🚀 京东预约抢购API列表")
    print("=" * 60)
    
    print("\n📦 主要类：JDReadyAPI")
    print("   完整的京东预约抢购API类")
    
    print("\n🔧 初始化方法：")
    print("   JDReadyAPI(cookie=None)")
    print("   - cookie: 可选，京东登录Cookie")
    
    print("\n📋 核心API方法：")
    
    # 1. 预约单个商品
    print("\n1️⃣  预约单个商品")
    print("   api.reserve_product(product_num)")
    print("   参数: product_num - 商品编号 ('1', '2', '3', '4')")
    print("   返回: dict - 预约结果")
    print("   示例: result = api.reserve_product('1')")
    
    # 2. 预约所有商品
    print("\n2️⃣  预约所有商品")
    print("   api.reserve_all_products(delay=2)")
    print("   参数: delay - 请求间隔时间（秒）")
    print("   返回: dict - 所有商品的预约结果")
    print("   示例: results = api.reserve_all_products(delay=3)")
    
    # 3. 获取商品信息
    print("\n3️⃣  获取商品信息")
    print("   api.get_product_info()")
    print("   参数: 无")
    print("   返回: dict - 活动和商品信息")
    print("   示例: info = api.get_product_info()")
    
    # 4. 设置Cookie
    print("\n4️⃣  设置Cookie")
    print("   api.set_cookie(cookie)")
    print("   参数: cookie - 新的京东登录Cookie")
    print("   返回: 无")
    print("   示例: api.set_cookie('your_cookie_here')")
    
    print("\n🎯 商品信息：")
    api = JDReadyAPI()
    products = api.get_product_info()
    for num, product in products["products"].items():
        print(f"   {num}. {product['name']} (楼层ID: {product['floor_id']})")
    
    print(f"\n🏷️  活动信息：")
    print(f"   活动ID: {products['activity_id']}")
    print(f"   用户: {products['user']}")

def show_usage_examples():
    """显示使用示例"""
    print("\n" + "=" * 60)
    print("📖 使用示例")
    print("=" * 60)
    
    examples = [
        {
            "title": "基础使用",
            "code": """
# 导入API
from jd_ready_api import JDReadyAPI

# 创建实例
api = JDReadyAPI()

# 预约商品1
result = api.reserve_product('1')
print(f"预约结果: {result['status']}")
"""
        },
        {
            "title": "批量预约",
            "code": """
# 预约所有商品
results = api.reserve_all_products(delay=2)

# 统计成功数量
success_count = sum(1 for r in results.values() 
                   if r.get('success') and r.get('subCode') == 'A01')
print(f"成功预约 {success_count} 个商品")
"""
        },
        {
            "title": "自定义Cookie",
            "code": """
# 使用自定义Cookie
my_cookie = "pinId=xxx; pin=xxx; ..."
api = JDReadyAPI(cookie=my_cookie)

# 或者后续设置
api.set_cookie(my_cookie)
"""
        },
        {
            "title": "错误处理",
            "code": """
result = api.reserve_product('1')

if result['success']:
    if result.get('api_success'):
        print(f"预约状态: {result['status']}")
    else:
        print(f"API失败: {result['description']}")
else:
    print(f"请求失败: {result['error']}")
"""
        }
    ]
    
    for i, example in enumerate(examples, 1):
        print(f"\n{i}. {example['title']}")
        print(example['code'])

def show_response_format():
    """显示响应格式"""
    print("\n" + "=" * 60)
    print("📊 响应格式")
    print("=" * 60)
    
    print("\n🔄 reserve_product() 返回格式：")
    response_format = {
        "success": "bool - 请求是否成功",
        "product_name": "str - 商品名称",
        "floor_id": "str - 楼层ID",
        "status_code": "int - HTTP状态码",
        "timestamp": "int - 请求时间戳",
        "api_success": "bool - API调用是否成功",
        "code": "str - API响应码",
        "subCode": "str - 业务状态码",
        "message": "str - 提示信息",
        "status": "str - 预约状态",
        "description": "str - 状态描述",
        "response": "dict - 完整响应数据"
    }
    
    for key, desc in response_format.items():
        print(f"   {key}: {desc}")
    
    print("\n📋 状态码说明：")
    status_codes = {
        "A01": "预约成功",
        "A28": "没有抢到",
        "A02": "活动未开始",
        "A03": "活动已结束"
    }
    
    for code, desc in status_codes.items():
        print(f"   {code}: {desc}")

def show_quick_start():
    """显示快速开始"""
    print("\n" + "=" * 60)
    print("⚡ 快速开始")
    print("=" * 60)
    
    quick_start = """
# 1. 导入API
from jd_ready_api import JDReadyAPI

# 2. 创建实例（使用默认Cookie）
api = JDReadyAPI()

# 3. 预约单个商品
result = api.reserve_product('1')

# 4. 检查结果
if result['success'] and result.get('subCode') == 'A01':
    print('🎉 预约成功！')
else:
    print(f'预约状态: {result.get("status", "未知")}')

# 5. 预约所有商品
results = api.reserve_all_products()
"""
    
    print(quick_start)

def main():
    """主函数"""
    show_api_list()
    show_usage_examples()
    show_response_format()
    show_quick_start()
    
    print("\n" + "=" * 60)
    print("✅ API列表展示完成")
    print("=" * 60)
    print("\n📁 相关文件：")
    print("   - jd_ready_api.py: 完整API实现")
    print("   - jd_api_documentation.md: 详细文档")
    print("   - api_list.py: 本文件（API列表）")
    
    print("\n🎯 核心功能：")
    print("   ✅ 预约单个商品")
    print("   ✅ 批量预约所有商品")
    print("   ✅ 自动参数生成")
    print("   ✅ 响应解析")
    print("   ✅ 错误处理")
    print("   ✅ Cookie管理")

if __name__ == "__main__":
    main()
