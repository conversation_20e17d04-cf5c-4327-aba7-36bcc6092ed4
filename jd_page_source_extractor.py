#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东页面源码提取工具
获取完整的页面源码用于手动分析
"""

import json
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class JDPageSourceExtractor:
    def __init__(self):
        self.driver = None
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 设置用户代理，模拟移动端
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')
        
        # 使用ChromeDriverManager自动下载和管理驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_page_source(self, url):
        """提取页面源码"""
        try:
            print("正在设置浏览器...")
            self.setup_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(8)  # 等待页面完全加载
            
            # 滚动页面确保所有内容加载
            print("正在滚动页面加载所有内容...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            print("正在获取页面源码...")
            page_source = self.driver.page_source
            
            # 保存完整页面源码
            with open('jd_page_source.html', 'w', encoding='utf-8') as f:
                f.write(page_source)
            print("页面源码已保存到 jd_page_source.html")
            
            # 提取所有脚本内容
            print("正在提取脚本内容...")
            script_elements = self.driver.find_elements(By.TAG_NAME, "script")
            scripts_data = []
            
            for i, script in enumerate(script_elements):
                try:
                    script_content = script.get_attribute('innerHTML')
                    script_src = script.get_attribute('src')
                    
                    if script_content:
                        scripts_data.append({
                            'index': i,
                            'type': 'inline',
                            'content': script_content,
                            'src': script_src,
                            'length': len(script_content)
                        })
                    elif script_src:
                        scripts_data.append({
                            'index': i,
                            'type': 'external',
                            'src': script_src
                        })
                except Exception as e:
                    continue
            
            # 保存脚本数据
            with open('jd_scripts_data.json', 'w', encoding='utf-8') as f:
                json.dump(scripts_data, f, indent=2, ensure_ascii=False)
            print(f"提取了 {len(scripts_data)} 个脚本，已保存到 jd_scripts_data.json")
            
            # 分析页面源码中的关键信息
            print("正在分析关键信息...")
            analysis_result = self.analyze_source_code(page_source)
            
            # 保存分析结果
            with open('jd_analysis_result.json', 'w', encoding='utf-8') as f:
                json.dump(analysis_result, f, indent=2, ensure_ascii=False)
            print("分析结果已保存到 jd_analysis_result.json")
            
            return {
                'page_source': page_source,
                'scripts_data': scripts_data,
                'analysis_result': analysis_result
            }
            
        except Exception as e:
            print(f"提取页面源码时出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()
                
    def analyze_source_code(self, source_code):
        """分析源码中的关键信息"""
        analysis = {
            'args_patterns': [],
            'api_urls': [],
            'activity_ids': [],
            'function_calls': [],
            'key_values': [],
            'role_ids': [],
            'strengthen_keys': []
        }
        
        # 查找args相关的模式
        args_patterns = [
            r'args["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'"args"\s*:\s*"([^"]+)"',
            r'args:\s*"([^"]+)"',
            r'newBabelAwardCollection[^}]*args["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'key=([A-F0-9]+)[^,]*,roleId=([A-F0-9]+)[^,]*,strengthenKey=([A-F0-9]+)',
            r'functionId=newBabelAwardCollection[^&]*&[^&]*body=([^&]+)',
        ]
        
        for pattern in args_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['args_patterns'].extend(matches)
        
        # 查找API URLs
        api_patterns = [
            r'https://api\.m\.jd\.com/[^"\'>\s]+',
            r'client\.action\?functionId=newBabelAwardCollection[^"\'>\s]*',
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['api_urls'].extend(matches)
        
        # 查找activityId
        activity_patterns = [
            r'activityId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'"activityId"\s*:\s*"([^"]+)"',
        ]
        
        for pattern in activity_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['activity_ids'].extend(matches)
        
        # 查找函数调用
        function_patterns = [
            r'newBabelAwardCollection\([^)]*\)',
            r'functionId["\']?\s*[:=]\s*["\']newBabelAwardCollection["\']',
        ]
        
        for pattern in function_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['function_calls'].extend(matches)
        
        # 查找key值
        key_patterns = [
            r'key["\']?\s*[:=]\s*["\']([A-F0-9]{64,})["\']',
            r'key=([A-F0-9]{64,})',
        ]
        
        for pattern in key_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['key_values'].extend(matches)
        
        # 查找roleId
        role_patterns = [
            r'roleId["\']?\s*[:=]\s*["\']([A-F0-9]{64,})["\']',
            r'roleId=([A-F0-9]{64,})',
        ]
        
        for pattern in role_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['role_ids'].extend(matches)
        
        # 查找strengthenKey
        strengthen_patterns = [
            r'strengthenKey["\']?\s*[:=]\s*["\']([A-F0-9]{64,})["\']',
            r'strengthenKey=([A-F0-9]{64,})',
        ]
        
        for pattern in strengthen_patterns:
            matches = re.findall(pattern, source_code, re.IGNORECASE)
            analysis['strengthen_keys'].extend(matches)
        
        # 去重
        for key in analysis:
            if isinstance(analysis[key], list):
                analysis[key] = list(set(analysis[key]))
        
        return analysis

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    extractor = JDPageSourceExtractor()
    result = extractor.extract_page_source(url)
    
    if result:
        print("\n=== 提取完成 ===")
        analysis = result['analysis_result']
        
        print(f"找到 {len(analysis['args_patterns'])} 个args模式")
        print(f"找到 {len(analysis['api_urls'])} 个API URLs")
        print(f"找到 {len(analysis['activity_ids'])} 个activityId")
        print(f"找到 {len(analysis['key_values'])} 个key值")
        print(f"找到 {len(analysis['role_ids'])} 个roleId")
        print(f"找到 {len(analysis['strengthen_keys'])} 个strengthenKey")
        
        # 显示找到的关键信息
        if analysis['args_patterns']:
            print("\n找到的args模式:")
            for i, pattern in enumerate(analysis['args_patterns'][:5], 1):
                print(f"  {i}. {pattern[:100]}...")
                
        if analysis['key_values']:
            print("\n找到的key值:")
            for i, key in enumerate(analysis['key_values'][:5], 1):
                print(f"  {i}. {key}")
                
        if analysis['role_ids']:
            print("\n找到的roleId:")
            for i, role_id in enumerate(analysis['role_ids'][:5], 1):
                print(f"  {i}. {role_id}")
                
        if analysis['strengthen_keys']:
            print("\n找到的strengthenKey:")
            for i, strengthen_key in enumerate(analysis['strengthen_keys'][:5], 1):
                print(f"  {i}. {strengthen_key}")
        
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
