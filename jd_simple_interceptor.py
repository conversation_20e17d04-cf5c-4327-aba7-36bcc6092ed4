#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东简单拦截器
通过JavaScript注入修改响应并捕获网络请求
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class JDSimpleInterceptor:
    def __init__(self):
        self.driver = None
        self.captured_requests = []
        
        # 京东APP User-Agent
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_driver(self):
        """设置浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用性能日志
        chrome_options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL'
        })
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def inject_response_modifier(self):
        """注入响应修改器"""
        modifier_script = """
        // 创建全局拦截器
        window._JD_INTERCEPTOR = {
            originalRequests: [],
            modifiedResponses: [],
            triggeredRequests: [],
            statusModified: false
        };
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            
            window._JD_INTERCEPTOR.originalRequests.push({
                type: 'xhr_open',
                method: method,
                url: url,
                timestamp: Date.now()
            });
            
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            
            // 记录发送的请求
            window._JD_INTERCEPTOR.originalRequests.push({
                type: 'xhr_send',
                method: this._method,
                url: this._url,
                data: data,
                timestamp: Date.now()
            });
            
            // 拦截响应
            const originalOnReadyStateChange = xhr.onreadystatechange;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4 && xhr.status === 200) {
                    try {
                        const responseText = xhr.responseText;
                        
                        // 检查是否需要修改响应
                        if (xhr._url && (xhr._url.includes('api.m.jd.com') || xhr._url.includes('babel'))) {
                            console.log('拦截到API响应:', xhr._url);
                            
                            try {
                                const responseJson = JSON.parse(responseText);
                                const modified = modifyStatusInResponse(responseJson);
                                
                                if (modified) {
                                    console.log('修改了响应status: 2 -> 5');
                                    window._JD_INTERCEPTOR.statusModified = true;
                                    
                                    // 记录修改
                                    window._JD_INTERCEPTOR.modifiedResponses.push({
                                        url: xhr._url,
                                        original: responseText,
                                        modified: JSON.stringify(responseJson),
                                        timestamp: Date.now()
                                    });
                                    
                                    // 替换响应内容
                                    Object.defineProperty(xhr, 'responseText', {
                                        value: JSON.stringify(responseJson),
                                        writable: false
                                    });
                                    
                                    Object.defineProperty(xhr, 'response', {
                                        value: JSON.stringify(responseJson),
                                        writable: false
                                    });
                                }
                            } catch (e) {
                                console.log('解析响应JSON失败:', e);
                            }
                        }
                    } catch (e) {
                        console.log('处理响应时出错:', e);
                    }
                }
                
                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.apply(xhr, arguments);
                }
            };
            
            return originalXHRSend.apply(this, [data]);
        };
        
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            console.log('Fetch请求:', url);
            
            window._JD_INTERCEPTOR.originalRequests.push({
                type: 'fetch',
                url: url,
                options: options,
                timestamp: Date.now()
            });
            
            return originalFetch.apply(this, arguments).then(response => {
                // 克隆响应以便修改
                const clonedResponse = response.clone();
                
                if (url.includes('api.m.jd.com') || url.includes('babel')) {
                    return clonedResponse.text().then(text => {
                        try {
                            const responseJson = JSON.parse(text);
                            const modified = modifyStatusInResponse(responseJson);
                            
                            if (modified) {
                                console.log('修改了Fetch响应status: 2 -> 5');
                                window._JD_INTERCEPTOR.statusModified = true;
                                
                                window._JD_INTERCEPTOR.modifiedResponses.push({
                                    url: url,
                                    original: text,
                                    modified: JSON.stringify(responseJson),
                                    timestamp: Date.now()
                                });
                                
                                // 返回修改后的响应
                                return new Response(JSON.stringify(responseJson), {
                                    status: response.status,
                                    statusText: response.statusText,
                                    headers: response.headers
                                });
                            }
                        } catch (e) {
                            console.log('解析Fetch响应JSON失败:', e);
                        }
                        
                        return response;
                    });
                }
                
                return response;
            });
        };
        
        // 修改响应中的status值
        function modifyStatusInResponse(obj) {
            let modified = false;
            
            function modifyRecursive(item) {
                if (typeof item === 'object' && item !== null) {
                    if (Array.isArray(item)) {
                        item.forEach(modifyRecursive);
                    } else {
                        for (const key in item) {
                            if (key === 'status' && item[key] === 2) {
                                console.log('发现status=2，修改为5');
                                item[key] = 5;
                                modified = true;
                            } else if (typeof item[key] === 'object') {
                                modifyRecursive(item[key]);
                            }
                        }
                    }
                }
            }
            
            modifyRecursive(obj);
            return modified;
        }
        
        // 监控点击事件
        document.addEventListener('click', function(event) {
            console.log('点击事件:', event.target);
            
            // 记录点击后的网络请求
            setTimeout(() => {
                const currentRequests = window._JD_INTERCEPTOR.originalRequests.length;
                console.log('点击后的请求数量:', currentRequests);
            }, 1000);
        }, true);
        
        // 获取拦截数据的函数
        window.getInterceptorData = function() {
            return window._JD_INTERCEPTOR;
        };
        
        console.log('响应修改器已注入');
        """
        
        self.driver.execute_script(modifier_script)
        
    def wait_for_page_load(self):
        """等待页面加载完成"""
        try:
            WebDriverWait(self.driver, 15).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 等待JavaScript执行
            time.sleep(3)
            
            # 滚动页面确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            return True
        except Exception as e:
            print(f"等待页面加载失败: {e}")
            return False
            
    def find_and_click_elements(self):
        """查找并点击相关元素"""
        clicked_elements = []
        
        # 查找包含特定文本的元素
        target_texts = ["预约", "领券", "立即预约", "马上预约", "去预约", "领取", "抢购", "立即领取"]
        
        for text in target_texts:
            try:
                # 使用XPath查找包含文本的元素
                elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                
                for element in elements[:2]:  # 每种文本最多点击2个元素
                    try:
                        if element.is_displayed() and element.is_enabled():
                            # 获取点击前的拦截数据
                            before_data = self.driver.execute_script("return window.getInterceptorData ? window.getInterceptorData() : null;")
                            
                            # 滚动到元素位置
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(0.5)
                            
                            print(f"点击元素: {text} - {element.tag_name}")
                            
                            # 点击元素
                            try:
                                element.click()
                            except:
                                self.driver.execute_script("arguments[0].click();", element)
                            
                            # 等待响应
                            time.sleep(3)
                            
                            # 获取点击后的拦截数据
                            after_data = self.driver.execute_script("return window.getInterceptorData ? window.getInterceptorData() : null;")
                            
                            clicked_elements.append({
                                'text': text,
                                'element': element.tag_name,
                                'before_requests': len(before_data['originalRequests']) if before_data else 0,
                                'after_requests': len(after_data['originalRequests']) if after_data else 0,
                                'status_modified': after_data['statusModified'] if after_data else False
                            })
                            
                            # 检查是否有新的请求
                            if after_data and before_data:
                                new_requests_count = len(after_data['originalRequests']) - len(before_data['originalRequests'])
                                if new_requests_count > 0:
                                    print(f"点击后触发了 {new_requests_count} 个新请求")
                                    
                                if after_data['statusModified'] and not before_data['statusModified']:
                                    print("✅ 成功修改了响应status!")
                            
                    except Exception as e:
                        print(f"点击元素失败: {e}")
                        continue
                        
            except Exception as e:
                print(f"查找元素失败 {text}: {e}")
                continue
                
        return clicked_elements
        
    def get_final_results(self):
        """获取最终结果"""
        try:
            interceptor_data = self.driver.execute_script("return window.getInterceptorData ? window.getInterceptorData() : null;")
            
            if interceptor_data:
                return {
                    'original_requests': interceptor_data['originalRequests'],
                    'modified_responses': interceptor_data['modifiedResponses'],
                    'triggered_requests': interceptor_data['triggeredRequests'],
                    'status_modified': interceptor_data['statusModified'],
                    'total_requests': len(interceptor_data['originalRequests']),
                    'total_modifications': len(interceptor_data['modifiedResponses'])
                }
            else:
                return None
                
        except Exception as e:
            print(f"获取最终结果失败: {e}")
            return None
            
    def run_interception(self, url):
        """运行拦截"""
        try:
            print("=== 启动简单拦截器 ===")
            
            # 设置驱动
            self.setup_driver()
            
            # 加载页面
            print(f"正在加载页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            if not self.wait_for_page_load():
                print("页面加载失败")
                return None
                
            # 注入响应修改器
            print("注入响应修改器...")
            self.inject_response_modifier()
            time.sleep(2)
            
            # 查找并点击元素
            print("查找并点击相关元素...")
            clicked_elements = self.find_and_click_elements()
            
            # 等待一段时间让所有请求完成
            print("等待请求完成...")
            time.sleep(5)
            
            # 获取最终结果
            final_results = self.get_final_results()
            
            if final_results:
                final_results['clicked_elements'] = clicked_elements
                
            return final_results
            
        except Exception as e:
            print(f"拦截过程中出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    interceptor = JDSimpleInterceptor()
    result = interceptor.run_interception(url)
    
    if result:
        print("\n=== 拦截结果 ===")
        print(f"总请求数: {result['total_requests']}")
        print(f"修改的响应数: {result['total_modifications']}")
        print(f"状态是否被修改: {result['status_modified']}")
        print(f"点击的元素数: {len(result['clicked_elements'])}")
        
        # 保存结果
        with open('jd_simple_interception_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("详细结果已保存到 jd_simple_interception_result.json")
        
        # 显示修改的响应
        if result['modified_responses']:
            print("\n=== 修改的响应 ===")
            for i, resp in enumerate(result['modified_responses'], 1):
                print(f"{i}. URL: {resp['url']}")
                print(f"   时间: {resp['timestamp']}")
                
        # 显示关键请求
        if result['original_requests']:
            print("\n=== 关键请求 ===")
            api_requests = [req for req in result['original_requests'] if 'api.m.jd.com' in req.get('url', '')]
            for i, req in enumerate(api_requests[:5], 1):
                print(f"{i}. {req['type']}: {req['url']}")
                if 'data' in req and req['data']:
                    print(f"   数据: {str(req['data'])[:100]}...")
                    
    else:
        print("拦截失败")

if __name__ == "__main__":
    main()
