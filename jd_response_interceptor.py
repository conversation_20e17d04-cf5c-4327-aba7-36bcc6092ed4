#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东响应拦截器
修改响应status从2改为5，触发真实网络请求并捕获
"""

import json
import time
import asyncio
import websockets
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import requests
from urllib.parse import unquote

class JDResponseInterceptor:
    def __init__(self):
        self.driver = None
        self.websocket = None
        self.intercepted_requests = []
        self.modified_responses = []
        self.triggered_requests = []
        
        # 京东APP User-Agent
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_interceptor_driver(self):
        """设置拦截器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用远程调试
        chrome_options.add_argument('--remote-debugging-port=9223')
        
        # 启用详细日志
        chrome_options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL'
        })
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 启用CDP域
        self.driver.execute_cdp_cmd('Network.enable', {})
        self.driver.execute_cdp_cmd('Runtime.enable', {})
        self.driver.execute_cdp_cmd('Fetch.enable', {
            'patterns': [{'urlPattern': '*', 'requestStage': 'Response'}]
        })
        
    async def connect_to_devtools(self):
        """连接到Chrome DevTools WebSocket"""
        try:
            response = requests.get('http://localhost:9223/json')
            tabs = response.json()
            
            if tabs:
                websocket_url = tabs[0]['webSocketDebuggerUrl']
                self.websocket = await websockets.connect(websocket_url)
                print(f"已连接到DevTools WebSocket: {websocket_url}")
                
                # 启用网络和Fetch域
                await self.websocket.send(json.dumps({
                    "id": 1,
                    "method": "Network.enable"
                }))
                
                await self.websocket.send(json.dumps({
                    "id": 2,
                    "method": "Fetch.enable",
                    "params": {
                        "patterns": [{"urlPattern": "*", "requestStage": "Response"}]
                    }
                }))
                
                return True
        except Exception as e:
            print(f"连接DevTools失败: {e}")
            return False
            
    async def monitor_and_modify_responses(self):
        """监控并修改响应"""
        if not self.websocket:
            return
            
        try:
            while True:
                message = await self.websocket.recv()
                event = json.loads(message)
                
                if 'method' in event:
                    await self.handle_fetch_event(event)
                    
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket连接已关闭")
        except Exception as e:
            print(f"监控响应时出错: {e}")
            
    async def handle_fetch_event(self, event):
        """处理Fetch事件"""
        method = event['method']
        params = event.get('params', {})
        
        if method == 'Fetch.requestPaused':
            request_id = params['requestId']
            request = params['request']
            
            print(f"拦截到请求: {request['url']}")
            
            # 检查是否是需要修改的响应
            if self.should_modify_response(request['url']):
                print(f"准备修改响应: {request['url']}")
                
                # 获取原始响应
                try:
                    # 继续请求以获取响应
                    await self.websocket.send(json.dumps({
                        "id": int(time.time()),
                        "method": "Fetch.continueRequest",
                        "params": {"requestId": request_id}
                    }))
                    
                    # 等待响应
                    await asyncio.sleep(1)
                    
                    # 获取响应体
                    await self.websocket.send(json.dumps({
                        "id": int(time.time()),
                        "method": "Fetch.getResponseBody",
                        "params": {"requestId": request_id}
                    }))
                    
                except Exception as e:
                    print(f"处理响应时出错: {e}")
            else:
                # 不需要修改，直接继续
                await self.websocket.send(json.dumps({
                    "id": int(time.time()),
                    "method": "Fetch.continueRequest",
                    "params": {"requestId": request_id}
                }))
                
        elif method == 'Network.responseReceived':
            response = params['response']
            request_id = params['requestId']
            
            # 检查是否是需要修改的响应
            if self.should_modify_response(response['url']):
                await self.modify_response_content(request_id, response)
                
    def should_modify_response(self, url):
        """判断是否需要修改响应"""
        # 检查是否是京东API响应
        modify_patterns = [
            'api.m.jd.com',
            'babelGetGuideTips',
            'newBabelAwardCollection',
            'babelAwardCollection'
        ]
        
        return any(pattern in url for pattern in modify_patterns)
        
    async def modify_response_content(self, request_id, response):
        """修改响应内容"""
        try:
            # 获取响应体
            await self.websocket.send(json.dumps({
                "id": int(time.time()),
                "method": "Network.getResponseBody",
                "params": {"requestId": request_id}
            }))
            
            # 等待响应
            response_msg = await self.websocket.recv()
            response_data = json.loads(response_msg)
            
            if 'result' in response_data and 'body' in response_data['result']:
                original_body = response_data['result']['body']
                
                try:
                    # 解析JSON响应
                    response_json = json.loads(original_body)
                    
                    # 修改status从2改为5
                    if self.modify_status_in_response(response_json):
                        modified_body = json.dumps(response_json, separators=(',', ':'))
                        
                        print(f"修改响应成功:")
                        print(f"原始: {original_body[:200]}...")
                        print(f"修改: {modified_body[:200]}...")
                        
                        # 记录修改
                        self.modified_responses.append({
                            'url': response['url'],
                            'original': original_body,
                            'modified': modified_body,
                            'timestamp': time.time()
                        })
                        
                        # 使用修改后的响应
                        await self.websocket.send(json.dumps({
                            "id": int(time.time()),
                            "method": "Fetch.fulfillRequest",
                            "params": {
                                "requestId": request_id,
                                "responseCode": 200,
                                "responseHeaders": [
                                    {"name": "Content-Type", "value": "application/json"}
                                ],
                                "body": modified_body
                            }
                        }))
                        
                except json.JSONDecodeError:
                    print("响应不是有效的JSON格式")
                    
        except Exception as e:
            print(f"修改响应内容时出错: {e}")
            
    def modify_status_in_response(self, response_json):
        """在响应中修改status值"""
        modified = False
        
        def modify_recursive(obj):
            nonlocal modified
            if isinstance(obj, dict):
                for key, value in obj.items():
                    if key == 'status' and value == 2:
                        obj[key] = 5
                        modified = True
                        print(f"修改 status: 2 -> 5")
                    elif isinstance(value, (dict, list)):
                        modify_recursive(value)
            elif isinstance(obj, list):
                for item in obj:
                    if isinstance(item, (dict, list)):
                        modify_recursive(item)
        
        modify_recursive(response_json)
        return modified
        
    def inject_click_monitor(self):
        """注入点击监控脚本"""
        monitor_script = """
        // 监控所有点击事件
        window._CLICK_MONITOR = {
            clicks: [],
            requests: []
        };
        
        // 拦截所有点击事件
        document.addEventListener('click', function(event) {
            console.log('点击事件:', event.target);
            
            window._CLICK_MONITOR.clicks.push({
                target: event.target.tagName,
                className: event.target.className,
                id: event.target.id,
                text: event.target.textContent,
                timestamp: Date.now()
            });
        }, true);
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            console.log('XHR请求:', method, url);
            
            window._CLICK_MONITOR.requests.push({
                type: 'xhr',
                method: method,
                url: url,
                timestamp: Date.now()
            });
            
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            console.log('Fetch请求:', url, options);
            
            window._CLICK_MONITOR.requests.push({
                type: 'fetch',
                url: url,
                options: options,
                timestamp: Date.now()
            });
            
            return originalFetch.apply(this, arguments);
        };
        
        // 获取监控数据的函数
        window.getClickMonitorData = function() {
            return window._CLICK_MONITOR;
        };
        
        console.log('点击监控已启用');
        """
        
        self.driver.execute_script(monitor_script)
        
    def trigger_clicks_and_monitor(self):
        """触发点击并监控网络请求"""
        try:
            print("正在查找可点击元素...")
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 滚动页面确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # 查找包含"预约"、"领券"等文本的元素
            target_texts = ["预约", "领券", "立即预约", "马上预约", "去预约", "领取", "抢购"]
            
            clicked_elements = []
            
            for text in target_texts:
                try:
                    # 查找包含特定文本的元素
                    elements = self.driver.find_elements(By.XPATH, f"//*[contains(text(), '{text}')]")
                    
                    for element in elements:
                        try:
                            if element.is_displayed() and element.is_enabled():
                                # 滚动到元素位置
                                self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                                time.sleep(0.5)
                                
                                print(f"点击元素: {text} - {element.tag_name}")
                                
                                # 点击前获取监控数据
                                before_data = self.driver.execute_script("return window.getClickMonitorData ? window.getClickMonitorData() : null;")
                                
                                # 点击元素
                                try:
                                    element.click()
                                except:
                                    self.driver.execute_script("arguments[0].click();", element)
                                
                                time.sleep(2)
                                
                                # 点击后获取监控数据
                                after_data = self.driver.execute_script("return window.getClickMonitorData ? window.getClickMonitorData() : null;")
                                
                                clicked_elements.append({
                                    'text': text,
                                    'element': element.tag_name,
                                    'before_requests': len(before_data['requests']) if before_data else 0,
                                    'after_requests': len(after_data['requests']) if after_data else 0
                                })
                                
                                # 检查是否有新的网络请求
                                if after_data and before_data:
                                    new_requests = after_data['requests'][len(before_data['requests']):]
                                    if new_requests:
                                        print(f"点击后触发了 {len(new_requests)} 个新请求:")
                                        for req in new_requests:
                                            print(f"  {req['type']}: {req['url']}")
                                            self.triggered_requests.append(req)
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
                    
            print(f"总共点击了 {len(clicked_elements)} 个元素")
            return clicked_elements
            
        except Exception as e:
            print(f"触发点击时出错: {e}")
            return []
            
    async def run_response_interception(self, url):
        """运行响应拦截"""
        try:
            print("=== 启动响应拦截器 ===")
            
            # 设置驱动
            self.setup_interceptor_driver()
            
            # 连接DevTools
            connected = await self.connect_to_devtools()
            if not connected:
                print("无法连接到DevTools")
                return None
            
            # 启动响应监控任务
            monitor_task = asyncio.create_task(self.monitor_and_modify_responses())
            
            # 加载页面
            print(f"正在加载页面: {url}")
            self.driver.get(url)
            time.sleep(5)
            
            # 注入点击监控
            self.inject_click_monitor()
            
            # 触发点击并监控
            clicked_elements = self.trigger_clicks_and_monitor()
            
            # 继续监控一段时间
            print("继续监控网络活动...")
            await asyncio.sleep(10)
            
            # 停止监控
            monitor_task.cancel()
            
            # 分析结果
            result = {
                'modified_responses': self.modified_responses,
                'triggered_requests': self.triggered_requests,
                'clicked_elements': clicked_elements,
                'total_modifications': len(self.modified_responses),
                'total_triggered_requests': len(self.triggered_requests)
            }
            
            return result
            
        except Exception as e:
            print(f"响应拦截过程中出错: {e}")
            return None
            
        finally:
            if self.websocket:
                await self.websocket.close()
            if self.driver:
                self.driver.quit()

def main():
    async def run_interceptor():
        url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
        
        interceptor = JDResponseInterceptor()
        result = await interceptor.run_response_interception(url)
        
        if result:
            print("\n=== 拦截结果 ===")
            print(f"修改的响应数量: {result['total_modifications']}")
            print(f"触发的请求数量: {result['total_triggered_requests']}")
            print(f"点击的元素数量: {len(result['clicked_elements'])}")
            
            # 保存结果
            with open('jd_response_interception_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print("详细结果已保存到 jd_response_interception_result.json")
            
            # 显示触发的请求
            if result['triggered_requests']:
                print("\n=== 触发的网络请求 ===")
                for i, req in enumerate(result['triggered_requests'], 1):
                    print(f"{i}. {req['type']}: {req['url']}")
                    
            # 显示修改的响应
            if result['modified_responses']:
                print("\n=== 修改的响应 ===")
                for i, resp in enumerate(result['modified_responses'], 1):
                    print(f"{i}. {resp['url']}")
                    
        else:
            print("拦截失败")
    
    # 运行异步拦截器
    asyncio.run(run_interceptor())

if __name__ == "__main__":
    main()
