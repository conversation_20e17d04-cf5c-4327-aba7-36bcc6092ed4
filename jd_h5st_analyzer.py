#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东h5st签名算法分析器
基于真实网络请求分析h5st参数生成算法
"""

import json
import time
import re
import hashlib
import hmac
import base64
from urllib.parse import unquote, quote
import requests

class JDH5STAnalyzer:
    def __init__(self):
        # 从真实请求中提取的h5st样本
        self.h5st_samples = [
            "20250605021939340%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w994ae57d41lMSsyKzErMnFssmeh_6oSBNIUHJ4i7K4R2JrSuueuX87kqYaTMZra1Sog2mbhfZqh4mog4urg4KLh%3B21b69aaa078abe0cdb2b822e3ea35ebe3398777013115863a90f21bee1350ad3%3B5.1%3B1749061174340%3Bri_uxFOm5ubT_5oUERYU7ibU3NXg_lsm0msSIlsmOGuj8qrm0mMTLhImOuMsCmciKZYi1qrg5mYV9OLV9abi3urg9iLiMZbW1arh7urh1msm0msSo94VMZ4RMusmk_Mm1e4h1Kri5iYVJd7i7uLV6OLiMhrh1Krg8ioi3mLiIdImOGLm7pIRAp4WMusmk_siOGLm6aHWMusmk_Mm52ciAaLRPZoTFV4X5OImOGLm4lsmOGujMKKaalng8KYYBlsm0mcT-dITNlHmOuMsCmMi72YUXlsm0mMV_lsmOGujxtsmkmrm0mci9aHWMusmOuMsCqbiOGLm_qbRMlsmOusmk_sgBuMgMmbi5lImOusmOGujMqXhnd6hMtbZ8S4aKlsm0mcT-dITNlHmOusmOGuj_uMgMObRMlsmOusmk_siOGLm3aHWMusmOuMsCmLiOGLm4aHWMusmOuMsCurm0mch5lImOusmOGuj_uMgMebRMlsmOusmk_ci3uMgMibRMlsmOusmk_Mm52ciAu7XJdXUClsm0mci5lImOusmOGuj9uMgMC4RMusmOuMsCarm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPztMgMunSMusmk_MmMuMgMqYR7lsmOG_Q%3Bedfc4618da1d6c2fbd5081bc4ccd3ef2e705d161023b3dc83eb7a3b0cf5d0df7%3Bri_uKJKT-JoRL1YRI9cQKxIWCeYU_tXW",
            "20250605021939346%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w5a896dce41lMngxZFAxMlBismeh_6oSBNIUHJ4i7K4R2JrSuueuX87kqEKi-tXjelJTBm7UCxqh4urV9qrgJdIW%3Beeba83fe517546485c3e66ddff6fa54ba4866cc022d444e90267476f96d05813%3B5.1%3B1749061174346%3Bri_uxFOm5ubT_5oUERYU7ibU3NXg_lsm0msSIlsmOGuj8qrm0mMTLhImOuMsCmciKZYi1qrg5mYV9OLV9abi3urg9iLiMZbW1arh7urh1msm0msSo94VMZ4RMusmk_Mm1e4h1Kri5iYVJd7i7uLV6OLiMhrh1Krg8ioi3mLiIdImOGLm7pIRAp4WMusmk_siOGLm6aHWMusmk_Mm52ciAaLRPZoTFV4X5OImOGLm4lsmOGujMu5SM5Kb8mpdDlsm0mcT-dITNlHmOuMsCmMi72YUXlsm0mMV_lsmOGujxtsmkmrm0mci9aHWMusmOuMsCqbiOGLm_qbRMlsmOusmk_sgBuMgMmbi5lImOusmOGujMKqi8W7cidJW8C6VKlsm0mcT-dITNlHmOusmOGuj_uMgMObRMlsmOusmk_siOGLm3aHWMusmOuMsCmLiOGLm4aHWMusmOuMsCurm0mch5lImOusmOGuj_uMgMebRMlsmOusmk_Mi3uMgMibRMlsmOusmk_Mm52ciAu7XJdXUClsm0mci5lImOusmOGuj9uMgMC4RMusmOuMsCarm0m8SClsmOusmk_siOGLmClsmOusmk_siOGLmKRHmOusmOG_QOGLmK1YV6NXVMusmk_cPztMgMunSMusmk_MmMuMgMqYR7lsmOG_Q%3B81947d13127d10b705c2b6913501ee1e23e664dba2891191e371fc7ff90e9d14%3Bri_uKJKT-JoRL1YRI9MT-J4S8ZIZ61YVF94WCeHTJJoTL9cQKxIWCeYU_tXW"
        ]

        # 从真实请求中提取的关键信息
        self.activity_id = "4P9a2T9osR9JvtzHVaYTPvsecRtg"
        self.app_id = "babelh5"
        self.function_id = "babelGetGuideTips"

        # 设备指纹信息
        self.device_info = {
            "shshshfpa": "ebfc99d8-68e9-503d-ecd0-20e126972cc3-1749061174",
            "browser_info": "3401658ed42177bf06e212ad9720768f",
            "jdu": "17490611722851941819610",
            "mba_muid": "17490611722851941819610.1.1749061172286"
        }

    def decode_h5st(self, h5st_encoded):
        """解码h5st参数"""
        try:
            # URL解码
            h5st_decoded = unquote(h5st_encoded)
            print(f"解码后的h5st: {h5st_decoded}")

            # 分析h5st结构
            parts = h5st_decoded.split(';')
            if len(parts) >= 7:
                result = {
                    'timestamp': parts[0],
                    'fingerprint': parts[1],
                    'version': parts[2],
                    'token': parts[3],
                    'signature1': parts[4],
                    'sdk_version': parts[5],
                    'timestamp2': parts[6],
                    'encrypted_data': parts[7] if len(parts) > 7 else '',
                    'signature2': parts[8] if len(parts) > 8 else '',
                    'device_id': parts[9] if len(parts) > 9 else ''
                }

                print("h5st结构分析:")
                for key, value in result.items():
                    print(f"  {key}: {value[:50]}..." if len(str(value)) > 50 else f"  {key}: {value}")

                return result
            else:
                print(f"h5st格式不符合预期，部分数量: {len(parts)}")
                return None

        except Exception as e:
            print(f"解码h5st失败: {e}")
            return None

    def analyze_h5st_patterns(self):
        """分析h5st模式"""
        print("=== h5st模式分析 ===")

        decoded_samples = []
        for i, sample in enumerate(self.h5st_samples):
            print(f"\n样本 {i+1}:")
            decoded = self.decode_h5st(sample)
            if decoded:
                decoded_samples.append(decoded)

        # 比较样本差异
        if len(decoded_samples) >= 2:
            print(f"\n=== 样本对比分析 ===")
            sample1, sample2 = decoded_samples[0], decoded_samples[1]

            for key in sample1.keys():
                val1, val2 = sample1[key], sample2[key]
                if val1 != val2:
                    print(f"{key}: 不同")
                    print(f"  样本1: {val1}")
                    print(f"  样本2: {val2}")
                else:
                    print(f"{key}: 相同")

        return decoded_samples

    def extract_token_algorithm(self):
        """提取token生成算法"""
        print("\n=== Token算法分析 ===")

        # 从样本中提取token部分
        tokens = []
        for sample in self.h5st_samples:
            decoded = self.decode_h5st(sample)
            if decoded and 'token' in decoded:
                tokens.append(decoded['token'])

        print("发现的token:")
        for i, token in enumerate(tokens):
            print(f"  Token {i+1}: {token}")

            # 分析token结构
            if token.startswith('tk'):
                print(f"    类型: tk类型token")
                print(f"    长度: {len(token)}")

                # 尝试解析token内容
                self.analyze_token_content(token)

    def analyze_token_content(self, token):
        """分析token内容"""
        try:
            # token通常包含时间戳、设备信息等
            # 分析可能的编码模式

            # 检查是否包含base64编码部分
            if '_' in token:
                parts = token.split('_')
                print(f"    Token部分: {len(parts)} 个")
                for i, part in enumerate(parts):
                    print(f"      部分{i+1}: {part}")

                    # 尝试base64解码
                    try:
                        if len(part) % 4 == 0:
                            decoded = base64.b64decode(part + '==')
                            print(f"        Base64解码: {decoded}")
                    except:
                        pass

        except Exception as e:
            print(f"    分析token内容失败: {e}")

    def generate_h5st_signature(self, timestamp, body, function_id):
        """尝试生成h5st签名"""
        print(f"\n=== 尝试生成h5st签名 ===")

        # 基于分析的模式尝试生成
        fingerprint = "p9xwi33igjhmpm05"  # 从样本中提取
        version = "35fa0"  # 从样本中提取

        # 生成token（简化版本）
        token_base = f"tk05w{hashlib.md5(str(timestamp).encode()).hexdigest()[:10]}"

        # 构造签名输入
        sign_input = f"{timestamp};{fingerprint};{version};{token_base};{function_id};{body}"

        # 生成签名
        signature1 = hashlib.md5(sign_input.encode()).hexdigest()

        # 构造h5st
        h5st_parts = [
            str(timestamp),
            fingerprint,
            version,
            token_base,
            signature1,
            "5.1",
            str(timestamp),
            "encrypted_data_placeholder",
            "signature2_placeholder",
            "device_id_placeholder"
        ]

        h5st = ';'.join(h5st_parts)
        h5st_encoded = quote(h5st)

        print(f"生成的h5st: {h5st}")
        print(f"编码后: {h5st_encoded}")

        return h5st_encoded

    def find_real_newbabel_requests(self):
        """查找真实的newBabelAwardCollection请求"""
        print("\n=== 查找真实的newBabelAwardCollection请求 ===")

        # 基于已知的API模式，构造可能的请求
        possible_endpoints = [
            "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection",
            "https://api.m.jd.com/?functionId=newBabelAwardCollection",
            "https://api.m.jd.com/api?functionId=newBabelAwardCollection"
        ]

        # 尝试不同的参数组合
        test_bodies = [
            {"activityId": self.activity_id, "scene": "1"},
            {"activityId": self.activity_id, "scene": "1", "floorId": "117554838"},
            {"activityId": self.activity_id, "scene": "1", "args": "test"}
        ]

        for endpoint in possible_endpoints:
            for body in test_bodies:
                print(f"\n测试: {endpoint}")
                print(f"Body: {body}")

                # 生成h5st
                timestamp = int(time.time() * 1000)
                h5st = self.generate_h5st_signature(timestamp, json.dumps(body), "newBabelAwardCollection")

                # 构造完整URL
                body_encoded = quote(json.dumps(body, separators=(',', ':')))
                full_url = f"{endpoint}&client=wh5&body={body_encoded}&h5st={h5st}"

                print(f"完整URL: {full_url[:100]}...")

    def create_real_request_template(self):
        """创建真实请求模板"""
        print("\n=== 创建真实请求模板 ===")

        # 基于真实监控到的请求结构
        template = {
            "url": "https://api.m.jd.com/client.action",
            "method": "POST",
            "headers": {
                "User-Agent": "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Babel-ActId": self.activity_id,
                "x-referer-page": f"https://pro.m.jd.com/mall/active/{self.activity_id}/index.html",
                "x-rp-client": "h5_1.0.0"
            },
            "params": {
                "functionId": "newBabelAwardCollection",
                "client": "wh5",
                "clientVersion": "1.0.0"
            },
            "body_template": {
                "activityId": self.activity_id,
                "scene": "1",
                "args": "key={key}_bingo,roleId={roleId}_bingo,strengthenKey={strengthenKey}_bingo"
            }
        }

        return template

def main():
    analyzer = JDH5STAnalyzer()

    print("=== 京东h5st签名算法分析 ===")

    # 分析h5st模式
    decoded_samples = analyzer.analyze_h5st_patterns()

    # 提取token算法
    analyzer.extract_token_algorithm()

    # 查找真实请求
    analyzer.find_real_newbabel_requests()

    # 创建请求模板
    template = analyzer.create_real_request_template()

    print(f"\n=== 请求模板 ===")
    print(json.dumps(template, indent=2, ensure_ascii=False))

    # 保存分析结果
    result = {
        'decoded_samples': decoded_samples,
        'request_template': template,
        'analysis_time': time.time()
    }

    with open('jd_h5st_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(result, f, indent=2, ensure_ascii=False)

    print(f"\n分析结果已保存到 jd_h5st_analysis.json")

if __name__ == "__main__":
    main()