# 京东预约抢购API文档

## 📋 API概览

### 主要类：`JDReadyAPI`

完整的京东预约抢购API，包含所有必要功能。

## 🚀 快速开始

```python
from jd_ready_api import JDReadyAPI

# 创建API实例
api = JDReadyAPI()

# 预约单个商品
result = api.reserve_product("1")

# 预约所有商品
results = api.reserve_all_products()
```

## 📖 详细API文档

### 1. 初始化

```python
api = JDReadyAPI(cookie=None)
```

**参数：**
- `cookie` (str, 可选): 京东登录Cookie，如果不提供则使用默认Cookie

**示例：**
```python
# 使用默认Cookie
api = JDReadyAPI()

# 使用自定义Cookie
api = JDReadyAPI(cookie="your_cookie_here")
```

### 2. 预约单个商品

```python
result = api.reserve_product(product_num)
```

**参数：**
- `product_num` (str): 商品编号，可选值："1", "2", "3", "4"

**返回值：**
```python
{
    "success": True,                    # 请求是否成功
    "product_name": "商品1",            # 商品名称
    "floor_id": "117554838",           # 楼层ID
    "status_code": 200,                # HTTP状态码
    "timestamp": 1749064352669,        # 请求时间戳
    "api_success": True,               # API调用是否成功
    "code": "0",                       # API响应码
    "subCode": "A28",                  # 业务状态码
    "message": "很抱歉，没抢到~",        # 提示信息
    "status": "没有抢到",               # 预约状态
    "description": "活动可能未开始或商品已抢完",  # 状态描述
    "response": {...}                  # 完整响应数据
}
```

**状态码说明：**
- `A01`: 预约成功
- `A28`: 没有抢到
- `A02`: 活动未开始
- `A03`: 活动已结束

**示例：**
```python
# 预约商品1
result = api.reserve_product("1")

if result["success"]:
    print(f"商品: {result['product_name']}")
    print(f"状态: {result['status']}")
    print(f"说明: {result['description']}")
else:
    print(f"预约失败: {result['error']}")
```

### 3. 预约所有商品

```python
results = api.reserve_all_products(delay=2)
```

**参数：**
- `delay` (int, 可选): 请求间隔时间（秒），默认2秒

**返回值：**
```python
{
    "1": {...},  # 商品1的预约结果
    "2": {...},  # 商品2的预约结果
    "3": {...},  # 商品3的预约结果
    "4": {...}   # 商品4的预约结果
}
```

**示例：**
```python
# 预约所有商品，间隔3秒
results = api.reserve_all_products(delay=3)

# 统计结果
success_count = 0
for product_num, result in results.items():
    if result["success"] and result.get("api_success"):
        if result.get("subCode") == "A01":
            success_count += 1

print(f"成功预约 {success_count} 个商品")
```

### 4. 获取商品信息

```python
info = api.get_product_info()
```

**返回值：**
```python
{
    "activity_id": "4P9a2T9osR9JvtzHVaYTPvsecRtg",
    "products": {
        "1": {"floor_id": "117554838", "name": "商品1"},
        "2": {"floor_id": "117554839", "name": "商品2"},
        "3": {"floor_id": "117554841", "name": "商品3"},
        "4": {"floor_id": "117554842", "name": "商品4"}
    },
    "user": "jd_4b25e12eb2177"
}
```

**示例：**
```python
info = api.get_product_info()
print(f"活动ID: {info['activity_id']}")
for num, product in info["products"].items():
    print(f"{num}. {product['name']} (楼层ID: {product['floor_id']})")
```

### 5. 设置Cookie

```python
api.set_cookie(cookie)
```

**参数：**
- `cookie` (str): 新的京东登录Cookie

**示例：**
```python
new_cookie = "pinId=xxx; pin=xxx; ..."
api.set_cookie(new_cookie)
```

## 🎯 完整使用示例

### 基础使用

```python
from jd_ready_api import JDReadyAPI

# 1. 创建API实例
api = JDReadyAPI()

# 2. 查看商品信息
products = api.get_product_info()
print("可用商品:")
for num, product in products["products"].items():
    print(f"  {num}. {product['name']}")

# 3. 预约单个商品
result = api.reserve_product("1")
print(f"预约结果: {result['status']}")
```

### 批量预约

```python
from jd_ready_api import JDReadyAPI

# 创建API实例
api = JDReadyAPI()

# 预约所有商品
print("开始批量预约...")
results = api.reserve_all_products(delay=2)

# 分析结果
success_products = []
failed_products = []

for product_num, result in results.items():
    if result["success"] and result.get("api_success"):
        if result.get("subCode") == "A01":
            success_products.append(result["product_name"])
        else:
            failed_products.append(f"{result['product_name']}: {result['status']}")
    else:
        failed_products.append(f"{result['product_name']}: 请求失败")

print(f"\n=== 预约结果汇总 ===")
print(f"成功预约: {len(success_products)} 个")
for product in success_products:
    print(f"  ✅ {product}")

print(f"未成功预约: {len(failed_products)} 个")
for product in failed_products:
    print(f"  ❌ {product}")
```

### 自定义Cookie使用

```python
from jd_ready_api import JDReadyAPI

# 使用自定义Cookie
my_cookie = "pinId=xxx; pin=xxx; unick=xxx; ..."
api = JDReadyAPI(cookie=my_cookie)

# 或者后续设置
api = JDReadyAPI()
api.set_cookie(my_cookie)

# 预约商品
result = api.reserve_product("1")
```

### 定时预约

```python
import time
from datetime import datetime
from jd_ready_api import JDReadyAPI

def scheduled_reserve():
    """定时预约函数"""
    api = JDReadyAPI()
    
    # 设置预约时间（例如：每天10:00）
    target_time = "10:00:00"
    
    while True:
        current_time = datetime.now().strftime("%H:%M:%S")
        
        if current_time == target_time:
            print(f"开始预约 - {datetime.now()}")
            results = api.reserve_all_products()
            
            # 检查是否有成功的预约
            success = any(
                r.get("api_success") and r.get("subCode") == "A01" 
                for r in results.values() if r.get("success")
            )
            
            if success:
                print("🎉 预约成功！停止监控")
                break
            else:
                print("⏰ 未抢到，继续监控...")
        
        time.sleep(1)  # 每秒检查一次

# 运行定时预约
# scheduled_reserve()
```

## 🔧 错误处理

### 常见错误及处理

```python
from jd_ready_api import JDReadyAPI

api = JDReadyAPI()

try:
    result = api.reserve_product("1")
    
    if not result["success"]:
        print(f"请求失败: {result['error']}")
    elif not result.get("api_success"):
        print(f"API调用失败: {result.get('description', '未知错误')}")
    else:
        print(f"预约状态: {result['status']}")
        
except Exception as e:
    print(f"程序异常: {e}")
```

### 网络重试机制

```python
import time
from jd_ready_api import JDReadyAPI

def reserve_with_retry(api, product_num, max_retries=3):
    """带重试机制的预约"""
    for attempt in range(max_retries):
        try:
            result = api.reserve_product(product_num)
            
            if result["success"]:
                return result
            else:
                print(f"第{attempt+1}次尝试失败: {result.get('error', '未知错误')}")
                
        except Exception as e:
            print(f"第{attempt+1}次尝试异常: {e}")
        
        if attempt < max_retries - 1:
            time.sleep(2)  # 重试间隔
    
    return {"success": False, "error": "重试次数已用完"}

# 使用示例
api = JDReadyAPI()
result = reserve_with_retry(api, "1", max_retries=3)
```

## 📊 响应状态码参考

| 状态码 | 含义 | 说明 |
|--------|------|------|
| A01 | 预约成功 | 恭喜！成功预约商品 |
| A28 | 没有抢到 | 活动可能未开始或商品已抢完 |
| A02 | 活动未开始 | 活动还未开始，请稍后再试 |
| A03 | 活动已结束 | 活动已经结束 |
| 其他 | 未知状态 | 需要进一步分析 |

## 🎯 最佳实践

1. **合理设置请求间隔**：避免请求过于频繁被限制
2. **错误处理**：始终检查返回结果的success字段
3. **Cookie管理**：定期更新Cookie保持登录状态
4. **日志记录**：记录预约结果便于分析
5. **时间控制**：在活动开始时间进行预约

## 📝 注意事项

- 确保Cookie有效且未过期
- 网络环境稳定，避免请求超时
- 遵守京东的使用条款和频率限制
- 建议在活动开始前测试API可用性
