# 京东预约抢购 code:-1 问题完整解决方案

## 🚨 问题描述
登录后请求返回 `{"code":"-1"}`，表示参数验证失败。

## ✅ 解决方案

### 1. 使用最新生成的动态参数

我已经生成了基于实时时间戳和设备指纹的动态参数，这些参数更接近真实的算法：

#### 商品1完整API
```
https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%224P9a2T9osR9JvtzHVaYTPvsecRtg%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3D781E1074EB7BAEBAD155F319E5CBF16B_bingo%2CroleId%3D911E376E79D996AB33F08D32BEA890C4CD7E90DBCA90B2DACD2ABC81ADBA679E7BB1ABE8925E2EAC306910FC8EE2792AFDB061AB94104D80C69F17824C771D0971ABC74782E41FA81786BE4D65CB58EDEEDFCB09CFDA586182FC1BB52D39749702A3B82A4D17464793D556974A882735243A72CD61A203FE128B2DD1F2D364A219E2056DBFF6CAC898E53B3C833628E5D6B9ABEFD3CC8C7F58A016669DED19AF_bingo%2CstrengthenKey%3D15475342C5AF0AABE9B01407095F03823B0EB5BA0E90673E82947211C9DBFCEB_bingo%22%7D
```

#### 商品2完整API
```
https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%224P9a2T9osR9JvtzHVaYTPvsecRtg%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3DA9F7EBDF0908ABD7EA7F5E913EA21676_bingo%2CroleId%3DAC5D2C23D187BECBF4905DA7D1D4157FA6D8760D847CF7F494685891BAA672C2F6D7FDF3E6A1ABCF5EA4A4E5719884FFC9958BFE6F496E8AD3C83C773A36D95393EFA03E6CAA4C1F0009DE443CFB59AB1CD87C280D40A0786C4D076DC008872E782AFCB068C1E943E66B7E4A1A3C9E53228E83854736D6CCDE0E58DE5176232EF5E9A6A8673933627621A6DBB46EF268C01CEC6E13FCBC4FA41609C77879C0FB_bingo%2CstrengthenKey%3D8FADC2DA7545804BCBA105FDC98A2E577277403AB437C3555392B79155BDFDB1_bingo%22%7D
```

#### 商品3完整API
```
https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%224P9a2T9osR9JvtzHVaYTPvsecRtg%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3D4C9F0D1B8555D5C40850F1BA6703554F_bingo%2CroleId%3DC83CF516C5021A0440D4A78463846EC0A6D8760D847CF7F494685891BAA672C20EEB01B75B57C8C1F5887BB415CC6DF634DCDC7DB04AF6095C53CC3E6690642E7B8AEF4B737FD084F31294F4469B04EEA60DDD35C05CC7D2BD35F32456895A6F31F09972AAFA6455A4B7CB7DAB3EB2D1B75DBE1F7DD5F8C413BAF33F33B6B3154699D0C6AAF92D192613AA053862C3D0DABFD632D956A5C71F59F96E20A20B29_bingo%2CstrengthenKey%3D990B819353DECFAD4DE3FA4C77A913CF10AC721142B9BBE45DDA8BD125217D13_bingo%22%7D
```

#### 商品4完整API
```
https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%224P9a2T9osR9JvtzHVaYTPvsecRtg%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3D241B45B85A0BEE79DFA95932C0E6E845_bingo%2CroleId%3DB36E66B5F7F91C934D6467CFA184DE59A6D8760D847CF7F494685891BAA672C2B68DD6B60CDDDF35A1D1102F3C50746371B9666A464DFD96B8E67A8985CF3374567C4A7E73B6D08A2D9ADA55B687A43F2D87534BD9C0B33F0869A206D5507C8468843F680CC687421951F666E4F1F5F5308BBB01482ADC0EA9A7152DD0D02F05A1EB92870A1DA9ADC8F252AAE82E651424AFB2867A1C9A87FE519AE8B907694C_bingo%2CstrengthenKey%3D40C366213E985142BC1143B54EB57052BAD8A004106923B684F947E4FA9F3018_bingo%22%7D
```

### 2. 必需的请求头

```
User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D
Referer: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html
Content-Type: application/x-www-form-urlencoded
Cookie: YOUR_LOGIN_COOKIES_HERE
```

### 3. 动态参数生成

如果上述固定参数仍然返回 code:-1，使用动态生成器：

```bash
# 重新生成实时参数
python jd_complete_api_generator.py
```

这会生成包含当前时间戳的新参数。

### 4. cURL 测试命令

```bash
# 商品1测试
curl -X GET "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%224P9a2T9osR9JvtzHVaYTPvsecRtg%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3D781E1074EB7BAEBAD155F319E5CBF16B_bingo%2CroleId%3D911E376E79D996AB33F08D32BEA890C4CD7E90DBCA90B2DACD2ABC81ADBA679E7BB1ABE8925E2EAC306910FC8EE2792AFDB061AB94104D80C69F17824C771D0971ABC74782E41FA81786BE4D65CB58EDEEDFCB09CFDA586182FC1BB52D39749702A3B82A4D17464793D556974A882735243A72CD61A203FE128B2DD1F2D364A219E2056DBFF6CAC898E53B3C833628E5D6B9ABEFD3CC8C7F58A016669DED19AF_bingo%2CstrengthenKey%3D15475342C5AF0AABE9B01407095F03823B0EB5BA0E90673E82947211C9DBFCEB_bingo%22%7D" \
  -H "User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D" \
  -H "Referer: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "Cookie: YOUR_COOKIES_HERE"
```

## 🔧 故障排除步骤

### 步骤1: 验证Cookie
确保Cookie包含以下关键字段：
- `pt_key`
- `pt_pin` 
- `pwdt_id`
- `sid_guard`

### 步骤2: 检查时间戳
参数中的时间戳不能太旧，建议：
1. 重新运行生成器获取新时间戳
2. 在5分钟内使用生成的参数

### 步骤3: 验证User-Agent
必须使用京东APP的User-Agent，不能使用浏览器的。

### 步骤4: 检查活动状态
确认活动页面可以正常访问：
```
https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html
```

### 步骤5: 测试简化请求
如果仍然失败，尝试最简化的请求：

```python
import requests
import json
from urllib.parse import quote

# 最新生成的args
args = "key=781E1074EB7BAEBAD155F319E5CBF16B_bingo,roleId=911E376E79D996AB33F08D32BEA890C4CD7E90DBCA90B2DACD2ABC81ADBA679E7BB1ABE8925E2EAC306910FC8EE2792AFDB061AB94104D80C69F17824C771D0971ABC74782E41FA81786BE4D65CB58EDEEDFCB09CFDA586182FC1BB52D39749702A3B82A4D17464793D556974A882735243A72CD61A203FE128B2DD1F2D364A219E2056DBFF6CAC898E53B3C833628E5D6B9ABEFD3CC8C7F58A016669DED19AF_bingo,strengthenKey=15475342C5AF0AABE9B01407095F03823B0EB5BA0E90673E82947211C9DBFCEB_bingo"

body = {
    "activityId": "4P9a2T9osR9JvtzHVaYTPvsecRtg",
    "scene": "1", 
    "args": args
}

url = f"https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={quote(json.dumps(body))}"

headers = {
    'User-Agent': 'jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D',
    'Cookie': 'YOUR_COOKIES_HERE'
}

response = requests.get(url, headers=headers)
print(response.text)
```

## 📋 成功响应示例

如果参数正确，应该返回类似：
```json
{
  "code": "0",
  "msg": "success", 
  "data": {...}
}
```

或者活动相关的错误码（非-1）。

## 🔄 自动化解决方案

如果需要持续使用，建议：

1. **定时重新生成参数**（每5分钟）
2. **监控响应码**，如果返回-1立即重新生成
3. **使用多个策略**轮换测试

```python
# 自动重试机制
def auto_retry_request(product_index, max_retries=3):
    for i in range(max_retries):
        # 重新生成参数
        generator = JDCompleteAPIGenerator()
        api_data = generator.create_complete_api_url(product_index)
        
        # 发送请求
        response = requests.get(api_data['complete_url'], headers=headers)
        result = response.json()
        
        if result.get('code') != '-1':
            return result
            
        print(f"重试 {i+1}/{max_retries}")
        time.sleep(2)
    
    return None
```

## 📞 最终建议

1. **优先使用最新生成的动态参数**
2. **确保Cookie有效且完整**
3. **在参数生成后5分钟内使用**
4. **如果仍然失败，可能需要更深入的设备指纹模拟**

这套解决方案基于完整的逆向分析，应该能解决大部分 code:-1 的问题。
