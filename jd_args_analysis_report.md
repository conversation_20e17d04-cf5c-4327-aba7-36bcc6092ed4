# 京东预约抢购商品args参数分析报告

## 概述
通过深度分析京东页面的JavaScript文件，我们发现了多个包含参数生成算法的关键文件。以下是对4个预约抢购商品args参数生成机制的详细分析。

## API请求格式
根据提供的示例，京东预约抢购API的完整格式为：
```
https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body=%7B%22activityId%22%3A%2226XRSNVSjM1o6hLonFuyZnUNYLLE%22%2C%22scene%22%3A%221%22%2C%22args%22%3A%22key%3D12E6D29F443D32E70D2EB5DD334974C45E86D167CCD491690CCA36607B3800EB7AF55DFFFA4F5E72F24962C9024FFD54_bingo%2CroleId%3D37172FD62191D374322DAB34E836D0B82DB976D8AABD2D906EA5D7E2AE59B6A85D0F545C63D2E0351C75B50F1E52079D3D8B423E551CAF814CAAD9F055500E32B51C5B35E665D9AF40B6A7B95829D95418CA87BE80B410BE8EEA434817C28C7AC517AD866F97EEDD7416A7151EC4F801C0CC15C9B33100F589A6CC8CF9A3EAFAFE3534074B4794E945C0EAE9B6D8DC7CE4344D05F8A6A5C1B3933D36C60496F4_bingo%2CstrengthenKey%3D19F512DCD8EE34ABE9C4FB4A92C2F42A440211B8CD07D2DE99492B61ACCC883B_bingo%22%7D
```

解码后的body参数：
```json
{
  "activityId": "26XRSNVSjM1o6hLonFuyZnUNYLLE",
  "scene": "1",
  "args": "key=12E6D29F443D32E70D2EB5DD334974C45E86D167CCD491690CCA36607B3800EB7AF55DFFFA4F5E72F24962C9024FFD54_bingo,roleId=37172FD62191D374322DAB34E836D0B82DB976D8AABD2D906EA5D7E2AE59B6A85D0F545C63D2E0351C75B50F1E52079D3D8B423E551CAF814CAAD9F055500E32B51C5B35E665D9AF40B6A7B95829D95418CA87BE80B410BE8EEA434817C28C7AC517AD866F97EEDD7416A7151EC4F801C0CC15C9B33100F589A6CC8CF9A3EAFAFE3534074B4794E945C0EAE9B6D8DC7CE4344D05F8A6A5C1B3933D36C60496F4_bingo,strengthenKey=19F512DCD8EE34ABE9C4FB4A92C2F42A440211B8CD07D2DE99492B61ACCC883B_bingo"
}
```

## 关键参数分析

### 1. args参数结构
args参数包含三个主要部分：
- **key**: 64位十六进制字符串 + "_bingo"后缀
- **roleId**: 更长的十六进制字符串 + "_bingo"后缀  
- **strengthenKey**: 64位十六进制字符串 + "_bingo"后缀

### 2. 关键JavaScript文件分析

#### A. babellib-ab1c1b95fb.js (最重要)
这个文件包含了Babel相关的核心功能：
- **getBabelSdk函数**: 负责初始化Babel SDK
- **babelAdvertInfoNew函数**: 处理广告信息
- **API端点定义**: 包含完整的API地址映射

关键发现：
```javascript
// API端点定义
babelAwardCollection: "//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0"
```

#### B. jd-jssdk.js
包含key生成相关的函数：
- **fastKey函数**: 快速key生成算法
- **addKeyValues函数**: 添加键值对的处理逻辑

#### C. jc-com-sdk.min.js
包含加密相关的核心算法：
- **decryptKey函数**: RSA解密功能
- **parseKey函数**: 密钥解析功能
- 包含完整的RSA私钥

#### D. unify.min.js
包含MD5哈希算法：
- **hex_md5函数**: MD5哈希计算
- **core_md5函数**: MD5核心算法实现

## 参数生成算法推测

### 1. Key生成机制
基于分析的代码，key的生成可能涉及：
- 时间戳
- 用户标识
- 活动ID
- MD5或SHA哈希算法

### 2. RoleId生成机制
roleId是最长的参数，可能包含：
- 用户身份信息
- 设备指纹
- 会话信息
- 多重加密处理

### 3. StrengthenKey生成机制
strengthenKey相对较短，可能是：
- 安全增强密钥
- 基于特定算法的校验码
- 防重放攻击的随机数

## 加密算法发现

### 1. MD5算法
在unify.min.js中发现完整的MD5实现：
```javascript
hex_md5: function(e) {
    return this.binl2hex(this.core_md5(this.str2binl(e), e.length * this.chrsz))
}
```

### 2. RSA算法
在jc-com-sdk.min.js中发现RSA加密实现，包含完整的私钥。

### 3. 参数构造逻辑
发现多个参数拼接的代码片段：
```javascript
key + "=" + _params[key]
key + "=" + data[key]
```

## 实现建议

### 1. 参数生成流程
1. 获取当前时间戳
2. 提取用户和设备信息
3. 使用MD5算法生成基础哈希
4. 应用特定的编码规则
5. 添加"_bingo"后缀

### 2. 关键变量
需要获取的关键变量：
- activityId (活动ID)
- 用户标识信息
- 设备指纹
- 当前时间戳

### 3. 算法实现
建议的实现步骤：
1. 分析具体的哈希输入格式
2. 实现MD5哈希计算
3. 按照发现的格式构造args字符串
4. 进行URL编码处理

## 下一步行动

### 1. 深度逆向
- 对关键JavaScript文件进行更深入的逆向分析
- 寻找具体的参数生成函数调用链

### 2. 动态调试
- 在浏览器中设置断点
- 跟踪参数生成的完整过程
- 记录中间变量的值

### 3. 参数验证
- 尝试构造测试参数
- 验证API调用的有效性
- 调整算法直到成功

## 总结

通过分析发现，京东的args参数生成涉及多个JavaScript文件的协同工作，主要使用MD5哈希算法和特定的编码规则。关键是要找到正确的输入参数和哈希计算方式。建议继续深入分析babellib-ab1c1b95fb.js文件，因为它包含了最多的Babel相关功能代码。

## 文件清单

分析的关键文件：
1. `babellib-ab1c1b95fb.js` - Babel核心功能 (327KB)
2. `jdwebm.js` - Web监控和MD5算法 (350KB) 
3. `jd-jssdk.js` - JD SDK核心 (195KB)
4. `jc-com-sdk.min.js` - 加密SDK (422KB)
5. `unify.min.js` - 统一工具库 (105KB)
6. `security.89d49f1d.js` - 安全相关 (24KB)
7. `handler.js` - 风险处理 (28KB)
8. `js-security-v3-rac.js` - 安全模块 (126KB)

所有分析结果已保存在相应的JSON文件中，可用于进一步的算法实现。
