#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东args参数生成算法高级调试器
使用专业的逆向工程方法进行深度分析
"""

import json
import time
import re
import hashlib
import base64
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import requests
from urllib.parse import unquote, quote

class JDAdvancedDebugger:
    def __init__(self):
        self.driver = None
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        self.intercepted_requests = []
        self.function_calls = []
        self.variable_states = {}
        
    def setup_advanced_driver(self):
        """设置高级调试浏览器"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用详细的性能日志
        chrome_options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL'
        })
        
        # 启用开发者工具协议
        chrome_options.add_experimental_option("useAutomationExtension", False)
        chrome_options.add_experimental_option("excludeSwitches", ["enable-automation"])
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 启用CDP (Chrome DevTools Protocol)
        self.driver.execute_cdp_cmd('Runtime.enable', {})
        self.driver.execute_cdp_cmd('Network.enable', {})
        self.driver.execute_cdp_cmd('Debugger.enable', {})
        
    def inject_debugging_hooks(self):
        """注入调试钩子函数"""
        debug_script = """
        // 创建全局调试对象
        window._JD_DEBUG = {
            interceptedCalls: [],
            variableStates: {},
            originalFunctions: {},
            logs: []
        };
        
        // 拦截console.log
        const originalLog = console.log;
        console.log = function(...args) {
            window._JD_DEBUG.logs.push({
                type: 'log',
                args: args,
                timestamp: Date.now(),
                stack: new Error().stack
            });
            return originalLog.apply(console, arguments);
        };
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            window._JD_DEBUG.interceptedCalls.push({
                type: 'xhr',
                method: method,
                url: url,
                timestamp: Date.now(),
                stack: new Error().stack
            });
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            window._JD_DEBUG.interceptedCalls.push({
                type: 'fetch',
                url: url,
                options: options,
                timestamp: Date.now(),
                stack: new Error().stack
            });
            return originalFetch.apply(this, arguments);
        };
        
        // 监控特定函数调用
        function hookFunction(obj, funcName, callback) {
            if (obj && typeof obj[funcName] === 'function') {
                const original = obj[funcName];
                window._JD_DEBUG.originalFunctions[funcName] = original;
                obj[funcName] = function(...args) {
                    const result = original.apply(this, args);
                    callback(funcName, args, result, this);
                    return result;
                };
            }
        }
        
        // 监控可能的args生成函数
        function monitorArgsGeneration() {
            // 监控所有包含'args'的函数
            for (let prop in window) {
                try {
                    if (typeof window[prop] === 'function' && prop.toLowerCase().includes('args')) {
                        hookFunction(window, prop, function(name, args, result, context) {
                            window._JD_DEBUG.interceptedCalls.push({
                                type: 'function_call',
                                name: name,
                                args: args,
                                result: result,
                                timestamp: Date.now()
                            });
                        });
                    }
                } catch (e) {}
            }
        }
        
        // 延迟执行监控
        setTimeout(monitorArgsGeneration, 1000);
        
        // 监控MD5相关函数
        function monitorCryptoFunctions() {
            const cryptoFunctions = ['md5', 'MD5', 'hex_md5', 'sha1', 'SHA1', 'encrypt', 'decrypt'];
            cryptoFunctions.forEach(funcName => {
                if (window[funcName]) {
                    hookFunction(window, funcName, function(name, args, result, context) {
                        window._JD_DEBUG.interceptedCalls.push({
                            type: 'crypto_call',
                            name: name,
                            args: args,
                            result: result,
                            timestamp: Date.now()
                        });
                    });
                }
            });
        }
        
        setTimeout(monitorCryptoFunctions, 2000);
        
        // 监控对象属性变化
        function monitorObjectChanges(obj, objName) {
            return new Proxy(obj, {
                set(target, property, value) {
                    window._JD_DEBUG.variableStates[objName + '.' + property] = {
                        value: value,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    };
                    target[property] = value;
                    return true;
                }
            });
        }
        
        // 获取调试信息的函数
        window.getDebugInfo = function() {
            return window._JD_DEBUG;
        };
        
        console.log('JD Debug hooks injected successfully');
        """
        
        self.driver.execute_script(debug_script)
        
    def inject_args_interceptor(self):
        """注入args参数拦截器"""
        interceptor_script = """
        // 专门拦截args参数生成
        window._ARGS_INTERCEPTOR = {
            argsHistory: [],
            keyGeneration: [],
            roleIdGeneration: [],
            strengthenKeyGeneration: []
        };
        
        // 拦截所有可能的args相关操作
        const originalStringify = JSON.stringify;
        JSON.stringify = function(obj) {
            if (obj && typeof obj === 'object') {
                if (obj.args || obj.key || obj.roleId || obj.strengthenKey) {
                    window._ARGS_INTERCEPTOR.argsHistory.push({
                        type: 'json_stringify',
                        data: obj,
                        timestamp: Date.now(),
                        stack: new Error().stack
                    });
                }
            }
            return originalStringify.apply(this, arguments);
        };
        
        // 监控字符串拼接操作
        const originalConcat = String.prototype.concat;
        String.prototype.concat = function(...args) {
            const result = originalConcat.apply(this, args);
            if (this.includes('key=') || this.includes('roleId=') || this.includes('strengthenKey=')) {
                window._ARGS_INTERCEPTOR.argsHistory.push({
                    type: 'string_concat',
                    original: this.toString(),
                    args: args,
                    result: result,
                    timestamp: Date.now()
                });
            }
            return result;
        };
        
        // 监控encodeURIComponent
        const originalEncodeURI = encodeURIComponent;
        window.encodeURIComponent = function(str) {
            const result = originalEncodeURI(str);
            if (str && (str.includes('args') || str.includes('key=') || str.includes('roleId='))) {
                window._ARGS_INTERCEPTOR.argsHistory.push({
                    type: 'encode_uri',
                    input: str,
                    output: result,
                    timestamp: Date.now()
                });
            }
            return result;
        };
        
        // 获取args拦截信息
        window.getArgsInfo = function() {
            return window._ARGS_INTERCEPTOR;
        };
        
        console.log('Args interceptor injected successfully');
        """
        
        self.driver.execute_script(interceptor_script)
        
    def inject_crypto_monitor(self):
        """注入加密算法监控器"""
        crypto_script = """
        window._CRYPTO_MONITOR = {
            hashCalls: [],
            encryptCalls: [],
            keyGeneration: []
        };
        
        // 监控可能的哈希函数
        function monitorHashFunctions() {
            const hashFunctions = [
                'md5', 'MD5', 'hex_md5', 'core_md5',
                'sha1', 'SHA1', 'sha256', 'SHA256',
                'hash', 'Hash', 'digest'
            ];
            
            hashFunctions.forEach(funcName => {
                // 检查window对象
                if (window[funcName] && typeof window[funcName] === 'function') {
                    const original = window[funcName];
                    window[funcName] = function(...args) {
                        const result = original.apply(this, args);
                        window._CRYPTO_MONITOR.hashCalls.push({
                            function: funcName,
                            input: args,
                            output: result,
                            timestamp: Date.now(),
                            stack: new Error().stack
                        });
                        return result;
                    };
                }
                
                // 检查可能的对象方法
                for (let obj in window) {
                    try {
                        if (window[obj] && typeof window[obj] === 'object' && window[obj][funcName]) {
                            const original = window[obj][funcName];
                            window[obj][funcName] = function(...args) {
                                const result = original.apply(this, args);
                                window._CRYPTO_MONITOR.hashCalls.push({
                                    function: obj + '.' + funcName,
                                    input: args,
                                    output: result,
                                    timestamp: Date.now()
                                });
                                return result;
                            };
                        }
                    } catch (e) {}
                }
            });
        }
        
        // 延迟执行以确保所有脚本加载完成
        setTimeout(monitorHashFunctions, 3000);
        
        window.getCryptoInfo = function() {
            return window._CRYPTO_MONITOR;
        };
        
        console.log('Crypto monitor injected successfully');
        """
        
        self.driver.execute_script(crypto_script)
        
    def trigger_args_generation(self):
        """触发args参数生成"""
        print("正在触发args参数生成...")
        
        # 滚动页面确保所有内容加载
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        # 查找并点击所有可能的预约按钮
        button_selectors = [
            "button", "div[onclick]", "a[onclick]", "[data-role]",
            ".btn", ".button", "[class*='reserve']", "[class*='book']",
            "[class*='award']", "[class*='collection']", "img[onclick]"
        ]
        
        clicked_elements = 0
        for selector in button_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements[:5]:  # 限制每种选择器最多点击5个元素
                    try:
                        if element.is_displayed() and element.is_enabled():
                            # 滚动到元素位置
                            self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", element)
                            time.sleep(0.5)
                            
                            # 尝试点击
                            try:
                                element.click()
                            except:
                                self.driver.execute_script("arguments[0].click();", element)
                            
                            clicked_elements += 1
                            time.sleep(1)
                            
                            # 检查是否有新的网络请求
                            debug_info = self.get_debug_info()
                            if debug_info and len(debug_info.get('interceptedCalls', [])) > 0:
                                print(f"点击元素后检测到 {len(debug_info['interceptedCalls'])} 个调用")
                                
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue
                
        print(f"总共点击了 {clicked_elements} 个元素")
        
        # 等待一段时间让所有异步操作完成
        time.sleep(5)
        
    def get_debug_info(self):
        """获取调试信息"""
        try:
            debug_info = self.driver.execute_script("return window.getDebugInfo ? window.getDebugInfo() : null;")
            return debug_info
        except:
            return None
            
    def get_args_info(self):
        """获取args拦截信息"""
        try:
            args_info = self.driver.execute_script("return window.getArgsInfo ? window.getArgsInfo() : null;")
            return args_info
        except:
            return None
            
    def get_crypto_info(self):
        """获取加密监控信息"""
        try:
            crypto_info = self.driver.execute_script("return window.getCryptoInfo ? window.getCryptoInfo() : null;")
            return crypto_info
        except:
            return None
            
    def analyze_network_requests(self):
        """分析网络请求"""
        logs = self.driver.get_log('performance')
        network_requests = []
        
        for log in logs:
            try:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request = message['message']['params']['request']
                    url = request['url']
                    
                    # 重点关注包含newBabelAwardCollection的请求
                    if 'newBabelAwardCollection' in url or 'babelAwardCollection' in url:
                        network_requests.append({
                            'url': url,
                            'method': request.get('method', 'GET'),
                            'headers': request.get('headers', {}),
                            'postData': request.get('postData', ''),
                            'timestamp': log['timestamp']
                        })
                        
                        # 尝试解析args参数
                        if 'body=' in url:
                            try:
                                body_start = url.find('body=') + 5
                                body_end = url.find('&', body_start)
                                if body_end == -1:
                                    body_end = len(url)
                                body_encoded = url[body_start:body_end]
                                body_decoded = unquote(body_encoded)
                                body_json = json.loads(body_decoded)
                                
                                if 'args' in body_json:
                                    print(f"发现args参数: {body_json['args']}")
                                    self.analyze_args_structure(body_json['args'])
                                    
                            except Exception as e:
                                print(f"解析body参数失败: {e}")
                                
            except Exception as e:
                continue
                
        return network_requests
        
    def analyze_args_structure(self, args_string):
        """分析args参数结构"""
        print(f"\n=== Args参数结构分析 ===")
        print(f"原始args: {args_string}")
        
        # 解析key, roleId, strengthenKey
        parts = args_string.split(',')
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                print(f"{key}: {value}")
                
                # 分析值的特征
                if value.endswith('_bingo'):
                    clean_value = value[:-6]  # 移除_bingo后缀
                    print(f"  清理后的值: {clean_value}")
                    print(f"  长度: {len(clean_value)}")
                    print(f"  是否为十六进制: {all(c in '0123456789ABCDEFabcdef' for c in clean_value)}")
                    
                    # 尝试分析可能的生成规律
                    if len(clean_value) == 64:
                        print(f"  可能是SHA256哈希 (64字符)")
                    elif len(clean_value) == 32:
                        print(f"  可能是MD5哈希 (32字符)")
                    else:
                        print(f"  自定义长度: {len(clean_value)}")
                        
    def extract_javascript_variables(self):
        """提取JavaScript中的关键变量"""
        variables_script = """
        const extractedVars = {};
        
        // 提取可能相关的全局变量
        const keywords = ['activity', 'user', 'token', 'key', 'role', 'strengthen', 'args', 'babel'];
        
        for (let prop in window) {
            try {
                const lowerProp = prop.toLowerCase();
                if (keywords.some(keyword => lowerProp.includes(keyword))) {
                    extractedVars[prop] = {
                        type: typeof window[prop],
                        value: typeof window[prop] === 'function' ? '[Function]' : window[prop]
                    };
                }
            } catch (e) {}
        }
        
        // 检查localStorage和sessionStorage
        extractedVars._localStorage = {};
        extractedVars._sessionStorage = {};
        
        try {
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                extractedVars._localStorage[key] = localStorage.getItem(key);
            }
        } catch (e) {}
        
        try {
            for (let i = 0; i < sessionStorage.length; i++) {
                const key = sessionStorage.key(i);
                extractedVars._sessionStorage[key] = sessionStorage.getItem(key);
            }
        } catch (e) {}
        
        return extractedVars;
        """
        
        return self.driver.execute_script(variables_script)
        
    def run_advanced_debug(self, url):
        """运行高级调试"""
        try:
            print("=== 启动高级调试模式 ===")
            self.setup_advanced_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(3)
            
            print("注入调试钩子...")
            self.inject_debugging_hooks()
            time.sleep(1)
            
            print("注入args拦截器...")
            self.inject_args_interceptor()
            time.sleep(1)
            
            print("注入加密监控器...")
            self.inject_crypto_monitor()
            time.sleep(2)
            
            print("提取JavaScript变量...")
            js_variables = self.extract_javascript_variables()
            
            print("触发args参数生成...")
            self.trigger_args_generation()
            
            print("分析网络请求...")
            network_requests = self.analyze_network_requests()
            
            print("收集调试信息...")
            debug_info = self.get_debug_info()
            args_info = self.get_args_info()
            crypto_info = self.get_crypto_info()
            
            # 汇总结果
            result = {
                'network_requests': network_requests,
                'debug_info': debug_info,
                'args_info': args_info,
                'crypto_info': crypto_info,
                'js_variables': js_variables,
                'analysis_timestamp': time.time()
            }
            
            return result
            
        except Exception as e:
            print(f"高级调试过程中出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    debugger = JDAdvancedDebugger()
    result = debugger.run_advanced_debug(url)
    
    if result:
        print("\n=== 高级调试结果 ===")
        
        # 保存完整结果
        with open('jd_advanced_debug_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("完整调试结果已保存到 jd_advanced_debug_result.json")
        
        # 分析关键发现
        print(f"\n网络请求数量: {len(result.get('network_requests', []))}")
        print(f"调试信息条目: {len(result.get('debug_info', {}).get('interceptedCalls', []))}")
        print(f"Args拦截条目: {len(result.get('args_info', {}).get('argsHistory', []))}")
        print(f"加密调用条目: {len(result.get('crypto_info', {}).get('hashCalls', []))}")
        print(f"JavaScript变量: {len(result.get('js_variables', {}))}")
        
        # 显示关键发现
        if result.get('args_info', {}).get('argsHistory'):
            print("\n=== Args拦截发现 ===")
            for item in result['args_info']['argsHistory'][:3]:
                print(f"类型: {item.get('type')}")
                if 'result' in item:
                    print(f"结果: {str(item['result'])[:100]}...")
                    
        if result.get('crypto_info', {}).get('hashCalls'):
            print("\n=== 加密调用发现 ===")
            for call in result['crypto_info']['hashCalls'][:3]:
                print(f"函数: {call.get('function')}")
                print(f"输入: {str(call.get('input'))[:100]}...")
                print(f"输出: {str(call.get('output'))[:100]}...")
                
    else:
        print("高级调试失败")

if __name__ == "__main__":
    main()
