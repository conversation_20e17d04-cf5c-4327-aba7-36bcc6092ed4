#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东元素查找器
查找页面上所有可点击元素并分析其状态
"""

import json
import time
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager

class JDElementFinder:
    def __init__(self):
        self.driver = None
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_driver(self):
        """设置浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def inject_network_monitor(self):
        """注入网络监控器"""
        monitor_script = """
        window._NETWORK_MONITOR = {
            requests: [],
            responses: []
        };
        
        // 拦截XMLHttpRequest
        const originalXHROpen = XMLHttpRequest.prototype.open;
        const originalXHRSend = XMLHttpRequest.prototype.send;
        
        XMLHttpRequest.prototype.open = function(method, url, ...args) {
            this._method = method;
            this._url = url;
            return originalXHROpen.apply(this, [method, url, ...args]);
        };
        
        XMLHttpRequest.prototype.send = function(data) {
            const xhr = this;
            
            window._NETWORK_MONITOR.requests.push({
                type: 'xhr',
                method: this._method,
                url: this._url,
                data: data,
                timestamp: Date.now()
            });
            
            const originalOnReadyStateChange = xhr.onreadystatechange;
            xhr.onreadystatechange = function() {
                if (xhr.readyState === 4) {
                    window._NETWORK_MONITOR.responses.push({
                        type: 'xhr',
                        url: xhr._url,
                        status: xhr.status,
                        response: xhr.responseText,
                        timestamp: Date.now()
                    });
                }
                
                if (originalOnReadyStateChange) {
                    originalOnReadyStateChange.apply(xhr, arguments);
                }
            };
            
            return originalXHRSend.apply(this, [data]);
        };
        
        // 拦截fetch
        const originalFetch = window.fetch;
        window.fetch = function(url, options = {}) {
            window._NETWORK_MONITOR.requests.push({
                type: 'fetch',
                url: url,
                options: options,
                timestamp: Date.now()
            });
            
            return originalFetch.apply(this, arguments).then(response => {
                response.clone().text().then(text => {
                    window._NETWORK_MONITOR.responses.push({
                        type: 'fetch',
                        url: url,
                        status: response.status,
                        response: text,
                        timestamp: Date.now()
                    });
                });
                return response;
            });
        };
        
        window.getNetworkData = function() {
            return window._NETWORK_MONITOR;
        };
        
        console.log('网络监控器已注入');
        """
        
        self.driver.execute_script(monitor_script)
        
    def find_all_clickable_elements(self):
        """查找所有可点击元素"""
        clickable_elements = []
        
        # 等待页面加载
        WebDriverWait(self.driver, 10).until(
            EC.presence_of_element_located((By.TAG_NAME, "body"))
        )
        
        # 滚动页面确保所有内容加载
        self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
        time.sleep(2)
        self.driver.execute_script("window.scrollTo(0, 0);")
        time.sleep(2)
        
        # 查找各种可点击元素
        selectors = [
            "button",
            "a",
            "[onclick]",
            "[data-role]",
            ".btn",
            ".button",
            "[class*='btn']",
            "[class*='button']",
            "[class*='click']",
            "[class*='tap']",
            "div[role='button']",
            "span[role='button']",
            "[data-click]",
            "[data-tap]"
        ]
        
        for selector in selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                
                for element in elements:
                    try:
                        if element.is_displayed():
                            element_info = {
                                'selector': selector,
                                'tag_name': element.tag_name,
                                'text': element.text.strip()[:50],
                                'class': element.get_attribute('class'),
                                'id': element.get_attribute('id'),
                                'onclick': element.get_attribute('onclick'),
                                'data_role': element.get_attribute('data-role'),
                                'is_enabled': element.is_enabled(),
                                'location': element.location,
                                'size': element.size
                            }
                            
                            # 避免重复
                            if not any(e['location'] == element_info['location'] and 
                                     e['size'] == element_info['size'] for e in clickable_elements):
                                clickable_elements.append(element_info)
                                
                    except Exception as e:
                        continue
                        
            except Exception as e:
                continue
                
        return clickable_elements
        
    def analyze_page_content(self):
        """分析页面内容"""
        try:
            # 获取页面HTML
            page_source = self.driver.page_source
            
            # 获取页面标题
            title = self.driver.title
            
            # 获取当前URL
            current_url = self.driver.current_url
            
            # 查找包含特定关键词的文本
            keywords = ["预约", "领券", "抢购", "立即", "马上", "去", "领取", "status", "2", "5"]
            
            keyword_matches = []
            for keyword in keywords:
                if keyword in page_source:
                    keyword_matches.append(keyword)
                    
            # 使用JavaScript获取更多信息
            js_info = self.driver.execute_script("""
                return {
                    readyState: document.readyState,
                    hasJQuery: typeof jQuery !== 'undefined',
                    hasVue: typeof Vue !== 'undefined',
                    hasReact: typeof React !== 'undefined',
                    windowKeys: Object.keys(window).filter(key => key.includes('jd') || key.includes('babel') || key.includes('activity')),
                    globalVars: Object.keys(window).filter(key => typeof window[key] === 'object' && window[key] !== null && (key.includes('config') || key.includes('data') || key.includes('info')))
                };
            """)
            
            return {
                'title': title,
                'url': current_url,
                'keyword_matches': keyword_matches,
                'js_info': js_info,
                'page_length': len(page_source)
            }
            
        except Exception as e:
            print(f"分析页面内容失败: {e}")
            return None
            
    def click_elements_and_monitor(self, clickable_elements):
        """点击元素并监控网络请求"""
        click_results = []
        
        for i, element_info in enumerate(clickable_elements[:10]):  # 最多点击10个元素
            try:
                print(f"尝试点击元素 {i+1}: {element_info['text']}")
                
                # 根据位置查找元素
                location = element_info['location']
                size = element_info['size']
                
                # 使用JavaScript点击
                js_click_script = f"""
                var elements = document.elementsFromPoint({location['x'] + size['width']//2}, {location['y'] + size['height']//2});
                if (elements.length > 0) {{
                    elements[0].click();
                    return true;
                }}
                return false;
                """
                
                # 获取点击前的网络数据
                before_data = self.driver.execute_script("return window.getNetworkData ? window.getNetworkData() : {requests: [], responses: []};")
                
                # 执行点击
                click_success = self.driver.execute_script(js_click_script)
                
                # 等待响应
                time.sleep(2)
                
                # 获取点击后的网络数据
                after_data = self.driver.execute_script("return window.getNetworkData ? window.getNetworkData() : {requests: [], responses: []};")
                
                # 分析新的网络请求
                new_requests = after_data['requests'][len(before_data['requests']):]
                new_responses = after_data['responses'][len(before_data['responses']):]
                
                click_result = {
                    'element_info': element_info,
                    'click_success': click_success,
                    'new_requests': new_requests,
                    'new_responses': new_responses,
                    'request_count': len(new_requests),
                    'response_count': len(new_responses)
                }
                
                click_results.append(click_result)
                
                if new_requests:
                    print(f"  触发了 {len(new_requests)} 个新请求")
                    for req in new_requests:
                        print(f"    {req['type']}: {req['url']}")
                        
                if new_responses:
                    print(f"  收到了 {len(new_responses)} 个新响应")
                    for resp in new_responses:
                        if 'status' in resp['response']:
                            print(f"    响应包含status字段: {resp['url']}")
                
            except Exception as e:
                print(f"点击元素失败: {e}")
                continue
                
        return click_results
        
    def run_analysis(self, url):
        """运行完整分析"""
        try:
            print("=== 启动元素查找器 ===")
            
            # 设置驱动
            self.setup_driver()
            
            # 加载页面
            print(f"正在加载页面: {url}")
            self.driver.get(url)
            time.sleep(5)
            
            # 注入网络监控
            self.inject_network_monitor()
            
            # 分析页面内容
            print("分析页面内容...")
            page_analysis = self.analyze_page_content()
            
            # 查找可点击元素
            print("查找可点击元素...")
            clickable_elements = self.find_all_clickable_elements()
            
            print(f"找到 {len(clickable_elements)} 个可点击元素")
            
            # 点击元素并监控
            print("点击元素并监控网络请求...")
            click_results = self.click_elements_and_monitor(clickable_elements)
            
            # 获取最终网络数据
            final_network_data = self.driver.execute_script("return window.getNetworkData ? window.getNetworkData() : {requests: [], responses: []};")
            
            result = {
                'page_analysis': page_analysis,
                'clickable_elements': clickable_elements,
                'click_results': click_results,
                'final_network_data': final_network_data,
                'summary': {
                    'total_clickable_elements': len(clickable_elements),
                    'clicked_elements': len(click_results),
                    'total_requests': len(final_network_data['requests']),
                    'total_responses': len(final_network_data['responses'])
                }
            }
            
            return result
            
        except Exception as e:
            print(f"分析过程中出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    finder = JDElementFinder()
    result = finder.run_analysis(url)
    
    if result:
        print("\n=== 分析结果 ===")
        print(f"页面标题: {result['page_analysis']['title']}")
        print(f"可点击元素数量: {result['summary']['total_clickable_elements']}")
        print(f"实际点击元素数量: {result['summary']['clicked_elements']}")
        print(f"总网络请求数: {result['summary']['total_requests']}")
        print(f"总网络响应数: {result['summary']['total_responses']}")
        
        # 保存结果
        with open('jd_element_analysis_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("详细结果已保存到 jd_element_analysis_result.json")
        
        # 显示找到的可点击元素
        print(f"\n=== 可点击元素列表 ===")
        for i, element in enumerate(result['clickable_elements'][:10], 1):
            print(f"{i}. {element['tag_name']} - '{element['text']}' - {element['class']}")
            
        # 显示点击结果
        print(f"\n=== 点击结果 ===")
        for i, click_result in enumerate(result['click_results'], 1):
            element = click_result['element_info']
            print(f"{i}. 点击 '{element['text']}' - 成功: {click_result['click_success']} - 新请求: {click_result['request_count']}")
            
        # 显示网络请求
        api_requests = [req for req in result['final_network_data']['requests'] if 'api.m.jd.com' in req.get('url', '')]
        if api_requests:
            print(f"\n=== API请求 ===")
            for i, req in enumerate(api_requests, 1):
                print(f"{i}. {req['type']}: {req['url']}")
                
    else:
        print("分析失败")

if __name__ == "__main__":
    main()
