[{"url": "https://storage.360buyimg.com/webcontainer/main/js-security-v3-rac.js?v=20250605", "filename": "js-security-v3-rac.js?v=20250605", "file_size": 126131, "args_functions": [], "encryption_algorithms": [], "api_code": [], "parameter_constructions": [{"type": "parameter_construction", "code": "key+'':'';(_$F1=0x84c+-0xa75*0x3+0x1713+0.5)&&Math.random()<=_$F1&&(_$F4='*'),_$F2=_$F4+','+_$Wq;", "file": "js-security-v3-rac.js?v=20250605"}], "total_findings": 1}, {"url": "https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js", "filename": "jd-jssdk.js", "file_size": 195815, "args_functions": [{"type": "args_function", "code": "args:r,fn:e,installed:!1},this._installed&&this._installPlugins()}return this}},{key:\"addInterceptor\",value:function(e){\"function\"==typeof e&&(this._interceptors||(this._interceptors=[]),this._interceptors.push(e))}},{key:\"removeInterceptor\",value:function(e){var t", "file": "jd-jssdk.js"}, {"type": "key_generation", "code": "keys=function(e){var t=Object(e),r=[];for(var n in t)c(r).call(r,n);return l(r).call(r),function e(){for(;r.length;){var n=r.pop();if(n in t)return e.value=n,e.done=!1,e}", "file": "jd-jssdk.js"}, {"type": "key_generation", "code": "fastKey:function(e,t){if(!hT(e))return\"symbol\"==typeof e?e:(\"string\"==typeof e?\"S\":\"P\")+e;if(!vT(e,kT)){if(!wT(e))return\"F\";if(!t)return\"E\";jT(e)}", "file": "jd-jssdk.js"}, {"type": "key_generation", "code": "addKeyValues:function(e){try{if(e&&\"[object Object]\"===Object.prototype.toString.call(e))for(var t in e)e.hasOwnProperty(t)&&void 0!==e[t]&&this.customKeyValueData.push({k:t,v:e[t],s:t}", "file": "jd-jssdk.js"}], "encryption_algorithms": [{"type": "sha", "code": "jdappShareRes=function(e){e.hasOwnProperty(\"shareResult\")?t.callback&&t.callback(e):u&&m&&g&&e.hasOwnProperty(\"shareEvent\")?(\"CANCEL_SHARE_CONTENT\"==e.shareEvent?(e.shareResult=\"2\",e.shareChannel=\"\"):(e.shareResult=\"0\",e.shareChannel=function(e){return{WeChat_Friend:\"Wxfriends\",WeChat_FriendTimeline:\"Wxmoments\",Weibo:\"Sinaweibo\",QQFriend_SHARE_CLIENT:\"QQfriends\",QQZone_SHARE_CLIENT:\"QQzone\"}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "setShareInfo=function(t){try{var r=null;if((r=B(t)).shareActionType=j,o||a)if(s)if(e.shareHelper)if(H(r),\"function\"==typeof shareHelper.initShare)I(r);else if(\"function\"==typeof shareHelper.setShareInfoCallback)if(r.callbackSwitcher===k)try{C(\"setShareInfoCallback\",r)}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "callSharePane=function(t){try{var r=null;(r=B(t)).shareActionType=T,s?(o||a)&&e.shareHelper?(H(r),\"function\"==typeof shareHelper.initShare?I(r):\"function\"==typeof shareHelper.callShare?C(\"callShare\",r):e.location.href=_(r)):e.location.href=_(r):(u||f)&&(d||h||a?w?(t.shareActionType=T,q(t)):location.href=P(r):i?location.href=P(r):u&&(location.href=E(r,\"share\")))}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "sendDirectShare=function(t){try{var r,n=null;if((n=B(t)).shareActionType=O,s)(o||a)&&e.shareHelper?\"function\"==typeof shareHelper.initShare?I(n):\"function\"==typeof shareHelper.sendShare?shareHelper.sendShare(n.title,n.content,decodeURIComponent(n.url),decodeURIComponent(n.img),n.channel,n.callbackSwitcher):location.href=_(n):e.location.href=_(n);else if(u||f){if(!n.channel)throw new N(\"分享渠道未设置\");d||h||a?w?(t.shareActionType=O,q(t)):location.href=P(n):i?location.href=P(n):u&&(r=S+'virtual?params={\"category\":\"jump\",\"des\":\"share\",\"type\":\"111\",\"title\":\"'+n.title+'\",\"content\":\"'+n.content+'\",\"shareUrl\":\"'+n.url+'\",\"imageUrl\":\"'+n.img+'\",\"channel\":\"'+n.channel+'\",\"isCallBack\":\"'+n.callbackSwitcher+'\"}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "callSharePane=function(e){return function(){var e,t=d_();return p_(t),(e=t.jm.jdshare).callSharePane.apply(e,arguments)}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "sendDirectShare=function(e){return function(){var e,t=d_();return p_(t),(e=t.jm.jdshare).sendDirectShare.apply(e,arguments)}", "file": "jd-jssdk.js"}, {"type": "sha", "code": "setShareInfo=function(e){return function(){var e,t=d_();return p_(t),(e=t.jm.jdshare).setShareInfo.apply(e,arguments)}", "file": "jd-jssdk.js"}], "api_code": [], "parameter_constructions": [], "total_findings": 11}, {"url": "https://storage.360buyimg.com/jsresource/ws_js/jdwebm.js?v=babel", "filename": "jdwebm.js?v=babel", "file_size": 350417, "args_functions": [{"type": "key_generation", "code": "fontsKey:function(r,e){if(!(0,i[\"inCollecte\"+o(1317,1771)])())return e(r);if(!(0,i[l(222,32)+o(1731,1703)])())return e(r);function o(t,r){return n(t-234,r)}", "file": "jdwebm.js?v=babel"}], "encryption_algorithms": [{"type": "md5", "code": "hex_md5=function(n){return function(n){function t(n,t){return e(n-337,t)}", "file": "jdwebm.js?v=babel"}], "api_code": [{"type": "api_related", "code": "api.m.jd.com\",hMTeE:n(1490,1436)+t(-345,-408),ThPig:n(1449,1429)+n(1393,1400)+\"m\",YsMYp:n(1420,1500)+n(1433,1469),cRRTM:function(n,t){return n+t", "file": "jdwebm.js?v=babel"}, {"type": "api_related", "code": "api.m.jd.com\",\".jddj.com\":t(-277,-353)+\".com\",\".jdyiyao.com\":\"api.jdyiya\"+t(-346,-322),\".jingdong.com\":\"color.jingdong.com\",\".jdtest.net\":t(-349,-415)+n(1352,1361),\".jingdonghealth.cn\":\"api.jingdo\"+t(-214,-230)+\"n\",\".isvjcloud.com\":\"blackhole.isvjcloud.\"+n(1433,1520)", "file": "jdwebm.js?v=babel"}], "parameter_constructions": [], "total_findings": 4}, {"url": "https://wl.jd.com/unify.min.js", "filename": "unify.min.js", "file_size": 105277, "args_functions": [], "encryption_algorithms": [{"type": "md5", "code": "hex_md5:function(e){return this.binl2hex(this.core_md5(this.str2binl(e),e.length*this.chrsz))}", "file": "unify.min.js"}, {"type": "md5", "code": "core_md5:function(e,t){e[t>>5]|=128<<t%32,e[14+(t+64>>>9<<4)]=t;for(var r=**********,i=-271733879,n=-**********,a=271733878,o=0;o<e.length;o+=16){var s=r,d=i,l=n,c=a;r=this.md5_ff(r,i,n,a,e[o+0],7,-680876936),a=this.md5_ff(a,r,i,n,e[o+1],12,-389564586),n=this.md5_ff(n,a,r,i,e[o+2],17,606105819),i=this.md5_ff(i,n,a,r,e[o+3],22,-**********),r=this.md5_ff(r,i,n,a,e[o+4],7,-176418897),a=this.md5_ff(a,r,i,n,e[o+5],12,**********),n=this.md5_ff(n,a,r,i,e[o+6],17,-**********),i=this.md5_ff(i,n,a,r,e[o+7],22,-45705983),r=this.md5_ff(r,i,n,a,e[o+8],7,1770035416),a=this.md5_ff(a,r,i,n,e[o+9],12,-1958414417),n=this.md5_ff(n,a,r,i,e[o+10],17,-42063),i=this.md5_ff(i,n,a,r,e[o+11],22,-1990404162),r=this.md5_ff(r,i,n,a,e[o+12],7,1804603682),a=this.md5_ff(a,r,i,n,e[o+13],12,-40341101),n=this.md5_ff(n,a,r,i,e[o+14],17,-1502002290),i=this.md5_ff(i,n,a,r,e[o+15],22,1236535329),r=this.md5_gg(r,i,n,a,e[o+1],5,-165796510),a=this.md5_gg(a,r,i,n,e[o+6],9,-1069501632),n=this.md5_gg(n,a,r,i,e[o+11],14,643717713)", "file": "unify.min.js"}, {"type": "md5", "code": "md5_cmn:function(e,t,r,i,n,a){return this.safe_add(this.bit_rol(this.safe_add(this.safe_add(t,e),this.safe_add(i,a)),n),r)}", "file": "unify.min.js"}], "api_code": [{"type": "api_related", "code": "api.m.jd.com/?functionId=h5Config\",\"POST\",Mg.toFormParams({functionId:\"h5Config\",appid:\"jdma_h5\"", "file": "unify.min.js"}], "parameter_constructions": [{"type": "parameter_construction", "code": "key+\"=\"+_params[key]);var content=data.impr?data.impr:data.clk,extraVParamStr=vArray.join(\"$\");if(-1!==content.indexOf(\"mercury\")){extraVParamStr+=\"$\";var contentArr=content.split(\"v=\");1<contentArr.length&&(content=contentArr[0]+\"v=\"+extraVParamStr+contentArr[1])", "file": "unify.min.js"}, {"type": "parameter_construction", "code": "key+\"=\"+data[key]);var url=serverAddr+\"?t=\"+data.t+\"&v=\"+vArray.join(\"$\");1==data.action?StorageBridge.set(\"npr-reco\",url):logByImg(url)", "file": "unify.min.js"}], "total_findings": 6}, {"url": "https://storage.360buyimg.com/babelview/sdk/156/1_0_0/jc-com-sdk.min.js", "filename": "jc-com-sdk.min.js", "file_size": 422468, "args_functions": [{"type": "key_generation", "code": "decryptKey=function(t){var e=n;t=t[\"rep\"+e(477)+\"e\"](/\\-/g,\"+\")[e(571)+e(477)+\"e\"](/\\_/g,\"/\");var r=new(c[e(695)+\"ncrypt\"]),a=\"MIICXA\"+e(502)+e(633)+e(408)+e(411)+\"tvI\"+e(580)+e(592)+e(491)+\"kAb\"+e(495)+\"uBOF8L\"+e(721)+\"rBy\"+e(667)+e(514)+\"2RHLJBGWF\"+e(565)+e(511)+e(576)+e(383)+e(730)+e(678)+e(436)+\"Bes\"+e(533)+e(540)+e(693)+e(456)+\"hmN\"+e(536)+\"nlr\"+e(397)+e(441)+\"gpUsJz1+P\"+e(527)+e(725)+e(673)+e(449)+e(482)+e(497)+e(614)+\"sjE4e4IVf\"+e(616)+e(423)+e(669)+\"FdDrtvNRUn68\"+e(661)+e(578)+\"OsLkKT\"+e(544)+e(643)+\"KB\\n\"+e(450)+e(574)+\"JKFlTC\"+e(537)+\"/MnCq9QSsere\"+e(538)+\"0EbHWh\"+e(627)+e(404)+\"kLa\"+e(485)+\"EOuJU1\"+e(416)+e(606)+\"zYP\"+e(631)+\"eOI\"+e(515)+e(554)+\"2e4TV9NVsiwdrtlOIXfYi\"+e(723)+e(500)+\"tP+SLrgkl\"+e(686)+\"NeZ0eet7IxPW27l\"+e(524)+e(632)+e(429)+e(446)+e(655)+\"z+Z\"+e(589)+e(613)+\"YBu\"+e(434)+\"3QvlPKYRw\"+e(682)+e(706)+\"OwJBAPDOg1GxMsh5izQ4\\n\"+e(466)+e(645)+e(680)+\"JSQ\"+e(687)+e(630)+\"oiZ\"+e(582)+e(647)+e(712)+\"8vE0se\"+e(699)+e(696)+e(602)+\"ks3UNE\"+e(512)+\"U8r\"+e(692)+\"vf0\"+e(573)+\"U", "file": "jc-com-sdk.min.js"}, {"type": "key_generation", "code": "parseKey=function(t){try{var e=0,r=0,n=/^\\s*(?:[0-9A-Fa-f][0-9A-Fa-f]\\s*)+$/.test(t)?p(t):m.unarmor(t),a=S.decode(n);if(3===a.sub.length&&(a=a.sub[2].sub[0]),9===a.sub.length){e=a.sub[1].getHexStringValue(),this.n=E(e,16),r=a.sub[2].getHexStringValue(),this.e=parseInt(r,16);var i=a.sub[3].getHexStringValue();this.d=E(i,16);var s=a.sub[4].getHexStringValue();this.p=E(s,16);var o=a.sub[5].getHexStringValue();this.q=E(o,16);var c=a.sub[6].getHexStringValue();this.dmp1=E(c,16);var u=a.sub[7].getHexStringValue();this.dmq1=E(u,16);var f=a.sub[8].getHexStringValue();this.coeff=E(f,16)}", "file": "jc-com-sdk.min.js"}, {"type": "key_generation", "code": "getPrivateKey=function(){var t=\"-----BEGIN RSA PRIVATE KEY-----\\n\";return(t+=e.wordwrap(this.getPrivateBaseKeyB64())+\"\\n\")+\"-----END RSA PRIVATE KEY-----\"}", "file": "jc-com-sdk.min.js"}, {"type": "key_generation", "code": "getPublicKey=function(){var t=\"-----BEGIN PUBLIC KEY-----\\n\";return(t+=e.wordwrap(this.getPublicBaseKeyB64())+\"\\n\")+\"-----END PUBLIC KEY-----\"}", "file": "jc-com-sdk.min.js"}], "encryption_algorithms": [], "api_code": [], "parameter_constructions": [], "total_findings": 4}, {"url": "https://storage11.360buyimg.com/tower/babelnode/babellib-ab1c1b95fb.js", "filename": "babellib-ab1c1b95fb.js", "file_size": 327441, "args_functions": [{"type": "babel_function", "code": "getBabelSdk=function(e){var t;return e&&e.styleId?(t=\"\".concat(e.styleId,\"_\").concat(e.moduleId||\"default\"),m[t]||(m[t]=(0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)((0,o.default)({http:(0,u.default)(e),jump:(0,c.default)(),hooks:(0,p.getHooks)(e)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "babel_function", "code": "babelAdvertInfoNew:function(e){var t=e.body,n=e.success,e=e.error;return f(i(\"getBabelAdvertInfoNew\"),g(h(t),\"advertGroup\",!1),n,e)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "babel_function", "code": "babelProductInfoNew:function(e){var t=e.body,n=e.success,e=e.error,r=arguments.length<=1||void 0===arguments[1]||arguments[1];return f(i(\"getBabelProductInfoNew\"),g(h(t),\"productGroup\",r),n,e)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "args_function", "code": "formatArgs=function(e){var t,n,r=this.useColors;e[0]=(r?\"%c\":\"\")+this.namespace+(r?\" %c\":\" \")+e[0]+(r?\"%c \":\" \")+\"+\"+o.humanize(this.diff),r&&(r=\"color: \"+this.color,e.splice(1,0,r,\"color: inherit\"),e[n=t=0].replace(/%[a-zA-Z%]/g,function(e){\"%%\"!==e&&(t++,\"%c\"===e)&&(n=t)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "key_generation", "code": "fastKey:function(e,t){if(!i(e))return\"symbol\"==typeof e?e:(\"string\"==typeof e?\"S\":\"P\")+e;if(!c(e,m)){if(!d(e))return\"F\";if(!t)return\"E\";r(e)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "key_generation", "code": "keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}", "file": "babellib-ab1c1b95fb.js"}, {"type": "key_generation", "code": "keys=function(e){var t,n=Object(e),r=[];for(t in n)r.push(t);return r.reverse(),function e(){for(;r.length;){var t=r.pop();if(t in n)return e.value=t,e.done=!1,e}", "file": "babellib-ab1c1b95fb.js"}, {"type": "key_generation", "code": "keyframes:function(e){for(var t=arguments.length,n=new Array(1<t?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];var e=C.apply(void 0,[e].concat(n)).join(\"\"),o=I(e);return new he(o,e)}", "file": "babellib-ab1c1b95fb.js"}], "encryption_algorithms": [{"type": "sha", "code": "setShareInfo:function(t){var e;t&&(r?null!=(e=window.jmfe)&&e.setShareInfo(t):window.futureBridge.then(function(e){e.addCompEventListener(\"onAppShareEvent\",function(e){r=!0,e.preventDefault(),null!=(e=window.jmfe)&&e.setShareInfo(t)}", "file": "babellib-ab1c1b95fb.js"}, {"type": "sha", "code": "useSharedInternals=function(){var e=l.default.useContext(k).store,t=d(l.default.useState(e.getState()),2),n=t[0],r=t[1];return l.default.useEffect(function(){e.subscribe(function(){r(e.getState())}", "file": "babellib-ab1c1b95fb.js"}, {"type": "sha", "code": "useSharedStateByPaths=function(){var e=0<arguments.length&&void 0!==arguments[0]?arguments[0]:[],t=1<arguments.length&&void 0!==arguments[1]&&arguments[1],n=d(s.useState(E),2),r=n[0],o=n[1],i=P(e,t);return s.useEffect(function(){o(i)}", "file": "babellib-ab1c1b95fb.js"}], "api_code": [{"type": "api_related", "code": "client.action?functionId=isvObfuscator\",e).then(function(e){e.token&&0===e.errcode?t({status:\"0\",msg:\"get isv token success!\",data:e.token", "file": "babellib-ab1c1b95fb.js"}, {"type": "api_related", "code": "client.action?functionId=getBabelAdvertInfo&client=wh5&clientVersion=1.0.0\",getBabelAdvertInfoNew:\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\",getBabelProductInfo:\"//api.m.jd.com/client.action?functionId=getBabelProductInfo&client=wh5&clientVersion=1.0.0\",getBabelProductInfoNew:\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\",babelAwardCollection:\"//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0\",babelActivityLuckDraw:\"//api.m.jd.com/client.action?functionId=babelActivityLuckDraw&client=wh5&clientVersion=1.0.0\",babelRedOpenRed:\"//api.m.jd.com/client.action?functionId=babelRedOpenRed&client=wh5&clientVersion=1.0.0\"", "file": "babellib-ab1c1b95fb.js"}, {"type": "api_related", "code": "api.m.jd.com/client.action?functionId=isvObfuscator\",e).then(function(e){e.token&&0===e.errcode?t({status:\"0\",msg:\"get isv token success!\",data:e.token", "file": "babellib-ab1c1b95fb.js"}, {"type": "api_related", "code": "api.m.jd.com/client.action?functionId=getBabelAdvertInfo&client=wh5&clientVersion=1.0.0\",getBabelAdvertInfoNew:\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\",getBabelProductInfo:\"//api.m.jd.com/client.action?functionId=getBabelProductInfo&client=wh5&clientVersion=1.0.0\",getBabelProductInfoNew:\"//api.m.jd.com/client.action?functionId=qryCompositeMaterials&client=wh5&clientVersion=1.0.0\",babelAwardCollection:\"//api.m.jd.com/client.action?functionId=babelAwardCollection&client=wh5&clientVersion=1.0.0\",babelActivityLuckDraw:\"//api.m.jd.com/client.action?functionId=babelActivityLuckDraw&client=wh5&clientVersion=1.0.0\",babelRedOpenRed:\"//api.m.jd.com/client.action?functionId=babelRedOpenRed&client=wh5&clientVersion=1.0.0\"", "file": "babellib-ab1c1b95fb.js"}], "parameter_constructions": [], "total_findings": 15}, {"url": "https://storage.360buyimg.com/jsresource/risk/handler.js", "filename": "handler.js", "file_size": 28988, "args_functions": [], "encryption_algorithms": [], "api_code": [{"type": "api_related", "code": "api.m.jd.com/api\",params:{appid:\"risk_\"+e(304)+\"fo\",functionId:\"reportInvokeLog\",body:JSON[\"strin\"+e(367)]({sdkClient:\"handler\",sdkVersion:w,url:(0,o.urlSafeBase64Encode)((0,o[\"getRe\"+e(310)])()),timestamp:Date.now()", "file": "handler.js"}], "parameter_constructions": [], "total_findings": 1}, {"url": "https://storage11.360buyimg.com/tower/babelnode/js/security.89d49f1d.js", "filename": "security.89d49f1d.js", "file_size": 24745, "args_functions": [{"type": "key_generation", "code": "keys=function(t){var e=Object(t),r=[];for(var n in e)r.push(n);return r.reverse(),function t(){for(;r.length;){var n=r.pop();if(n in e)return t.value=n,t.done=!1,t}", "file": "security.89d49f1d.js"}], "encryption_algorithms": [], "api_code": [], "parameter_constructions": [], "total_findings": 1}]