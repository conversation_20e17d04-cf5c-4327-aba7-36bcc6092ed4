#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东页面深度分析工具 - 专门提取预约抢购商品的args参数
"""

import json
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import requests
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import unquote

class JDDeepAnalyzer:
    def __init__(self):
        self.driver = None
        self.products_data = []
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 设置用户代理，模拟移动端
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')
        
        # 使用ChromeDriverManager自动下载和管理驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_all_scripts(self):
        """提取所有脚本内容"""
        scripts_data = []
        script_elements = self.driver.find_elements(By.TAG_NAME, "script")
        
        for i, script in enumerate(script_elements):
            try:
                script_content = script.get_attribute('innerHTML')
                script_src = script.get_attribute('src')
                
                if script_content:
                    scripts_data.append({
                        'index': i,
                        'type': 'inline',
                        'content': script_content,
                        'src': script_src
                    })
                elif script_src:
                    scripts_data.append({
                        'index': i,
                        'type': 'external',
                        'src': script_src
                    })
            except Exception as e:
                continue
                
        return scripts_data
        
    def analyze_script_content(self, script_content):
        """分析脚本内容，寻找args相关数据"""
        found_data = {}
        
        # 更全面的正则表达式模式
        patterns = {
            'args_object': r'args\s*[:=]\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            'roleId': r'["\']?roleId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'key': r'["\']?key["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'strengthenKey': r'["\']?strengthenKey["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'activityId': r'["\']?activityId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'venderId': r'["\']?venderId["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'skuId': r'["\']?skuId["\']?\s*[:=]\s*["\']?(\d+)["\']?',
            'productId': r'["\']?productId["\']?\s*[:=]\s*["\']?(\d+)["\']?',
            'reserveUrl': r'["\']?reserveUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'bookUrl': r'["\']?bookUrl["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            'json_data': r'(\{[^{}]*(?:"args"|"roleId"|"key"|"strengthenKey"|"activityId"|"venderId"|"skuId")[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
            'window_data': r'window\.__([A-Z_]+)__\s*=\s*(\{.+?\});',
            'config_data': r'(?:config|CONFIG)\s*[:=]\s*(\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\})',
        }
        
        for pattern_name, pattern in patterns.items():
            matches = re.findall(pattern, script_content, re.IGNORECASE | re.DOTALL)
            if matches:
                found_data[pattern_name] = matches
                
        return found_data
        
    def extract_from_network_requests(self):
        """从网络请求中提取数据"""
        # 执行JavaScript来获取所有网络请求
        network_data = self.driver.execute_script("""
            var requests = [];
            var originalFetch = window.fetch;
            var originalXHR = window.XMLHttpRequest.prototype.open;
            
            // 拦截fetch请求
            window.fetch = function() {
                requests.push({
                    type: 'fetch',
                    url: arguments[0],
                    options: arguments[1] || {}
                });
                return originalFetch.apply(this, arguments);
            };
            
            // 拦截XMLHttpRequest
            window.XMLHttpRequest.prototype.open = function(method, url) {
                requests.push({
                    type: 'xhr',
                    method: method,
                    url: url
                });
                return originalXHR.apply(this, arguments);
            };
            
            return requests;
        """)
        
        return network_data
        
    def find_product_elements(self):
        """查找页面中的商品元素"""
        product_selectors = [
            '[data-sku]',
            '[data-product-id]',
            '[data-item-id]',
            '.product-item',
            '.goods-item',
            '.item',
            '[class*="product"]',
            '[class*="goods"]',
            '[class*="item"]',
            'img[src*="jfs"]',  # 京东图片
        ]
        
        products = []
        for selector in product_selectors:
            try:
                elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                for element in elements:
                    product_info = {}
                    
                    # 提取各种属性
                    attributes = ['data-sku', 'data-product-id', 'data-item-id', 'data-role', 'data-args']
                    for attr in attributes:
                        value = element.get_attribute(attr)
                        if value:
                            product_info[attr] = value
                    
                    # 提取onclick事件
                    onclick = element.get_attribute('onclick')
                    if onclick:
                        product_info['onclick'] = onclick
                        
                    # 提取href
                    href = element.get_attribute('href')
                    if href:
                        product_info['href'] = href
                        
                    if product_info:
                        products.append(product_info)
                        
            except Exception as e:
                continue
                
        return products
        
    def extract_from_local_storage(self):
        """从localStorage和sessionStorage中提取数据"""
        try:
            local_storage = self.driver.execute_script("return window.localStorage;")
            session_storage = self.driver.execute_script("return window.sessionStorage;")
            
            return {
                'localStorage': dict(local_storage) if local_storage else {},
                'sessionStorage': dict(session_storage) if session_storage else {}
            }
        except:
            return {}
            
    def extract_from_cookies(self):
        """提取cookies"""
        try:
            cookies = self.driver.get_cookies()
            return cookies
        except:
            return []
            
    def analyze_api_responses(self):
        """分析API响应"""
        # 等待页面完全加载
        time.sleep(3)
        
        # 尝试获取页面中的API响应数据
        api_data = self.driver.execute_script("""
            var apiData = [];
            
            // 查找页面中可能的API数据
            var scripts = document.getElementsByTagName('script');
            for (var i = 0; i < scripts.length; i++) {
                var content = scripts[i].innerHTML;
                if (content.includes('api') || content.includes('args') || content.includes('roleId')) {
                    apiData.push({
                        index: i,
                        content: content.substring(0, 1000) // 只取前1000字符
                    });
                }
            }
            
            return apiData;
        """)
        
        return api_data
        
    def run(self, url):
        """运行深度分析"""
        try:
            print("正在设置浏览器...")
            self.setup_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(5)
            
            print("正在提取脚本内容...")
            scripts_data = self.extract_all_scripts()
            
            print("正在分析脚本内容...")
            analyzed_scripts = []
            for script in scripts_data:
                if script.get('content'):
                    analysis = self.analyze_script_content(script['content'])
                    if analysis:
                        analyzed_scripts.append({
                            'script_info': script,
                            'analysis': analysis
                        })
            
            print("正在查找商品元素...")
            products = self.find_product_elements()
            
            print("正在提取存储数据...")
            storage_data = self.extract_from_local_storage()
            
            print("正在提取cookies...")
            cookies = self.extract_from_cookies()
            
            print("正在分析API响应...")
            api_data = self.analyze_api_responses()
            
            print("正在提取网络请求...")
            network_data = self.extract_from_network_requests()
            
            result = {
                'analyzed_scripts': analyzed_scripts,
                'products': products,
                'storage_data': storage_data,
                'cookies': cookies,
                'api_data': api_data,
                'network_data': network_data
            }
            
            return result
            
        except Exception as e:
            print(f"运行时出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    analyzer = JDDeepAnalyzer()
    result = analyzer.run(url)
    
    if result:
        print("\n=== 深度分析结果 ===")
        
        # 保存完整结果
        with open('jd_deep_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("完整结果已保存到 jd_deep_analysis.json")
        
        # 提取关键信息
        key_info = {}
        
        # 从脚本分析中提取关键信息
        for script_analysis in result.get('analyzed_scripts', []):
            analysis = script_analysis.get('analysis', {})
            for key, values in analysis.items():
                if key not in key_info:
                    key_info[key] = []
                key_info[key].extend(values)
        
        # 显示关键信息
        print("\n=== 关键信息摘要 ===")
        for key, values in key_info.items():
            if values:
                print(f"{key}: {values[:5]}")  # 只显示前5个
                
        # 保存关键信息
        with open('jd_key_info.json', 'w', encoding='utf-8') as f:
            json.dump(key_info, f, indent=2, ensure_ascii=False)
        print("\n关键信息已保存到 jd_key_info.json")
        
    else:
        print("分析失败")

if __name__ == "__main__":
    main()
