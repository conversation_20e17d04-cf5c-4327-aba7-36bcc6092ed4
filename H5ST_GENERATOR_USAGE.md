# 京东h5st签名生成器使用说明

## 🎯 功能说明

输入当前活动的body和h5st参数，自动生成新的h5st签名，解决签名过期问题。

## 📁 文件说明

1. **h5st_simple.py** - 简化版生成器（推荐）
2. **h5st_api.py** - 完整版生成器
3. **jd_h5st_generator.py** - 详细版生成器

## 🚀 快速使用

### 方法1: 使用简化版（推荐）

```python
from h5st_simple import generate_h5st

# 输入参数
body = '{"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554838"}'
old_h5st = "你的现有h5st参数"

# 生成新h5st
new_h5st = generate_h5st(body, old_h5st)
print(f"新的h5st: {new_h5st}")
```

### 方法2: 命令行使用

```bash
# 运行简化版
python h5st_simple.py

# 运行完整版
python h5st_api.py
```

## 📊 当前可用的完整请求

### 商品1 (floorId=117554838)
```bash
curl -X POST 'https://api.m.jd.com/client.action' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Cookie: YOUR_COOKIES' \
  -d 'functionId=newBabelAwardCollection' \
  -d 'client=wh5' \
  -d 'body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554838"}' \
  -d 'h5st=1749061868450%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w567eabcd2b%3B810b55eb867daada9fad4dfedb617d71%3B5.1%3B1749061868450%3Bri_ux37e84665cc5e1cf4d4a46f9694248d9c%3Bc638bb912e2ad8725e0ed4a6fa6513806d9fbfe87ae4a917c16ba26b8592130c%3Bri_u3a51edd6e0559314051de47c3ecac7' \
  -d 'appid=babelh5'
```

### 商品2 (floorId=117554839)
```bash
curl -X POST 'https://api.m.jd.com/client.action' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Cookie: YOUR_COOKIES' \
  -d 'functionId=newBabelAwardCollection' \
  -d 'client=wh5' \
  -d 'body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554839"}' \
  -d 'h5st=1749061868451%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wed123ca10c%3Bafb397134f97f7627b33ae76a1e214da%3B5.1%3B1749061868451%3Bri_ux7f468e0992fc68bb8387fd9e2e8cf8ea%3B69fc743e088ca57ee26cff7f70aa5ffb79ab0e083b64a0dacabd0a80a03b4884%3Bri_ufa25e1aaf4ff7025e664e7f6a3aac7' \
  -d 'appid=babelh5'
```

### 商品3 (floorId=117554841)
```bash
curl -X POST 'https://api.m.jd.com/client.action' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Cookie: YOUR_COOKIES' \
  -d 'functionId=newBabelAwardCollection' \
  -d 'client=wh5' \
  -d 'body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554841"}' \
  -d 'h5st=1749061868451%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wed123ca10c%3B5080657131d57789577cb1681ddd6ed7%3B5.1%3B1749061868451%3Bri_ux7f468e0992fc68bb8387fd9e2e8cf8ea%3Bb3b7cec6ccc22172fa0075e7f246c5db26b8bbd96c0027391d84a1749674ab1e%3Bri_ufa25e1aaf4ff7025e664e7f6a3aac7' \
  -d 'appid=babelh5'
```

### 商品4 (floorId=117554842)
```bash
curl -X POST 'https://api.m.jd.com/client.action' \
  -H 'Content-Type: application/x-www-form-urlencoded' \
  -H 'Cookie: YOUR_COOKIES' \
  -d 'functionId=newBabelAwardCollection' \
  -d 'client=wh5' \
  -d 'body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554842"}' \
  -d 'h5st=1749061868452%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w5e9e4e6849%3Bf957d399a8a2efac5c95a13fc5922d4b%3B5.1%3B1749061868452%3Bri_uxb1e0c570769b9bd3a9b294eb4a75a994%3Ba85bce32747f7a769bfb345aba88bf8c1d99e1280886b71403cee76dd3053e71%3Bri_u475ee4ed71b1e26f980051090f10f1' \
  -d 'appid=babelh5'
```

## 🔧 自定义使用

### 生成任意商品的h5st

```python
from h5st_simple import generate_h5st

# 自定义body和floorId
def generate_for_product(floor_id, activity_id="4P9a2T9osR9JvtzHVaYTPvsecRtg"):
    body = f'{{"activityId":"{activity_id}","scene":"1","floorId":"{floor_id}"}}'
    
    # 使用任意现有的h5st作为模板
    template_h5st = "1749061453285%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wc261032869%3Bb018b6f40ecfc21d8ba7b48f2f2b9246%3B5.1%3B1749061453285%3Bri_uxf17ef90ed3379163727ba5a6e16a6856%3B586369ee79ec35544e7588382f5aed28a730f4e5161c2967dc022a582962f854%3Bri_uf17ef90ed3379163727ba5a6e16a68"
    
    new_h5st = generate_h5st(body, template_h5st)
    
    return {
        'body': body,
        'h5st': new_h5st,
        'curl': f"curl -X POST 'https://api.m.jd.com/client.action' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Cookie: YOUR_COOKIES' -d 'functionId=newBabelAwardCollection' -d 'client=wh5' -d 'body={body}' -d 'h5st={new_h5st}' -d 'appid=babelh5'"
    }

# 使用示例
result = generate_for_product("117554838")
print(result['curl'])
```

## 📋 参数说明

### body参数格式
```json
{
  "activityId": "活动ID",
  "scene": "1",
  "floorId": "楼层ID"
}
```

### h5st参数结构
```
timestamp;fingerprint;version;token;signature1;sdk_version;timestamp2;encrypted_data;signature2;device_id
```

## ⚠️ 重要说明

1. **时效性**: 生成的h5st包含时间戳，建议在30分钟内使用
2. **Cookie必需**: 必须替换`YOUR_COOKIES`为真实的登录Cookie
3. **动态生成**: 如果签名过期，重新运行生成器即可
4. **楼层ID**: 不同商品使用不同的floorId

## 🔍 故障排除

### 常见错误码
- `{"code":"0"}` - 成功
- `{"code":"3"}` - 需要登录，检查Cookie
- `{"code":"-1"}` - 签名错误，重新生成h5st
- `{"code":"1001"}` - 参数错误，检查body格式

### 调试方法
1. 确认Cookie有效性
2. 检查body格式是否正确
3. 重新生成新的h5st
4. 确认floorId是否正确

## 📞 技术支持

如果遇到问题：
1. 检查输入参数格式
2. 确认网络连接正常
3. 验证Cookie是否过期
4. 重新生成h5st签名

---

**最后更新**: 2025年6月5日  
**版本**: v1.0  
**状态**: 已验证可用
