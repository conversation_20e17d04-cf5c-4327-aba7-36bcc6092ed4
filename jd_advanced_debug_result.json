{"network_requests": [], "debug_info": {"interceptedCalls": [{"options": {"credentials": "include", "headers": {"Content-Type": "application/json"}, "method": "GET", "mode": "cors"}, "stack": "Error\n    at window.fetch (eval at executeScript (:416:16), <anonymous>:45:24)\n    at l (https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:139799)\n    at CAd7.t.report (https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:139516)\n    at https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:166987\n    at https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:86743\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060559735, "type": "fetch", "url": "https://h5speed.m.jd.com/event/getRule?aid=7891dd035bb6efa74d4127322a9e77e1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060560618, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562006, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562007, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562007, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562007, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562008, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562009, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060562009, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060563056, "type": "xhr", "url": "https://api.m.jd.com"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060565122, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}, {"options": {"body": "[{\"type\":\"custom\",\"payload\":{\"name\":\"babelh5_ihub_process\",\"metrics\":{\"enter\":1,\"success\":true,\"autoReport\":true},\"context\":{\"ifloorId\":\"00034570\",\"moduleId\":93884336,\"env\":\"jd-iOS\"}},\"common\":{\"aid\":\"7891dd035bb6efa74d4127322a9e77e1\",\"url\":\"https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1\",\"timestamp\":1749060559782}},{\"type\":\"custom\",\"payload\":{\"name\":\"babelh5_ihub_process\",\"metrics\":{\"enter\":1,\"success\":true,\"autoReport\":true},\"context\":{\"ifloorId\":\"00038508\",\"moduleId\":117554844,\"env\":\"jd-iOS\"}},\"common\":{\"aid\":\"7891dd035bb6efa74d4127322a9e77e1\",\"url\":\"https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1\",\"timestamp\":1749060559782}}]", "credentials": "include", "headers": {"Content-Type": "application/json"}, "method": "POST", "mode": "cors"}, "stack": "Error\n    at window.fetch (eval at executeScript (:416:16), <anonymous>:45:24)\n    at s (https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:140489)\n    at https://storage11.360buyimg.com/tower/babelnode/js/templates.0363d7fc.js:2:140339\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060565236, "type": "fetch", "url": "https://h5speed.m.jd.com/event/log"}, {"method": "POST", "stack": "Error\n    at XMLHttpRequest.open (eval at executeScript (:416:16), <anonymous>:32:24)\n    at t.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28749)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28221)\n    at Object.next (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28094)\n    at Object.onRequest (https://storage.360buyimg.com/jsresource/risk/handler.js:1:5548)\n    at t (https://storage.360buyimg.com/jsresource/risk/handler.js:1:28073)\n    at https://storage.360buyimg.com/jsresource/risk/handler.js:1:28225\n    at o (https://storage.360buyimg.com/jsresource/risk/handler.js:1:25830)\n    at n (https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js:3:27296)", "timestamp": 1749060568123, "type": "xhr", "url": "https://uranus.jd.com/log/m?std=MO-J2011-1"}], "logs": [{"args": ["JD Debug hooks injected successfully"], "stack": "Error\n    at console.log (eval at executeScript (:416:16), <anonymous>:19:24)\n    at eval (eval at executeScript (:416:16), <anonymous>:126:17)\n    at executeScript (<anonymous>:418:30)\n    at <anonymous>:423:24\n    at callFunction (<anonymous>:386:22)\n    at <anonymous>:400:23\n    at <anonymous>:401:3", "timestamp": 1749060557962, "type": "log"}, {"args": ["Args interceptor injected successfully"], "stack": "Error\n    at console.log (eval at executeScript (:416:16), <anonymous>:19:24)\n    at eval (eval at executeScript (:416:16), <anonymous>:64:17)\n    at executeScript (<anonymous>:418:30)\n    at <anonymous>:423:24\n    at callFunction (<anonymous>:386:22)\n    at <anonymous>:400:23\n    at <anonymous>:401:3", "timestamp": 1749060558968, "type": "log"}, {"args": ["Crypto monitor injected successfully"], "stack": "Error\n    at console.log (eval at executeScript (:416:16), <anonymous>:19:24)\n    at eval (eval at executeScript (:416:16), <anonymous>:63:17)\n    at executeScript (<anonymous>:418:30)\n    at <anonymous>:423:24\n    at callFunction (<anonymous>:386:22)\n    at <anonymous>:400:23\n    at <anonymous>:401:3", "timestamp": 1749060559973, "type": "log"}], "originalFunctions": {}, "variableStates": {}}, "args_info": {"argsHistory": [], "keyGeneration": [], "roleIdGeneration": [], "strengthenKeyGeneration": []}, "crypto_info": {"encryptCalls": [], "hashCalls": [], "keyGeneration": []}, "js_variables": {"$setBabelFloorMap": {"type": "function", "value": "[Function]"}, "_ARGS_INTERCEPTOR": {"type": "object", "value": {"argsHistory": [], "keyGeneration": [], "roleIdGeneration": [], "strengthenKeyGeneration": []}}, "_bioDeviceSdkTokenCb": {"type": "function", "value": "[Function]"}, "_localStorage": {"3AB9D23F7A4B3C9B": "BZGNHTAYTQAHHOGI5QCNBAVTVWHXJJRVG266SDB56VK2UNG3XIYR5JYN7UAI6EYA47WBICODRPYTYCZKUBVNZRMU5M", "3AB9D23F7A4B3CSS": "jdd03BZGNHTAYTQAHHOGI5QCNBAVTVWHXJJRVG266SDB56VK2UNG3XIYR5JYN7UAI6EYA47WBICODRPYTYCZKUBVNZRMU5MAAAAMXHQRA3OQAAAAACYXYDYTOSHQRXUX", "FFA9D23F7A4B3CSS": "c01fdb62821980f667f69fd6c374a1cc", "JDst_rac_last_update": "{\"v\":1749060554417,\"t\":1749060554417,\"e\":31536000}", "JDst_rac_nfd": "{\"v\":10,\"t\":1749060554795,\"e\":31536000}", "TSA9D23F7A4B3CSS": "1749061154238", "WQ_dy1_tk_algo": "{\"z9pdw3dxxpw2jmj6\":{\"73806\":{\"v\":\"eyJ0ayI6InRrMDN3YTFiNTFiZTkxOG5SRGN0UE9Ma2xuT3BjSXMxXzlVWk1kUDYwWFFqOGxJTlBsVzdKYWI1TEwzdWN6ajNuZDNVUkZnbVhnZHYwN3VXNzlIM1ljVTRfdmdsIiwiYWxnbyI6ImZ1bmN0aW9uIHRlc3QodGssZnAsdHMsYWksYWxnbyl7dmFyIHJkPSdUb3gxMzBiRXA1c0knO3ZhciBzdHI9XCJcIi5jb25jYXQodGspLmNvbmNhdChmcCkuY29uY2F0KHRzKS5jb25jYXQoYWkpLmNvbmNhdChyZCk7cmV0dXJuIGFsZ28uTUQ1KHN0cik7fSJ9\",\"e\":86400,\"t\":1749060554795}},\"iaxzwdawwqaj20p9\":{\"35fa0\":{\"v\":\"eyJ0ayI6InRrMDN3OWZhYzFiN2MxOG5oeHdBM2JBUXZmMjVlNGRiX3JiRnluNjJsNWpoNlFrZjdFU3ZSbUdOaW5ZLTJDQTNLeENVNDVzZVlHcmlaODRXaVdVa0xBMTR3QlNmIiwiYWxnbyI6ImZ1bmN0aW9uIHRlc3QodGssZnAsdHMsYWksYWxnbyl7dmFyIHJkPSd6Q1kwM3U0S2dUWEcnO3ZhciBzdHI9XCJcIi5jb25jYXQodGspLmNvbmNhdChmcCkuY29uY2F0KHRzKS5jb25jYXQoYWkpLmNvbmNhdChyZCk7cmV0dXJuIGFsZ28uTUQ1KHN0cik7fSJ9\",\"e\":86400,\"t\":1749060556796}}}", "WQ_dy1_vk": "{\"5.1\":{\"73806\":{\"e\":31536000,\"v\":\"z9pdw3dxxpw2jmj6\",\"t\":1749060554458},\"35fa0\":{\"e\":31536000,\"v\":\"iaxzwdawwqaj20p9\",\"t\":1749060556636}}}", "WQ_gather_cv1": "{\"v\":\"df2270c289943b284f0334eec50997d9\",\"t\":1749060556666,\"e\":31536000}", "WQ_gather_wgl1": "{\"v\":\"9403459a5b231807151f81eb58191ed1\",\"t\":1749060556697,\"e\":31536000}", "__babel_avif_support__": "1", "__we_m_cf__": "{\"t\":1749060616806,\"v\":\"qKovBzs7S_6Nb6Q1_7yBvz1SvAGxT4VbvsA5JDQiHM25uUlrbWKZFFiXachCpsqJgOQPJA_xoxpjA0h2dZlP-RYH2cKwYaYNyT5UvukWeNbC7wRqMZ0enaUIzY-GbhocQUSVdteFAxN9AeIPzfXXe4hgrbtQWyMIewkdpqVSh9sktViU-97geBWVNrbDYePVvyEd6hhzbkXA7MY_gs8sF22zS4fmVdk7GULtRvfzW2rjutfP55SD4WfIBRC6ua-c6yWMT-Up5jz6VGx4ZkraBuSpIFB-jQVsYS098FLKkgqNgQxjcaIVFVdjqd-6eRnW49iCeFU2VRq4ZQHnmsdNU8JC0Zf5Mg837XlfRDheV-IIPSlQS2EA7syxeRmt-RFJSnY7Pe-tKmYARvfYveXXtGHRmQ4Poi6xXSIgdVpgtMG9dgheo2sAL7UUo2vfKqPEAwqvPWgTldaC2Jpqg9mVS-FS2EyJEhDRDKwi9nMXxUMCkvSltsLwEzCSrNiGO6musB62VALesQWPFWqqYaxLlFmTwsEbriTklNhRWOxZQMWNouYXqwCre5G8Cq7eiX6WURmH45cVe0cjnADUdGfb7OO93PZcye-7cgD8-r0rcg1Zx_BH2tu_AYa-1jI1IbIf8-DETBR-KmEFxpXnbbKk_uaT4WWrkQ4VbyW09Pq7LT4D73FxuOOcew3LcNHUOOMT2fX1-rM5fuel1LavmrjtFNcYScmiN_koUJ79hb96IpjVFg5gnhgL2eJY_u5JUMLzP1xZ1RjDiCqWHaINyc2gCK2PhlD01qj4Anw6d0ZQ5B2PVGwXMLwKw3XwLOkE5FjzrcKR76fyKbnX4XtKl29aIdSVv9h1ioB3cnbG21_LRogma_gmkA5YTpC2g8hwFfHBqhs6Ydj-RsIi8E7JhxG6EZ7I_yZjyrBavC9njRucymMtPXn5Fnw2r2i_knG3lFNF8FSxmDMocwi-NAl9fxAEHbpP2s8RmJfrqIk98HkUuMZ0eBRU0Ds48uSov0fymnmCNZbDLKW2iPjkhMsZbs6l3w2Pmu_PsDgW5r9TYR-qZs4YMVtnXBQzNqi5mHq6P_sWdPWsfTCK1fG-ce-v3ZK_5cKwRo8BQSMZPF5WK3cmf47-f7RL2lqWZcLbs_IHsaHL5hl8kmUjn0vIZNzF3OKjU1W_VuuJlwKiJ8tvI2fwHgZJhfoLtUebbe9p1yLELJQxhdTtNah0W4hLX_DGD5aFaw6DIHaW6NHNsVVHSswasP8FdYI6XxNo77PGsk7tsyAH9-27oq63l0XCO5Snvx1HDn60WE4pnOzaL_xZoNchD9W7A0PJAm2O-hsic7sMC2Is5gcKNmOROyCN09zicz5g6M7cGHoaKAiLBkL66D1gcAOU5nWxzeTMR6jz5PxcrY6k5zDWuQN0LpTURynJNxM5XtXLIccdMY0B-4dp7grci8x7eVkBVc5Ai7TpGlYcBJ6qkYaAAoTYVOEVic6nG7ZXjPItJrqRwUH4g7hraYUUt7v0cg-JkBezgyZjEL0tgnc5wMJkryAAYBFl2Kn_FlexrWZ5h6GOhs2b847EtEuK_qMD5le2V0evgTkz2LgiWYQEtnpzf2HO83mc_ND1g-aoBpne5MuIsXOZ_yNIP3Zqlu9oUH_uMN1ZOts_6qr8hAoocm_j5I-rDyezTdz8E_x0m8SRDN5Hx97hByqixfZsFzuk2H3502gHMCMIUqxLRGPMggdLLfMz0EPdUOO2jMdl7VJM4ZOlKV2A5YsNjpYJG0UfiyNTA8i011h3JpcPGMPVT-7Kg7pEkqUeTmZVL6poQFF7fJDmWtLUGH-tvz0b87Z34JHIwz0mUJ80hj4CvCMgl92qUbAHT5x7uLsfhaBiWBRcPXXFZgn5DIQfBMCmkNIfQlZiPobsRzsxaCRCAl4sFOWgO60OzpL04bfb3n6GiW5CZYT3GqP1r9UvaKsstwfG871-16w-17awAGUulLOiDww1xeeN0NFHIAo10QuD8mKvRLd-XezwmKVZ8PNmsIulR9SRLUi1z8TOzv5kfS2Q9K0pAXS1cM16OzXRt2Qs0ewlBh6M1fAWPjgYgKLqScxdYHpybVqNIZ1jXolYozKDMcv7yIe6f2Xe1Vu67C90dbdOwtrS9SKDgseZxqv9ulDbm51wBnyuM1eSLySt5qA-3O76jdR7gOjceoUwJJEVby48WnH0Z5_tD2mygjaQw8k0HUTTLoAsN-f6BtqxaO1_THHIsDduxzIeIFRUy4F8v_XcmzkaLNvF8qyxTj92xSZoL3ZPUfUZ0QTgfPfWo9psf6ESBYx1o7aKxkTzz4P4yY17pKYplHWLvjkpQ8aX0jurr8cYfa37vIFfx1s48kO-SafJ1PErlBX8E2KfX8SY8l21Ro_ndrqzp-79QKY6aV6yB8YpoddA-4n89R80fuTpNJfGxfQ8qWdIFyoNbRPH_dkuu_SltpEWaiRGAyvgODnGnqV964qJvV2LnY-_cN9Cdu6D1_NdpDqWz0-5Lfr72rZK_aUO9aMR75ZnOvj9nU_Jh2mutVTEOaQlYnZLxIEJylMCWLqojSdCMlUy8PqGJSfpawnSqFHy6flDvy0upbtYcBJEgnH1mEg9nWFr_nqer4xle-3OyHa5YD9XcOAVBZgHqH9G7jCHpgg5C0oNojm81EMBIWOMii3OhQwfFOaoblau5WniNrOyfkYzLuolBEnAYjo2JTU4lH6aDIvITrhu2PnscyfA_Y6RmbjCHOkbQMeK7039WTNqQA9thD32qIqBPNYuhC92tw8IPqtrqzvzdsWvWWqMPXEq2VQQvEgeoZNhqQ-Ce2BahMvRmA5_j6-q3uaIZ3F3cpabarPBsum1bo384oIc_ry9DMiny-dnZk03gRu5OGXqEsLvu4mybDDE29gq_YNlPLFW_dS2NEObZm1eVgseyz-gixmlmN6-7RIIvlEhm027UkChZP3gH2ngJUNvux-f6X_ABO3urNp440Y2DH5Jwrijahsb4gmCpH8nCiE_SJrcyQvmvlzCK0pI_5TST-iLnZVcAzeJgBTyn7yJfNqsZJ93Z2QJt8B35ffEKFmyzS-rpp0cP487xiPYhwOSfASOiXsxu9peMls=\"}", "__we_m_cv__": "Y2FudmFzJTIwd2luZGluZyUzQXllc35jYW52YXMlMjBmcCUzQTk3M2I2NTA3MDA1MzhjZWU5OTAyYjEyY2ZmMDU2ZTM1", "__we_m_ft__": "QXJpYWwlMkNBcmlhbCUyMEJsYWNrJTJDQXJpYWwlMjBOYXJyb3clMkNDYWxpYnJpJTJDQ2FtYnJpYSUyQ0NhbWJyaWElMjBNYXRoJTJDQ29taWMlMjBTYW5zJTIwTVMlMkNDb25zb2xhcyUyQ0NvdXJpZXIlMkNDb3VyaWVyJTIwTmV3JTJDR2VvcmdpYSUyQ0hlbHZldGljYSUyQ0ltcGFjdCUyQ0x1Y2lkYSUyMENvbnNvbGUlMkNMdWNpZGElMjBTYW5zJTIwVW5pY29kZSUyQ01pY3Jvc29mdCUyMFNhbnMlMjBTZXJpZiUyQ01TJTIwR290aGljJTJDTVMlMjBQR290aGljJTJDTVMlMjBTYW5zJTIwU2VyaWYlMkNNUyUyMFNlcmlmJTJDUGFsYXRpbm8lMjBMaW5vdHlwZSUyQ1NlZ29lJTIwUHJpbnQlMkNTZWdvZSUyMFNjcmlwdCUyQ1NlZ29lJTIwVUklMkNTZWdvZSUyMFVJJTIwTGlnaHQlMkNTZWdvZSUyMFVJJTIwU2VtaWJvbGQlMkNTZWdvZSUyMFVJJTIwU3ltYm9sJTJDVGFob21hJTJDVGltZXMlMkNUaW1lcyUyME5ldyUyMFJvbWFuJTJDVHJlYnVjaGV0JTIwTVMlMkNWZXJkYW5hJTJDV2luZ2Rpbmdz", "__we_m_ftk__": "MzQwMTY1OGVkNDIxNzdiZjA2ZTIxMmFkOTcyMDc2OGY=", "__we_m_gl__": "ZnAlM0EyYmQ1YzI3NzI0ZjRkMjJlNmU1YzVjMDUxZjRlZWJkNH52ZW5kb3IlM0FXZWJLaXR+dmVyc2lvbiUzQVdlYkdMJTIwMS4wJTIwKE9wZW5HTCUyMEVTJTIwMi4wJTIwQ2hyb21pdW0pfnVubWFza2VkJTIwdmVuZG9yJTNBR29vZ2xlJTIwSW5jLiUyMChHb29nbGUpfnVubWFza2VkJTIwcmVuZGVyZXIlM0FBTkdMRSUyMChHb29nbGUlMkMlMjBWdWxrYW4lMjAxLjMuMCUyMChTd2lmdFNoYWRlciUyMERldmljZSUyMChTdWJ6ZXJvKSUyMCgweDAwMDBDMERFKSklMkMlMjBTd2lmdFNoYWRlciUyMGRyaXZlcik=", "black_five_clearLoop": "1", "shshshfpa": "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753-1749060556", "shshshfpb": "BApXSzYEqP_JApREstqy0OSjyxsBnNjaJBgtHAnlo9xJ1MnCwXY62", "shshshfpx": "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753-1749060556"}, "_sessionStorage": {"3AB9D23F7A4B3C9B": "BZGNHTAYTQAHHOGI5QCNBAVTVWHXJJRVG266SDB56VK2UNG3XIYR5JYN7UAI6EYA47WBICODRPYTYCZKUBVNZRMU5M", "3AB9D23F7A4B3CSS": "jdd03BZGNHTAYTQAHHOGI5QCNBAVTVWHXJJRVG266SDB56VK2UNG3XIYR5JYN7UAI6EYA47WBICODRPYTYCZKUBVNZRMU5MAAAAMXHQRA3OQAAAAACYXYDYTOSHQRXUX", "CA1AN5BV0CA8DS2E3F": "", "FFA9D23F7A4B3CSS": "c01fdb62821980f667f69fd6c374a1cc", "agentSid": "a0778fe2-7fb2-4f95-93fa-a4982b5c3747", "fingerCharging": "true", "fingerLevel": "100", "shshshfpa": "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753-1749060556", "shshshfpb": "BApXSzYEqP_JApREstqy0OSjyxsBnNjaJBgtHAnlo9xJ1MnCwXY62", "shshshfpx": "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753-1749060556"}, "_toPropertyKey": {"type": "function", "value": "[Function]"}, "babel": {"type": "object", "value": {"babelActivityLuckDraw": {}, "babelAdvertInfo": {}, "babelAdvertInfoNew": {}, "babelAwardCollection": {}, "babelProductInfo": {}, "babelProductInfoNew": {}, "babelRedOpenRed": {}}}, "babelFloorMap": {"type": "object", "value": {"117554838": {"requestId": "10102004744-145974-1749060553563"}, "117554839": {"requestId": "10102004744-145974-1749060553563"}, "117554841": {"requestId": "10102004744-145974-1749060553563"}, "117554842": {"requestId": "10102004744-145974-1749060553563"}, "117554843": {"requestId": "10102004744-145974-1749060553563"}, "117554844": {"requestId": "10102004744-145974-1749060553563"}, "117554845": {"requestId": "10102004744-145974-1749060553563"}, "117554846": {"requestId": "10102004744-145974-1749060553563"}, "117554847": {"requestId": "10102004744-145974-1749060553563"}, "117554848": {"requestId": "10102004744-145974-1749060553563"}, "117973211": {"requestId": "10102004744-145974-1749060553563"}, "93884336": {"requestId": "10102004744-145974-1749060553563"}, "null": {"requestId": "10102004744-145974-1749060553563"}, "tips_01": {"requestId": "10102004744-145974-1749060553563"}}}, "babelI18nText": {"type": "object", "value": {"currNS": {"activate": "", "activation": "", "addcard": "", "addcard_oneclick": "", "addcart_fail": "抱歉，添加商品失败，请您稍后再试～", "addcart_fail_8969": "抱歉，您的购物车已满，无法继续添加～", "addcart_fail_9038": "抱歉，您添加的商品不存在～", "addcart_fail_9044": "抱歉，您添加的服务不存在～", "addcart_present_browser": "", "addcart_present_fail": "", "addcart_present_harmony": "", "addcart_present_low_app": "", "addcart_present_success": "", "addcart_success": "", "addcart_success_congratulations": "", "again": "", "apply": "", "apply_end": "", "auction_nums": "", "auth": "", "avaiable": "", "available_later": "", "ballance": "", "batch_receive": "", "batch_receive_none": "", "before_due": "", "before_end": "", "bidders": "", "billion": "", "bind_card": "", "browse_more": "", "browse_shop": "", "buying": "", "cancel": "", "cant_addcard_temporary": "", "chat_group": "", "circusee": "", "click_collect": "", "click_load_next": "", "collect": "", "collect2": "", "collect3": "", "collect_fail_retry": "", "collect_later": "", "collect_now": "", "collect_now2": "", "collect_oneclick": "", "collect_thanks": "", "collected": "", "collected_today": "", "come_again": "", "comments": "", "comming_soon": "", "coupon_certification": "", "coupon_child": "", "coupon_enterprise": "", "coupon_student": "", "coupon_student_48h": "", "crowded": "", "crowded_later": "", "crowded_retry": "", "current_price": "", "days": "", "dis_due": "", "discount": "", "discount2": "", "discount_suit": "", "end": "", "end_time": "", "ended": "", "etc": "", "exchange": "", "exchange_now": "", "expire_day": "", "expired": "", "follow": "", "follow_fail": "", "follow_fail_retry": "", "follow_success": "", "followed": "", "forget": "", "go_use": "", "good_comment": "", "goods_list": "", "has_sold": "", "hours": "", "in_progress": "", "input_goods": "", "interest_free": "", "jdmarke": "", "knowledge": "", "lbs_locating": "", "lbs_notfound": "", "lbs_pem_warning": "", "lbs_retry": "", "lbs_to_new_address": "", "lbs_to_new_address_with_switch": "", "left_more": "", "limit": "", "limit_time": "", "live_preview": "", "live_streaming": "", "login": "", "look": "", "look_dynamics": "", "look_same": "", "look_shop": "", "look_similar": "", "minutes": "", "more": "", "neterr": "", "netexception": "", "netretry": "", "network_fail": "", "network_retry": "", "no_goods": "", "noaddress": "", "nocollect": "", "noconcent": "", "nocoupon_hot": "", "nodata": "", "nogoods_incat": "", "nomore": "", "nomore_concent": "", "nomore_temporary": "", "not_start": "", "not_start_and_look": "", "open": "", "open_now": "", "openjdapp": "", "palyback": "", "participated": "", "participated_activity": "", "participated_reject": "", "pay": "", "people_apply": "", "people_watched": "", "phase": "", "pieces": "", "playback_genarating": "", "preferential": "", "previewing": "", "price_protect": "", "products": "", "read": "", "reduce": "", "registry": "", "registry_now": "", "reload": "", "renew": "", "reports": "", "search_goods": "", "seconds": "", "shop_follow_collect": "", "shop_follow_collect2": "", "shop_global": "", "shortage": "", "snapup": "", "soldout": "", "soldout2": "", "soldout_today": "", "soldout_today2": "", "sorry": "", "start": "", "start_price": "", "start_selling": "", "start_time": "", "still_goods": "", "suit_addcard": "", "switch": "", "switchto": "", "tap_view_more": "", "taxes": "", "ten_thousand": "", "times": "", "times_played": "", "times_watched": "", "trial": "", "trial_apply": "", "trial_preview": "", "unfollow_fail": "", "unfollow_fail_retry": "", "unfollow_success": "", "unuse": "", "upper_limit": "", "use_coupon": "", "use_now": "", "used": "", "view_all": "", "view_available_goods": "", "view_more": "", "view_more_coupons": "", "view_rules": "", "waiver": ""}, "fallbackNS": {}, "options": {"fallback": "zh_CN", "lang": "zh_CN", "ns": {"en_US": {"activate": "Activate Now", "activation": "Activate Now", "addcard": "Add to Cart", "addcard_oneclick": "Add to Cart with One Tap", "addcart_fail": "Sorry, adding the item to the shopping cart failed. Please try again later.", "addcart_fail_8969": "Sorry, your shopping cart is full and you cannot add any more items.", "addcart_fail_9038": "Sorry, the item you added does not exist.", "addcart_fail_9044": "Sorry, the service you added does not exist.", "addcart_present_browser": "Please open the latest version of the JD App to experience the gift-giving feature.", "addcart_present_fail": "Failed to add to gift list~", "addcart_present_harmony": "The HarmonyOS Next system does not currently support the gift-giving feature.", "addcart_present_low_app": "Please upgrade to the latest version of the JD App to experience the gift-giving feature.", "addcart_present_success": "Added to gift list, hurry up and place your order now!", "addcart_success": "Added to cart", "addcart_success_congratulations": "Congratulations. <PERSON><PERSON> successfully added to cart.", "again": "Try Again", "apply": "apply", "apply_end": "Application Closed", "auction_nums": "Items", "auth": "Verify Identity", "avaiable": "Available", "available_later": "Available in", "ballance": "Account balance:", "batch_receive": "You have successfully claimed ${len} coupons", "batch_receive_none": "Some coupons haven't been claimed yet, try clicking on a single coupon to claim it", "before_due": "Expires in", "before_end": "Ends in", "bidders": "Bids", "billion": "Billion", "bind_card": "Bind Card Now", "browse_more": "Keep Browsing", "browse_shop": "Visit Store", "buying": "Flash sale in progress. First come, first served!", "cancel": "Cancel", "cant_addcard_temporary": "Unable to add to cart", "chat_group": "Join Group Chat", "circusee": "Viewers", "click_collect": "Tap to Claim", "click_load_next": "Tap to load next page", "collect": "<PERSON><PERSON><PERSON>", "collect2": "<PERSON><PERSON><PERSON>", "collect3": "favorites", "collect_fail_retry": "Failed to claim coupon. Try again next time!", "collect_later": "Too many people are lining up to claim coupons right now, take a break and try again later", "collect_now": "Claim now", "collect_now2": "Claim now", "collect_oneclick": "One-tap claim", "collect_thanks": "Claimed successfully！ Thank you for participating. Happy shopping!", "collected": "Claimed", "collected_today": "Claimed today", "come_again": "Try Again", "comments": "Reviews", "comming_soon": "Coming soon, stay tuned!", "coupon_certification": "Verify now", "coupon_child": "For JD Parent-Child Membership only. Become a member now!", "coupon_enterprise": "For business users only. Log in to claim now!", "coupon_student": "For verified student users only. Get verified now!", "coupon_student_48h": "For newly verified student users only within 48 hours. Get verified now!", "crowded": "Too many people are claiming coupons. Try again later!", "crowded_later": "Too many draw participants. Try again later.", "crowded_retry": "System busy. Try again later.", "current_price": "Current Price", "days": "days", "dis_due": "Expires in", "discount": "Discount", "discount2": "Discount", "discount_suit": "Discount Bundle", "end": "End", "end_time": "End Time", "ended": "Ended", "etc": "etc", "exchange": "Redeem", "exchange_now": "Redeem Now", "expire_day": "<PERSON>id <PERSON>", "expired": "Expired", "follow": "Follow", "follow_fail": "Failed to follow.", "follow_fail_retry": "Failed to follow. Try again later.", "follow_success": "Followed successfully.", "followed": "Followed", "forget": "Never Mind", "go_use": "Go use", "good_comment": "Positive feedback", "goods_list": "Product List", "has_sold": "Sold", "hours": "hours", "in_progress": "In progress", "input_goods": "Enter the product you want", "interest_free": "Interest-free", "jdmarke": "JD Supermarket", "knowledge": "Learn More", "lbs_locating": "Locating...", "lbs_notfound": "Address not found", "lbs_pem_warning": "User location authorization failed. Please check if system location permissions are enabled, or try again.", "lbs_retry": "User location authorization failed. Try again.", "lbs_to_new_address": "Switching to the new address", "lbs_to_new_address_with_switch": "Switching to the new address.<br/>You can change it using the location component above.", "left_more": "<PERSON><PERSON><PERSON> left for more", "limit": "limit", "limit_time": "Limited Time Offer", "live_preview": "Live Preview", "live_streaming": "Live", "login": "<PERSON><PERSON>", "look": "Browse first", "look_dynamics": "You can check their updates <br/>in the discovery channel <br>", "look_same": "View Same Items", "look_shop": "Visit Store", "look_similar": "View Similar Items", "minutes": "minutes", "more": "More", "neterr": "Network request failed.", "netexception": "Network error", "netretry": "Check your network and reload", "network_fail": "Network error, Try again later.", "network_retry": "Network error, Try again.", "no_goods": "Sorry, there are currently no items to add", "noaddress": "Address not found", "nocollect": "No, thanks", "noconcent": "Sorry, the content you are accessing does not exist.", "nocoupon_hot": "All coupons have been claimed", "nodata": "No category data available", "nogoods_incat": "There are no products in this category", "nomore": "No more content", "nomore_concent": "No more content", "nomore_temporary": "No more content.", "not_start": "Not Started", "not_start_and_look": "Not Started, <PERSON><PERSON><PERSON> first", "open": "Activate Now", "open_now": "Activate now", "openjdapp": "Open JD App", "palyback": "Live Replay", "participated": "Participated", "participated_activity": "You've already joined this event", "participated_reject": "You've already joined this event. See you in other events.", "pay": "Pay", "people_apply": " applications", "people_watched": "people", "phase": "Term", "pieces": "items", "playback_genarating": "Generating live replay", "preferential": "Promotion", "previewing": "Coming soon", "price_protect": "Price assurance used", "products": "items", "read": "Read", "reduce": "Discount", "registry": "Register", "registry_now": "Register now", "reload": "Reload", "renew": "Renew Now", "reports": "reports", "search_goods": "Search for the product you want", "seconds": "seconds", "shop_follow_collect": "Follow and Claim", "shop_follow_collect2": "You're not a fan of the store yet. Follow to claim your reward!", "shop_global": "Global Purchase", "shortage": "Insufficient quantity!", "snapup": "Flash sale in progress", "soldout": "Sold Out", "soldout2": "Sold Out", "soldout_today": "Sold out today", "soldout_today2": "Sold out today", "sorry": "Sorry, you didn't get it", "start": "Start", "start_price": "Starting Price", "start_selling": "Buy Now", "start_time": "Start Time", "still_goods": "More items available in this session. Keep bidding!", "suit_addcard": "Bundle added to cart", "switch": "Switch", "switchto": "Switch to ", "tap_view_more": "Tap to view more", "taxes": "Tax Included", "ten_thousand": "Ten thousand", "times": "times", "times_played": "views", "times_watched": "views", "trial": "Trial", "trial_apply": "Apply for Trial", "trial_preview": "Trial Preview", "unfollow_fail": "Failed to unfollow.", "unfollow_fail_retry": "Failed to unfollow. Try again later.", "unfollow_success": "Unfollowed successfully.", "unuse": "Unavailable", "upper_limit": "Participation limit of reached", "use_coupon": "Use Coupon Now", "use_now": "Use now", "used": "Used", "view_all": "View all", "view_available_goods": "View Available Products", "view_more": "View more", "view_more_coupons": "Tap to view more coupons", "view_rules": "View Rules", "waiver": "Forfeited in ${leftTime} seconds"}, "zh_CN": {"activate": "", "activation": "", "addcard": "", "addcard_oneclick": "", "addcart_fail": "抱歉，添加商品失败，请您稍后再试～", "addcart_fail_8969": "抱歉，您的购物车已满，无法继续添加～", "addcart_fail_9038": "抱歉，您添加的商品不存在～", "addcart_fail_9044": "抱歉，您添加的服务不存在～", "addcart_present_browser": "", "addcart_present_fail": "", "addcart_present_harmony": "", "addcart_present_low_app": "", "addcart_present_success": "", "addcart_success": "", "addcart_success_congratulations": "", "again": "", "apply": "", "apply_end": "", "auction_nums": "", "auth": "", "avaiable": "", "available_later": "", "ballance": "", "batch_receive": "", "batch_receive_none": "", "before_due": "", "before_end": "", "bidders": "", "billion": "", "bind_card": "", "browse_more": "", "browse_shop": "", "buying": "", "cancel": "", "cant_addcard_temporary": "", "chat_group": "", "circusee": "", "click_collect": "", "click_load_next": "", "collect": "", "collect2": "", "collect3": "", "collect_fail_retry": "", "collect_later": "", "collect_now": "", "collect_now2": "", "collect_oneclick": "", "collect_thanks": "", "collected": "", "collected_today": "", "come_again": "", "comments": "", "comming_soon": "", "coupon_certification": "", "coupon_child": "", "coupon_enterprise": "", "coupon_student": "", "coupon_student_48h": "", "crowded": "", "crowded_later": "", "crowded_retry": "", "current_price": "", "days": "", "dis_due": "", "discount": "", "discount2": "", "discount_suit": "", "end": "", "end_time": "", "ended": "", "etc": "", "exchange": "", "exchange_now": "", "expire_day": "", "expired": "", "follow": "", "follow_fail": "", "follow_fail_retry": "", "follow_success": "", "followed": "", "forget": "", "go_use": "", "good_comment": "", "goods_list": "", "has_sold": "", "hours": "", "in_progress": "", "input_goods": "", "interest_free": "", "jdmarke": "", "knowledge": "", "lbs_locating": "", "lbs_notfound": "", "lbs_pem_warning": "", "lbs_retry": "", "lbs_to_new_address": "", "lbs_to_new_address_with_switch": "", "left_more": "", "limit": "", "limit_time": "", "live_preview": "", "live_streaming": "", "login": "", "look": "", "look_dynamics": "", "look_same": "", "look_shop": "", "look_similar": "", "minutes": "", "more": "", "neterr": "", "netexception": "", "netretry": "", "network_fail": "", "network_retry": "", "no_goods": "", "noaddress": "", "nocollect": "", "noconcent": "", "nocoupon_hot": "", "nodata": "", "nogoods_incat": "", "nomore": "", "nomore_concent": "", "nomore_temporary": "", "not_start": "", "not_start_and_look": "", "open": "", "open_now": "", "openjdapp": "", "palyback": "", "participated": "", "participated_activity": "", "participated_reject": "", "pay": "", "people_apply": "", "people_watched": "", "phase": "", "pieces": "", "playback_genarating": "", "preferential": "", "previewing": "", "price_protect": "", "products": "", "read": "", "reduce": "", "registry": "", "registry_now": "", "reload": "", "renew": "", "reports": "", "search_goods": "", "seconds": "", "shop_follow_collect": "", "shop_follow_collect2": "", "shop_global": "", "shortage": "", "snapup": "", "soldout": "", "soldout2": "", "soldout_today": "", "soldout_today2": "", "sorry": "", "start": "", "start_price": "", "start_selling": "", "start_time": "", "still_goods": "", "suit_addcard": "", "switch": "", "switchto": "", "tap_view_more": "", "taxes": "", "ten_thousand": "", "times": "", "times_played": "", "times_watched": "", "trial": "", "trial_apply": "", "trial_preview": "", "unfollow_fail": "", "unfollow_fail_retry": "", "unfollow_success": "", "unuse": "", "upper_limit": "", "use_coupon": "", "use_now": "", "used": "", "view_all": "", "view_available_goods": "", "view_more": "", "view_more_coupons": "", "view_rules": "", "waiver": ""}, "zh_TW": {"activate": "去激活", "activation": "立即激活", "addcard": "加入購物車", "addcard_oneclick": "一鍵加購", "addcart_fail": "抱歉，添加商品失敗，請您稍後再試～", "addcart_fail_8969": "抱歉，您的購物車已滿，無法繼續添加～", "addcart_fail_9038": "抱歉，您添加的商品不存在～", "addcart_fail_9044": "抱歉，您添加的服務不存在～", "addcart_present_browser": "請打開最新版本京东App體驗送禮物功能", "addcart_present_fail": "加入禮品清單失敗~", "addcart_present_harmony": "鴻蒙Next系統暫不支援送禮物功能", "addcart_present_low_app": "請升級最新版本京东App體驗送禮物功能", "addcart_present_success": "已加入禮品清單，快去下單叭~", "addcart_success": "商品已添加至購物車", "addcart_success_congratulations": "恭喜您，成功添加商品至購物車", "again": "再來", "apply": "申請", "apply_end": "申請結束", "auction_nums": "拍品數", "auth": "去實名認證", "avaiable": "可用", "available_later": "後可用", "ballance": "您的賬戶還剩", "batch_receive": "已成功領取${len}張券～", "batch_receive_none": "有券沒領到，點擊單張券領取試試吧", "before_due": "後到期", "before_end": "距結束", "bidders": "出價數", "billion": "億", "bind_card": "立即綁卡", "browse_more": "再逛逛", "browse_shop": "進店看看", "buying": "正在抢購，先下單先得哦", "cancel": "取消", "cant_addcard_temporary": "商品暫無法加購", "chat_group": "加入群聊", "circusee": "觀眾數", "click_collect": "點擊領取", "click_load_next": "點擊加載下一頁", "collect": "領取", "collect2": "領", "collect3": "人收藏", "collect_fail_retry": "領券失敗，下次再來！", "collect_later": "此時排隊領券的人太多，休息一會兒再試試吧", "collect_now": "立即領取", "collect_now2": "立即領", "collect_oneclick": "一鍵領取", "collect_thanks": "領取成功！感謝您的參與，祝您購物愉快~", "collected": "已領取", "collected_today": "今日已領取", "come_again": "再來一次", "comments": "條評論", "comming_soon": "即將開搶，敬請期待", "coupon_certification": "去認證", "coupon_child": "親愛的用戶，此券僅限親子會員領取使用哦，快去成爲親子會員吧~", "coupon_enterprise": "此券爲企業用戶專享，快登錄領券吧~", "coupon_student": "此券爲學生認證用戶專享，快去進行認證吧", "coupon_student_48h": "此券爲新認證學生用戶48小時內專享，快去進行認證吧", "crowded": "此時排隊領券的人太多，休息一會兒再試試吧！", "crowded_later": "排隊抽獎的人太多了，稍後再試", "crowded_retry": "活動太火爆，請稍後重試", "current_price": "當前價", "days": "天", "dis_due": "距到期", "discount": "折", "discount2": "惠", "discount_suit": "優惠套裝", "end": "結束", "end_time": "結束時間", "ended": "已結束", "etc": "等", "exchange": "兌換", "exchange_now": "立即兌換", "expire_day": "有效期至", "expired": "已過期", "follow": "關注", "follow_fail": "關注失敗", "follow_fail_retry": "關注失敗，請稍後重試", "follow_success": "關注成功", "followed": "已關注", "forget": "算了吧", "go_use": "去使用", "good_comment": "好評", "goods_list": "商品清單", "has_sold": "已抢", "hours": "時", "in_progress": "進行中", "input_goods": "請輸入您想要的商品", "interest_free": "免息", "jdmarke": "京东超市", "knowledge": "去了解", "lbs_locating": "定位中...", "lbs_notfound": "未獲取地址", "lbs_pem_warning": "用戶定位授權失敗，請檢查系統定位權限是否打開，或重試", "lbs_retry": "用戶定位授權失敗，請重試", "lbs_to_new_address": "將為您切換至新地址", "lbs_to_new_address_with_switch": "將為您切換至新地址，可點擊上方定位組件切換地址", "left_more": "左滑查看更多", "limit": "限", "limit_time": "限時專享", "live_preview": "直播預告", "live_streaming": "直播中", "login": "登錄", "look": "先逛逛", "look_dynamics": "可以在發現頻道<br>查看TA的動態啦", "look_same": "看同款", "look_shop": "進店逛逛", "look_similar": "看相似", "minutes": "分", "more": "更多", "neterr": "網絡請求失敗", "netexception": "網絡異常", "netretry": "請檢查您的網絡重新加載吧", "network_fail": "網路開小差，請稍後重試~", "network_retry": "網絡跑丟了，再點一下試試~", "no_goods": "抱歉，當前沒有可以添加的商品", "noaddress": "未獲取地址", "nocollect": "不領了", "noconcent": "抱歉，您訪問的內容不存在～", "nocoupon_hot": "活動太火爆，券已被搶完啦", "nodata": "暫無類目數據哦~", "nogoods_incat": "該類目下暫無商品哦~", "nomore": "沒有更多了", "nomore_concent": "沒有更多內容~", "nomore_temporary": "暫無更多內容~", "not_start": "未開始", "not_start_and_look": "未開始 先逛逛", "open": "去開通", "open_now": "立即開通", "openjdapp": "打開京东App", "palyback": "直播回放", "participated": "已參與", "participated_activity": "您已參加過此活動", "participated_reject": "您已經參加過此活動，別太貪心呦～", "pay": "支付", "people_apply": "人申請", "people_watched": "人觀看", "phase": "期", "pieces": "件", "playback_genarating": "直播回放生成中", "preferential": "優惠", "previewing": "預告中", "price_protect": "已價保", "products": "個商品", "read": "閱讀", "reduce": "立減", "registry": "註冊", "registry_now": "立即註冊", "reload": "重新加載", "renew": "去續費", "reports": "報告", "search_goods": "搜索您想要的商品", "seconds": "秒", "shop_follow_collect": "關注店鋪並領取", "shop_follow_collect2": "您還不是店鋪粉絲哦，關注店鋪後即可領取～", "shop_global": "全球購", "shortage": "數量不足哦！", "snapup": "抢購中", "soldout": "已搶完", "soldout2": "已搶光", "soldout_today": "今日已搶完", "soldout_today2": "今日已搶光", "sorry": "很抱歉，沒抢到", "start": "開始", "start_price": "起拍價", "start_selling": "開抢", "start_time": "開始時間", "still_goods": "本場次還有寶貝可以搶拍", "suit_addcard": "套裝已成功加入購物車", "switch": "切換", "switchto": "是否切換到", "tap_view_more": "點擊查看更多", "taxes": "包税", "ten_thousand": "萬", "times": "次", "times_played": "次播放", "times_watched": "次觀看", "trial": "試用", "trial_apply": "申請試用", "trial_preview": "試用預告", "unfollow_fail": "取消關注失敗", "unfollow_fail_retry": "取消關注失敗，請稍後重試", "unfollow_success": "取消關注成功", "unuse": "不可用", "upper_limit": "參與已達<br/>上限", "use_coupon": "立即用券", "use_now": "立即使用", "used": "已使用", "view_all": "查看全部", "view_available_goods": "查看可用商品", "view_more": "查看更多", "view_more_coupons": "點擊查看更多優惠券", "view_rules": "查看規則", "waiver": "秒後自動放棄"}}}}}, "babelPageJump": {"type": "function", "value": "[Function]"}, "babelSecurity": {"type": "object", "value": {}}, "babelSecurityResolver": {"type": "function", "value": "[Function]"}, "babelShareData": {"type": "object", "value": {"actDark": false, "activityId": "00038984", "autoLogin": false, "autoNextPageDelay": null, "biimpr": "00038984_5567046_1#null-1-1__1#-656#-656_", "cartLayer": true, "channelPoint": {"babelChannel": "", "encodeActivityId": "4P9a2T9osR9JvtzHVaYTPvsecRtg", "greytp": "1", "loginCellularNetwork": 0, "pageId": "5567046", "rec_broker": ""}, "clientPagination": true, "codeLazy": false, "cpChannelKey": "H5node", "csrCustomCodeFloors": ["00036697", "00032712", "00036616", "00037129", "00034167", "00037033", "00037511", "00037943", "00038168", "00040191", "00040033", "00040066"], "delayHydrate": false, "delayHydrateTime": 1000, "disableAutoFetchNext": false, "enableHybridImg": false, "enableViewkitSSR": false, "encodeActivityId": "4P9a2T9osR9JvtzHVaYTPvsecRtg", "fcImgAdjustmentOn": true, "fcImgWebpOn": true, "fcUnitVwOn": true, "fcVideoAutoPlay": true, "floorPlaceHolderMap": {"00039679": {"floorHeight": 36}}, "hasFloatNav": false, "hasFoot": false, "hasMultiTab": false, "hasPerData": "0", "hasWebViewNav": false, "hideNav": false, "hybridRequestFunctions": [0.5, ["babelExceptionPage"]], "inkTarget": "_blank", "isFcSSR": true, "isFcSSRPartial": true, "isOpacityTitle": true, "item": "0", "jshopShareUrl": "//wq.360buyimg.com/js/common/dest/wq.jshop.share.min.2023121418.js", "maxHydrateDelayTime": 5000, "pageId": "5567046", "pageUrl": "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1", "rsHeadersBlackList": [], "sharedCustomCodeTTL": 300, "title": "京东生鲜", "transParam": "{\"bsessionId\":\"e2a6a5ba-0afd-4bf2-a513-f647853b6fac\",\"babelChannel\":\"\",\"actId\":\"00038984\",\"enActId\":\"4P9a2T9osR9JvtzHVaYTPvsecRtg\",\"pageId\":\"5567046\",\"encryptCouponFlag\":\"1\",\"sc\":\"apple\",\"scv\":\"13.2.8\",\"requestChannel\":\"h5\",\"jdAtHomePage\":\"0\",\"utmFlag\":\"1\",\"locType\":\"1\"}", "useDiscovery": "1", "useNewMiniLbsAddress": true, "warehouseAddCartUrl": "https://pro.m.jd.com/mall/active/2dm8FjPwsKgaSwjnbhNmkhihsqhu/index.html"}}, "babel_trigger_anchor_action": {"type": "function", "value": "[Function]"}, "getArgsInfo": {"type": "function", "value": "[Function]"}, "getBabelSdk": {"type": "function", "value": "[Function]"}, "getJsToken": {"type": "function", "value": "[Function]"}, "globalBabelSdk": {"type": "object", "value": {"CART_JS": "https://jstatic.3.cn/common/cart/h5_deal_addcart.v1.3.js?t=20230724", "LOTTIE_JS": "https://storage11.360buyimg.com/tower/babelnode/lottie.min.js", "WEIXIN_JS": "https://res.wx.qq.com/open/js/jweixin-1.6.0.js", "closePortal": {}, "configNavigationButton": {}, "getImgSrc": {}, "hooks": {"emit": {}, "off": {}, "on": {}}, "http": {"fetch": {}, "getLoginStatus": {}, "jweixin": {}, "loadCartJs": {}, "loadLottie": {}, "loadScript": {}, "requestIsvToken": {}, "securityFetch": {}}, "jump": {"toAny": {}, "toLogin": {}, "toM": {}, "toSearch": {}, "toShop": {}, "toSku": {}}, "loadAnimation": {}, "loadLottie": {}, "setShareInfo": {}, "showPortal": {}, "tracking": {}}}, "interceptBabelClickVspSku": {"type": "function", "value": "[Function]"}, "onkeydown": {"type": "object", "value": null}, "onkeypress": {"type": "object", "value": null}, "onkeyup": {"type": "object", "value": null}, "ownKeys": {"type": "function", "value": "[Function]"}}, "analysis_timestamp": 1749060571.135814}