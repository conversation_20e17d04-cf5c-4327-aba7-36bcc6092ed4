#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东args参数逆向工程分析器
基于发现的关键信息进行深度逆向分析
"""

import json
import hashlib
import base64
import time
import re
import requests
from urllib.parse import quote, unquote

class JDReverseEngineer:
    def __init__(self):
        # 从调试结果中提取的关键信息
        self.babel_floor_map = {
            "117554838": {"requestId": "10102004744-145974-1749060553563"},
            "117554839": {"requestId": "10102004744-145974-1749060553563"},
            "117554841": {"requestId": "10102004744-145974-1749060553563"},
            "117554842": {"requestId": "10102004744-145974-1749060553563"},
            "117554843": {"requestId": "10102004744-145974-1749060553563"},
            "117554844": {"requestId": "10102004744-145974-1749060553563"},
            "117554845": {"requestId": "10102004744-145974-1749060553563"},
            "117554846": {"requestId": "10102004744-145974-1749060553563"},
            "117973211": {"requestId": "10102004744-145974-1749060553563"},
            "93884336": {"requestId": "10102004744-145974-1749060553563"}
        }
        
        # 活动ID
        self.activity_id = "4P9a2T9osR9JvtzHVaYTPvsecRtg"
        
        # 从localStorage中提取的关键数据
        self.device_fingerprint = "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753-1749060556"
        self.agent_sid = "a0778fe2-7fb2-4f95-93fa-a4982b5c3747"
        
        # 从WQ_dy1_tk_algo中提取的算法信息
        self.tk_algo_data = {
            "z9pdw3dxxpw2jmj6": {
                "73806": {
                    "v": "eyJ0ayI6InRrMDN3YTFiNTFiZTkxOG5SRGN0UE9Ma2xuT3BjSXMxXzlVWk1kUDYwWFFqOGxJTlBsVzdKYWI1TEwzdWN6ajNuZDNVUkZnbVhnZHYwN3VXNzlIM1ljVTRfdmdsIiwiYWxnbyI6ImZ1bmN0aW9uIHRlc3QodGssZnAsdHMsYWksYWxnbyl7dmFyIHJkPSdUb3gxMzBiRXA1c0knO3ZhciBzdHI9XCJcIi5jb25jYXQodGspLmNvbmNhdChmcCkuY29uY2F0KHRzKS5jb25jYXQoYWkpLmNvbmNhdChyZCk7cmV0dXJuIGFsZ28uTUQ1KHN0cik7fSJ9",
                    "e": 86400,
                    "t": 1749060554795
                }
            }
        }
        
    def decode_tk_algo(self):
        """解码tk算法信息"""
        print("=== 解码tk算法信息 ===")
        
        for vk, vk_data in self.tk_algo_data.items():
            print(f"VK: {vk}")
            for key, data in vk_data.items():
                print(f"  Key: {key}")
                try:
                    # Base64解码
                    decoded = base64.b64decode(data['v']).decode('utf-8')
                    algo_data = json.loads(decoded)
                    
                    print(f"  Token: {algo_data.get('tk', 'N/A')}")
                    print(f"  算法: {algo_data.get('algo', 'N/A')}")
                    
                    # 分析算法逻辑
                    if 'algo' in algo_data:
                        self.analyze_algorithm(algo_data['algo'])
                        
                except Exception as e:
                    print(f"  解码失败: {e}")
                    
    def analyze_algorithm(self, algo_code):
        """分析算法代码"""
        print("  算法分析:")
        
        # 提取算法中的关键信息
        if 'MD5' in algo_code:
            print("    - 使用MD5哈希算法")
            
        # 提取随机字符串
        rd_match = re.search(r"rd='([^']+)'", algo_code)
        if rd_match:
            rd_value = rd_match.group(1)
            print(f"    - 随机字符串: {rd_value}")
            
        # 提取拼接逻辑
        if 'concat' in algo_code:
            print("    - 使用字符串拼接")
            concat_pattern = r'concat\(([^)]+)\)'
            matches = re.findall(concat_pattern, algo_code)
            if matches:
                print(f"    - 拼接参数: {matches}")
                
    def generate_args_parameters(self):
        """尝试生成args参数"""
        print("\n=== 尝试生成args参数 ===")
        
        # 基于发现的模块ID生成参数
        module_ids = list(self.babel_floor_map.keys())
        
        results = []
        for module_id in module_ids[:4]:  # 只处理前4个
            print(f"\n处理模块ID: {module_id}")
            
            # 生成基础参数
            timestamp = int(time.time() * 1000)
            
            # 尝试不同的key生成策略
            key_candidates = self.generate_key_candidates(module_id, timestamp)
            role_id_candidates = self.generate_role_id_candidates(module_id, timestamp)
            strengthen_key_candidates = self.generate_strengthen_key_candidates(module_id, timestamp)
            
            # 组合生成args
            for i, (key, role_id, strengthen_key) in enumerate(zip(key_candidates, role_id_candidates, strengthen_key_candidates)):
                args_string = f"key={key}_bingo,roleId={role_id}_bingo,strengthenKey={strengthen_key}_bingo"
                
                result = {
                    'module_id': module_id,
                    'strategy': i + 1,
                    'key': key,
                    'roleId': role_id,
                    'strengthenKey': strengthen_key,
                    'args': args_string,
                    'timestamp': timestamp
                }
                
                results.append(result)
                print(f"  策略{i+1}: key={key[:16]}..., roleId={role_id[:16]}..., strengthenKey={strengthen_key[:16]}...")
                
        return results
        
    def generate_key_candidates(self, module_id, timestamp):
        """生成key候选值"""
        candidates = []
        
        # 策略1: MD5(module_id + timestamp)
        input1 = f"{module_id}{timestamp}"
        key1 = hashlib.md5(input1.encode()).hexdigest().upper()
        candidates.append(key1)
        
        # 策略2: MD5(activity_id + module_id)
        input2 = f"{self.activity_id}{module_id}"
        key2 = hashlib.md5(input2.encode()).hexdigest().upper()
        candidates.append(key2)
        
        # 策略3: MD5(device_fingerprint + module_id)
        input3 = f"{self.device_fingerprint}{module_id}"
        key3 = hashlib.md5(input3.encode()).hexdigest().upper()
        candidates.append(key3)
        
        # 策略4: 基于tk算法的生成
        try:
            # 模拟tk算法
            tk = "tk03wa1b51be918nRDctPOLklnOpcIs1_9UZMdP60XQj8lINPlW7Jab5LL3uczj3nd3URFgmXgdv07uW79H3YcU4_vgl"
            fp = self.device_fingerprint
            ts = str(timestamp)
            ai = self.activity_id
            rd = "Tox130bEp5sI"
            
            input4 = f"{tk}{fp}{ts}{ai}{rd}"
            key4 = hashlib.md5(input4.encode()).hexdigest().upper()
            candidates.append(key4)
        except:
            candidates.append(key1)  # 回退到策略1
            
        return candidates
        
    def generate_role_id_candidates(self, module_id, timestamp):
        """生成roleId候选值"""
        candidates = []
        
        # roleId通常比key长，可能是多个哈希的组合
        base_inputs = [
            f"{self.activity_id}{module_id}{timestamp}",
            f"{self.device_fingerprint}{module_id}{self.agent_sid}",
            f"{module_id}{timestamp}{self.activity_id}{self.device_fingerprint}",
        ]
        
        for input_str in base_inputs:
            # 生成多个哈希并拼接
            hash1 = hashlib.md5(input_str.encode()).hexdigest().upper()
            hash2 = hashlib.md5((input_str + "salt1").encode()).hexdigest().upper()
            hash3 = hashlib.md5((input_str + "salt2").encode()).hexdigest().upper()
            
            # 拼接成更长的roleId
            role_id = hash1 + hash2 + hash3
            candidates.append(role_id)
            
        return candidates
        
    def generate_strengthen_key_candidates(self, module_id, timestamp):
        """生成strengthenKey候选值"""
        candidates = []
        
        # strengthenKey通常是64位，可能是SHA256或双MD5
        base_inputs = [
            f"{module_id}{self.activity_id}",
            f"{timestamp}{module_id}",
            f"{self.device_fingerprint}{module_id}",
        ]
        
        for input_str in base_inputs:
            # 策略1: SHA256
            try:
                import hashlib
                strengthen1 = hashlib.sha256(input_str.encode()).hexdigest().upper()
                candidates.append(strengthen1)
            except:
                # 策略2: 双MD5
                md5_1 = hashlib.md5(input_str.encode()).hexdigest()
                md5_2 = hashlib.md5(md5_1.encode()).hexdigest().upper()
                candidates.append(md5_2)
                
        return candidates
        
    def test_api_call(self, args_string):
        """测试API调用"""
        print(f"\n=== 测试API调用 ===")
        print(f"Args: {args_string[:100]}...")
        
        # 构造API请求
        body = {
            "activityId": self.activity_id,
            "scene": "1",
            "args": args_string
        }
        
        body_json = json.dumps(body, separators=(',', ':'))
        body_encoded = quote(body_json)
        
        url = f"https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={body_encoded}"
        
        headers = {
            'User-Agent': 'jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D',
            'Referer': f'https://pro.m.jd.com/mall/active/{self.activity_id}/index.html',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text[:200]}...")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return result
                except:
                    return {"raw_response": response.text}
            else:
                return {"error": f"HTTP {response.status_code}"}
                
        except Exception as e:
            print(f"请求失败: {e}")
            return {"error": str(e)}
            
    def analyze_existing_args(self):
        """分析已知的args参数样本"""
        print("\n=== 分析已知args参数 ===")
        
        # 已知的args样本
        known_args = "key=12E6D29F443D32E70D2EB5DD334974C45E86D167CCD491690CCA36607B3800EB7AF55DFFFA4F5E72F24962C9024FFD54_bingo,roleId=37172FD62191D374322DAB34E836D0B82DB976D8AABD2D906EA5D7E2AE59B6A85D0F545C63D2E0351C75B50F1E52079D3D8B423E551CAF814CAAD9F055500E32B51C5B35E665D9AF40B6A7B95829D95418CA87BE80B410BE8EEA434817C28C7AC517AD866F97EEDD7416A7151EC4F801C0CC15C9B33100F589A6CC8CF9A3EAFAFE3534074B4794E945C0EAE9B6D8DC7CE4344D05F8A6A5C1B3933D36C60496F4_bingo,strengthenKey=19F512DCD8EE34ABE9C4FB4A92C2F42A440211B8CD07D2DE99492B61ACCC883B_bingo"
        
        # 解析参数
        parts = known_args.split(',')
        params = {}
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                if value.endswith('_bingo'):
                    value = value[:-6]
                params[key] = value
                
        print("已知参数分析:")
        for key, value in params.items():
            print(f"  {key}: {value}")
            print(f"    长度: {len(value)}")
            print(f"    是否十六进制: {all(c in '0123456789ABCDEFabcdef' for c in value)}")
            
            # 尝试分析可能的生成规律
            if len(value) == 64:
                print(f"    可能是SHA256或双MD5")
            elif len(value) == 32:
                print(f"    可能是MD5")
            else:
                print(f"    自定义长度")
                
        return params
        
    def run_reverse_analysis(self):
        """运行逆向分析"""
        print("=== 京东args参数逆向分析 ===")
        
        # 1. 解码算法信息
        self.decode_tk_algo()
        
        # 2. 分析已知参数
        known_params = self.analyze_existing_args()
        
        # 3. 生成候选参数
        candidates = self.generate_args_parameters()
        
        # 4. 测试生成的参数
        print(f"\n=== 测试生成的参数 ===")
        test_results = []
        
        for i, candidate in enumerate(candidates[:3]):  # 只测试前3个
            print(f"\n测试候选参数 {i+1}:")
            result = self.test_api_call(candidate['args'])
            test_results.append({
                'candidate': candidate,
                'result': result
            })
            
        # 5. 保存分析结果
        analysis_result = {
            'known_params': known_params,
            'generated_candidates': candidates,
            'test_results': test_results,
            'babel_floor_map': self.babel_floor_map,
            'device_info': {
                'fingerprint': self.device_fingerprint,
                'agent_sid': self.agent_sid
            }
        }
        
        with open('jd_reverse_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(analysis_result, f, indent=2, ensure_ascii=False)
            
        print(f"\n逆向分析结果已保存到 jd_reverse_analysis.json")
        
        return analysis_result

def main():
    engineer = JDReverseEngineer()
    result = engineer.run_reverse_analysis()
    
    print("\n=== 逆向分析总结 ===")
    print(f"生成了 {len(result['generated_candidates'])} 个候选参数")
    print(f"测试了 {len(result['test_results'])} 个参数")
    
    # 显示测试结果摘要
    for i, test in enumerate(result['test_results']):
        print(f"\n候选参数 {i+1} 测试结果:")
        if 'error' in test['result']:
            print(f"  错误: {test['result']['error']}")
        else:
            print(f"  成功获得响应")

if __name__ == "__main__":
    main()
