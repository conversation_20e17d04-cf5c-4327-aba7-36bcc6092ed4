#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东动态args参数生成器
解决登录后code:-1的问题
"""

import hashlib
import time
import json
import requests
import random
import string
from urllib.parse import quote

class JDDynamicArgsGenerator:
    def __init__(self, activity_id="4P9a2T9osR9JvtzHVaYTPvsecRtg"):
        self.activity_id = activity_id
        
        # 4个商品的模块ID
        self.module_ids = [
            "117554838",  # 商品1
            "117554839",  # 商品2  
            "117554841",  # 商品3
            "117554842"   # 商品4
        ]
        
    def generate_device_info(self):
        """生成动态设备信息"""
        # 生成随机设备指纹
        device_id = ''.join(random.choices(string.hexdigits.lower(), k=8)) + '-' + \
                   ''.join(random.choices(string.hexdigits.lower(), k=4)) + '-' + \
                   ''.join(random.choices(string.hexdigits.lower(), k=4)) + '-' + \
                   ''.join(random.choices(string.hexdigits.lower(), k=4)) + '-' + \
                   ''.join(random.choices(string.hexdigits.lower(), k=12))
        
        timestamp = str(int(time.time()))
        return f"{device_id}-{timestamp}"
        
    def generate_enhanced_key(self, module_id, timestamp, user_info=""):
        """生成增强版key参数"""
        # 策略1: 基于时间戳和模块ID
        base1 = f"{module_id}{timestamp}{self.activity_id}"
        key1 = hashlib.md5(base1.encode()).hexdigest().upper()
        
        # 策略2: 添加用户信息
        if user_info:
            base2 = f"{user_info}{module_id}{timestamp}"
            key2 = hashlib.md5(base2.encode()).hexdigest().upper()
        else:
            key2 = key1
            
        # 策略3: 双重哈希
        base3 = f"{key1}{key2}"
        key3 = hashlib.md5(base3.encode()).hexdigest().upper()
        
        # 返回最复杂的key
        return key3
        
    def generate_enhanced_role_id(self, module_id, timestamp, device_info, user_info=""):
        """生成增强版roleId参数"""
        # 基础输入
        base_inputs = [
            f"{self.activity_id}{module_id}{timestamp}",
            f"{device_info}{module_id}{self.activity_id}",
            f"{user_info}{module_id}{timestamp}{device_info}",
            f"{module_id}{self.activity_id}{timestamp}{device_info}{user_info}"
        ]
        
        # 生成多个哈希
        hashes = []
        for base_input in base_inputs:
            hash_val = hashlib.md5(base_input.encode()).hexdigest().upper()
            hashes.append(hash_val)
            
        # 组合哈希，确保长度合适
        combined = ''.join(hashes)
        
        # 根据已知样本调整长度（约320字符）
        target_length = 320
        if len(combined) < target_length:
            # 如果太短，重复填充
            repeat_count = (target_length // len(combined)) + 1
            combined = (combined * repeat_count)[:target_length]
        else:
            # 如果太长，截取
            combined = combined[:target_length]
            
        return combined
        
    def generate_enhanced_strengthen_key(self, module_id, timestamp, device_info):
        """生成增强版strengthenKey参数"""
        # 多种策略组合
        inputs = [
            f"{module_id}{self.activity_id}{timestamp}",
            f"{device_info}{module_id}",
            f"{timestamp}{self.activity_id}{device_info}",
            f"{module_id}{device_info}{timestamp}{self.activity_id}"
        ]
        
        # 使用SHA256确保64位长度
        for input_str in inputs:
            strengthen_key = hashlib.sha256(input_str.encode()).hexdigest().upper()
            # 验证长度
            if len(strengthen_key) == 64:
                return strengthen_key
                
        # 默认策略
        default_input = f"{module_id}{self.activity_id}"
        return hashlib.sha256(default_input.encode()).hexdigest().upper()
        
    def generate_precise_args(self, product_index, user_info="", strategy="enhanced"):
        """生成精确的args参数"""
        if product_index < 0 or product_index >= len(self.module_ids):
            raise ValueError(f"商品索引必须在0-{len(self.module_ids)-1}之间")
            
        module_id = self.module_ids[product_index]
        timestamp = int(time.time() * 1000)
        device_info = self.generate_device_info()
        
        if strategy == "enhanced":
            # 增强策略
            key = self.generate_enhanced_key(module_id, timestamp, user_info)
            role_id = self.generate_enhanced_role_id(module_id, timestamp, device_info, user_info)
            strengthen_key = self.generate_enhanced_strengthen_key(module_id, timestamp, device_info)
        elif strategy == "precise":
            # 精确策略 - 模拟真实算法
            # 基于逆向分析的tk算法
            tk = "tk03wa1b51be918nRDctPOLklnOpcIs1_9UZMdP60XQj8lINPlW7Jab5LL3uczj3nd3URFgmXgdv07uW79H3YcU4_vgl"
            fp = device_info
            ts = str(timestamp)
            ai = self.activity_id
            rd = "Tox130bEp5sI"
            
            # 模拟tk算法: MD5(tk + fp + ts + ai + rd)
            tk_input = f"{tk}{fp}{ts}{ai}{rd}"
            key = hashlib.md5(tk_input.encode()).hexdigest().upper()
            
            # roleId使用更复杂的组合
            role_base = f"{key}{module_id}{timestamp}{device_info}"
            role_hash1 = hashlib.md5(role_base.encode()).hexdigest().upper()
            role_hash2 = hashlib.md5((role_base + module_id).encode()).hexdigest().upper()
            role_hash3 = hashlib.md5((role_base + str(timestamp)).encode()).hexdigest().upper()
            role_hash4 = hashlib.md5((role_base + device_info).encode()).hexdigest().upper()
            role_hash5 = hashlib.md5((role_base + self.activity_id).encode()).hexdigest().upper()
            
            # 拼接到320字符
            role_id = (role_hash1 + role_hash2 + role_hash3 + role_hash4 + role_hash5)[:320]
            
            # strengthenKey使用SHA256
            strengthen_input = f"{key}{module_id}{timestamp}"
            strengthen_key = hashlib.sha256(strengthen_input.encode()).hexdigest().upper()
            
        else:
            # 简单策略
            key = hashlib.md5(f"{module_id}{timestamp}".encode()).hexdigest().upper()
            role_id = hashlib.md5(f"{self.activity_id}{module_id}{timestamp}".encode()).hexdigest().upper() * 10
            role_id = role_id[:320]
            strengthen_key = hashlib.sha256(f"{module_id}{self.activity_id}".encode()).hexdigest().upper()
        
        # 构造args字符串
        args_string = f"key={key}_bingo,roleId={role_id}_bingo,strengthenKey={strengthen_key}_bingo"
        
        return {
            'product_index': product_index,
            'module_id': module_id,
            'strategy': strategy,
            'timestamp': timestamp,
            'device_info': device_info,
            'key': key,
            'roleId': role_id,
            'strengthenKey': strengthen_key,
            'args': args_string
        }
        
    def test_args_with_login(self, args_string, cookies=""):
        """使用登录状态测试args参数"""
        body = {
            "activityId": self.activity_id,
            "scene": "1",
            "args": args_string
        }
        
        body_json = json.dumps(body, separators=(',', ':'))
        body_encoded = quote(body_json)
        
        url = f"https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={body_encoded}"
        
        headers = {
            'User-Agent': 'jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D',
            'Referer': f'https://pro.m.jd.com/mall/active/{self.activity_id}/index.html',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        
        if cookies:
            headers['Cookie'] = cookies
            
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response': result,
                        'url': url
                    }
                except:
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response': response.text,
                        'url': url
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"HTTP {response.status_code}",
                    'url': url
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e),
                'url': url
            }
            
    def generate_multiple_strategies(self, product_index, user_info=""):
        """为单个商品生成多种策略的参数"""
        strategies = ["simple", "enhanced", "precise"]
        results = []
        
        for strategy in strategies:
            try:
                args_data = self.generate_precise_args(product_index, user_info, strategy)
                results.append(args_data)
            except Exception as e:
                print(f"策略 {strategy} 生成失败: {e}")
                
        return results
        
    def generate_all_products_multiple_strategies(self, user_info=""):
        """为所有商品生成多种策略的参数"""
        print("=== 京东动态args参数生成器 ===")
        print(f"活动ID: {self.activity_id}")
        print(f"用户信息: {user_info if user_info else '未提供'}")
        
        all_results = []
        
        for product_index in range(len(self.module_ids)):
            print(f"\n--- 商品 {product_index + 1} (模块ID: {self.module_ids[product_index]}) ---")
            
            strategies_results = self.generate_multiple_strategies(product_index, user_info)
            
            for i, result in enumerate(strategies_results):
                print(f"\n策略 {result['strategy']}:")
                print(f"  Key: {result['key'][:32]}...")
                print(f"  RoleId: {result['roleId'][:32]}...")
                print(f"  StrengthenKey: {result['strengthenKey'][:32]}...")
                print(f"  完整Args: {result['args'][:100]}...")
                
            all_results.append({
                'product_index': product_index,
                'module_id': self.module_ids[product_index],
                'strategies': strategies_results
            })
            
        # 保存结果
        output = {
            'activity_id': self.activity_id,
            'user_info': user_info,
            'generation_time': time.time(),
            'products': all_results
        }
        
        with open('jd_dynamic_args_results.json', 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
            
        print(f"\n=== 生成完成 ===")
        print("结果已保存到 jd_dynamic_args_results.json")
        
        return all_results

def main():
    # 创建动态生成器
    generator = JDDynamicArgsGenerator()
    
    # 可以提供用户信息来生成更精确的参数
    user_info = input("请输入用户信息(可选，直接回车跳过): ").strip()
    
    # 生成所有商品的多种策略参数
    results = generator.generate_all_products_multiple_strategies(user_info)
    
    print(f"\n=== 推荐测试顺序 ===")
    print("建议按以下顺序测试参数:")
    
    for i, product in enumerate(results):
        print(f"\n商品 {i+1}:")
        for strategy in product['strategies']:
            print(f"  {strategy['strategy']} 策略: {strategy['args'][:80]}...")

if __name__ == "__main__":
    main()
