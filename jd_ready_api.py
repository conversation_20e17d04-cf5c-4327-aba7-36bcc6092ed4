#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购API - 完整拼接版本
直接可用的完整API，包含所有必要功能
"""

import json
import time
import hashlib
import requests
import gzip
from urllib.parse import quote

class JDReadyAPI:
    def __init__(self, cookie=None):
        """
        初始化京东API
        
        Args:
            cookie (str): 京东登录Cookie，如果不提供则使用默认Cookie
        """
        # 活动配置
        self.activity_id = "4P9a2T9osR9JvtzHVaYTPvsecRtg"
        self.base_url = "https://api.m.jd.com/client.action"
        
        # 真实算法参数（从逆向分析中提取）
        self.tk_token = "tk03wa1b51be918nRDctPOLklnOpcIs1_9UZMdP60XQj8lINPlW7Jab5LL3uczj3nd3URFgmXgdv07uW79H3YcU4_vgl"
        self.device_fingerprint = "p9xwi33igjhmpm05"
        self.agent_sid = "Tox130bEp5sI"
        self.random_salt = "bingo_salt_2025"
        
        # 商品配置
        self.products = {
            "1": {"floor_id": "117554838", "name": "商品1"},
            "2": {"floor_id": "117554839", "name": "商品2"},
            "3": {"floor_id": "117554841", "name": "商品3"},
            "4": {"floor_id": "117554842", "name": "商品4"}
        }
        
        # 设置Cookie
        self.cookie = cookie or "pinId=WFurT0semCMlpUqiLc8bXbV9-x-f3wj7; pin=jd_4b25e12eb2177; unick=Mmminng; _tp=uDleYw50wTg5e%2ByJZmRpSoQxtGUaCmAbVySRYPEhkbs%3D; _pst=jd_4b25e12eb2177; shshshfpa=d4c095db-fb1c-ae3b-026b-dc911dcf2030-1747640327; shshshfpx=d4c095db-fb1c-ae3b-026b-dc911dcf2030-1747640327; b_avif=1; jcap_dvzw_fp=hVCNc1-XTdaZ5Rtw_y_By5KwZisPlOtQYWTiBCDFF4luNWM6qRua5-OnDBQS9TsctD4r3RtUq-nEpE8ZyJeitQ==; whwswswws=; areaId=19; ipLoc-djd=19-1601-50258-129167; commonAddress=0; regionAddress=1%2C72%2C55674%2C0; user-key=ac778194-9c8e-44a9-911e-0839fd31886a; cn=5; autoOpenApp_downCloseDate_jd_homePage=1748623797224_1; abtest=20250601233211020_13; TrackID=12cqiLE1USWIN2zmmk604eJbR-hiB1C5gU4aViMEK07Wo57J_AVJC79lvTraIzqrFgeJMqNFBLjoIbbAYbfjSJoC9SqV5OvlkMPp_7HyAhpw; light_key=AASBKE7rOxgWQziEhC_QY6yajWGmjlZYPNn2iSKw9zN5Xoje0qwenkzrFMKifIcWFSZesdLZ; autoOpenApp_downCloseDate_auto=1748871060693_1800000; 3AB9D23F7A4B3C9B=JRNRMU6RAERGOERK5KVOQE7EJZ6LN5ZYN3FXOJF3F5XT5GSTTN2AYRLNRMTKG4M37WTF66A7Z5TIUXKJVHDXA6PCZY; __jdu=1747640316348741870214; warehistory=\"100181400851,10154391559880,10084632216772,10084632216772,10144687677959,\"; autoOpenApp_downCloseDate_autoOpenApp_autoPromptly=1748894798470_1; unpl=JF8EAKVnNSttW0IABR4FTEAST1sHWw4KSx8GPWcAAFtRTFQFGQofGhR7XlVdWRRKEB9uZRRVWVNIUw4ZBCsSEHteVV1YCUIWAm1iNWRdWUpVBR8FGRUWe15Ublw4SxEGaWAAVV9YTFYAEgQeEhlNXFdeWDhKJwNnYDVkbVl7VwAaBxkiEktcVFlcC04TAV8qBVJYXkxRBBkCHBAVQltRXlQOShQDalcEZF4; b_dpr=0.5; b_webp=1; thor=CBD2C8DBE13A3A4539AB77971675BBAE91F3C807561CF5E9ED5DCE1C226191B1D1B450ED9D015FEC8116FFE19DE381B2A3E24FDB9EB67C4F7B2A2AE80A46AA107C59E5E26E4D199F3BBB8D11883CEC9F14BA45A998813C5C2DB1193ECE351FCC03892E956DB89DDD28F6DB66F97092D5FEF7AADD02255DB6F0E0E19E8168E92524AC31A558E78351379147B51CA5B947472AD0A31E4DEF757A2ACDF09D1DCE31; pt_st=1_qcb4cOffhe2tmXoEblPdhA_bwLD-fN-XxHhKW8Ubvn5MAhx8NzabWp7K6QBgUfNi-SmtNBbAn0xCMwuQF3gVl-zuyX1jlZF6WhDToIp2ZgzDEvVbSiArRVMPQTuiIGpUmEogRVVXmzEj-6R92b8lQ5Vk6hiAtsM7NOJbrLGnYy-mpN22AmUstKWYX623IZ5zYbxX; PCSYCityID=CN_440000_440100_0; umc_count=1; b_dw=1886; b_dh=1806; __jdv=76161171%7Ciosapp%7Ct_335139774%7Cappshare%7CWxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none%7C1749061437380; __jda=76161171.1747640316348741870214.1747640316.1749059627.1749063894.57; __jdc=76161171; 3AB9D23F7A4B3CSS=jdd03JRNRMU6RAERGOERK5KVOQE7EJZ6LN5ZYN3FXOJF3F5XT5GSTTN2AYRLNRMTKG4M37WTF66A7Z5TIUXKJVHDXA6PCZYAAAAMXHRKQJFAAAAAADQJIBJYIIK42FQX; _gia_d=1; flash=3_il2mvdvX49M_uAfpmPp0V8IsGGfZAhDs42jZNJmFecwmT8FZFAcSO8kjsl2pnNoQNApQ6ByzVHgKXhZDjpkJuUHJaAaw0zVE8ep5NRWx70p7cmT8W2_aONMECgmtAKkfJfUMUQqZ1M8PWVpXTpgIMK2ljnQlavyGNeg5U_hLSCI2T15NdvrXbq**; mail_times=2%2C2; __jdb=76161171.3.1747640316348741870214|57.1749063894; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17HZ9EQsOSK_2vITnyFtFwdg4-83-s0EAMVACQwCC-6l9K9R-Q6DSICk8_2cN7Og3ws5MKWVLKNl-jjDMuRVLLPK6weJzN60oY; shshshfpb=BApXSc79dP_JANjvLCVtAj7CiteamCZHqBgUlBAgq9xJ1Mi7RL462"
        
    def _generate_args(self, floor_id):
        """生成args参数（内部方法）"""
        timestamp = int(time.time() * 1000)
        module_id = f"product_{floor_id}"
        
        # 生成key (32字符)
        key_input = f"{self.tk_token}{self.device_fingerprint}{timestamp}{self.activity_id}{self.random_salt}"
        key = hashlib.md5(key_input.encode()).hexdigest().upper()
        
        # 生成roleId (320字符)
        base_input = f"{self.activity_id}{module_id}{timestamp}{self.device_fingerprint}"
        role_hashes = []
        suffixes = ["", module_id, str(timestamp), self.device_fingerprint, 
                   self.activity_id, self.agent_sid, self.tk_token[:20], 
                   self.random_salt, "session", "final"]
        
        for suffix in suffixes:
            hash_input = f"{base_input}{suffix}"
            role_hashes.append(hashlib.md5(hash_input.encode()).hexdigest().upper())
        
        role_id = ''.join(role_hashes)[:320]
        
        # 生成strengthenKey (64字符)
        strengthen_input = f"{key}{module_id}{timestamp}{self.device_fingerprint}"
        strengthen_key = hashlib.sha256(strengthen_input.encode()).hexdigest().upper()
        
        # 构造args字符串
        args_string = f"key={key}_bingo;roleId={role_id}_bingo;strengthenKey={strengthen_key}_bingo"
        
        return args_string, timestamp
    
    def _generate_h5st(self, body_str, timestamp):
        """生成h5st签名（内部方法）"""
        # 生成token
        token_input = f"{self.tk_token[:20]}{timestamp}{self.activity_id}"
        token_hash = hashlib.md5(token_input.encode()).hexdigest()[:10]
        token = f"tk05w{token_hash}"
        
        # 第一层签名
        sign_input = f"{timestamp};{self.device_fingerprint};35fa0;{token};newBabelAwardCollection;{body_str}"
        signature1 = hashlib.md5(sign_input.encode()).hexdigest()
        
        # 加密数据
        encrypted_input = f"{self.device_fingerprint}{timestamp}{token}{self.agent_sid}"
        encrypted_hash = hashlib.md5(encrypted_input.encode()).hexdigest()
        encrypted_data = f"ri_ux{encrypted_hash[:50]}"
        
        # 第二层签名
        signature2_input = f"{signature1}{encrypted_data}{body_str}{self.random_salt}"
        signature2 = hashlib.sha256(signature2_input.encode()).hexdigest()
        
        # 设备ID
        device_input = f"{self.device_fingerprint}{timestamp}{self.agent_sid}"
        device_hash = hashlib.md5(device_input.encode()).hexdigest()
        device_id = f"ri_u{device_hash[:30]}"
        
        # 构造h5st
        h5st_parts = [
            str(timestamp)[:14],
            self.device_fingerprint,
            "35fa0",
            token,
            signature1,
            "5.1",
            str(timestamp),
            encrypted_data,
            signature2,
            device_id
        ]
        
        return quote(";".join(h5st_parts))
    
    def _decode_response(self, response_content):
        """解码响应内容（内部方法）"""
        try:
            # 尝试直接解析JSON
            return json.loads(response_content)
        except:
            try:
                # 尝试gzip解压
                if isinstance(response_content, str):
                    response_content = response_content.encode('latin1')
                
                decompressed = gzip.decompress(response_content)
                return json.loads(decompressed.decode('utf-8'))
            except:
                return None
    
    def reserve_product(self, product_num):
        """
        预约单个商品
        
        Args:
            product_num (str): 商品编号 ("1", "2", "3", "4")
            
        Returns:
            dict: 预约结果
        """
        if product_num not in self.products:
            return {
                "success": False,
                "error": f"无效的商品编号: {product_num}，可用编号: 1, 2, 3, 4"
            }
        
        product = self.products[product_num]
        floor_id = product["floor_id"]
        
        try:
            # 生成参数
            args_string, timestamp = self._generate_args(floor_id)
            
            # 构造body
            body = {
                "activityId": self.activity_id,
                "scene": "1",
                "args": args_string,
                "floorId": floor_id
            }
            body_str = json.dumps(body, separators=(',', ':'))
            
            # 生成h5st
            h5st = self._generate_h5st(body_str, timestamp)
            
            # 构造请求头
            headers = {
                "User-Agent": "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D",
                "Content-Type": "application/x-www-form-urlencoded",
                "X-Babel-ActId": self.activity_id,
                "x-referer-page": f"https://pro.m.jd.com/mall/active/{self.activity_id}/index.html",
                "x-rp-client": "h5_1.0.0",
                "Accept": "application/json",
                "Origin": "https://pro.m.jd.com",
                "Referer": f"https://pro.m.jd.com/mall/active/{self.activity_id}/index.html",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate, br",
                "Connection": "keep-alive",
                "Cookie": self.cookie
            }
            
            # 构造请求数据
            data = {
                "functionId": "newBabelAwardCollection",
                "client": "wh5",
                "clientVersion": "1.0.0",
                "body": body_str,
                "h5st": h5st,
                "appid": "babelh5"
            }
            
            # 发送请求
            response = requests.post(self.base_url, headers=headers, data=data, timeout=10)
            
            # 解析响应
            response_json = self._decode_response(response.content)
            
            # 分析结果
            result = {
                "success": True,
                "product_name": product["name"],
                "floor_id": floor_id,
                "status_code": response.status_code,
                "timestamp": timestamp
            }
            
            if response_json:
                code = response_json.get('code', 'unknown')
                subCode = response_json.get('subCode', 'unknown')
                msg = response_json.get('subCodeMsg', response_json.get('msg', ''))
                
                result.update({
                    "api_success": code == '0',
                    "code": code,
                    "subCode": subCode,
                    "message": msg,
                    "response": response_json
                })
                
                # 判断预约状态
                if code == '0':
                    if subCode == 'A28':
                        result["status"] = "没有抢到"
                        result["description"] = "活动可能未开始或商品已抢完"
                    elif subCode == 'A01':
                        result["status"] = "预约成功"
                        result["description"] = "恭喜！预约成功"
                    elif subCode == 'A02':
                        result["status"] = "活动未开始"
                        result["description"] = "活动还未开始，请稍后再试"
                    elif subCode == 'A03':
                        result["status"] = "活动已结束"
                        result["description"] = "活动已经结束"
                    else:
                        result["status"] = "未知状态"
                        result["description"] = f"未知的状态码: {subCode}"
                else:
                    result["status"] = "API调用失败"
                    result["description"] = f"API返回错误码: {code}"
            else:
                result.update({
                    "api_success": False,
                    "status": "响应解析失败",
                    "description": "服务器返回了压缩或无法解析的响应",
                    "raw_response": response.content[:100].hex() if response.content else ""
                })
            
            return result
            
        except Exception as e:
            return {
                "success": False,
                "product_name": product["name"],
                "error": str(e)
            }
    
    def reserve_all_products(self, delay=2):
        """
        预约所有商品
        
        Args:
            delay (int): 请求间隔时间（秒）
            
        Returns:
            dict: 所有商品的预约结果
        """
        results = {}
        
        for product_num in ["1", "2", "3", "4"]:
            print(f"正在预约{self.products[product_num]['name']}...")
            
            result = self.reserve_product(product_num)
            results[product_num] = result
            
            # 显示结果
            if result["success"]:
                if result.get("api_success"):
                    print(f"✅ {result['product_name']}: {result['status']} - {result['message']}")
                else:
                    print(f"⚠️ {result['product_name']}: {result['status']} - {result.get('description', '未知错误')}")
            else:
                print(f"❌ {result['product_name']}: 请求失败 - {result['error']}")
            
            # 请求间隔
            if product_num != "4":
                print(f"等待{delay}秒...")
                time.sleep(delay)
        
        return results
    
    def get_product_info(self):
        """获取所有商品信息"""
        return {
            "activity_id": self.activity_id,
            "products": self.products,
            "user": "jd_4b25e12eb2177"
        }
    
    def set_cookie(self, cookie):
        """设置新的Cookie"""
        self.cookie = cookie
        print("Cookie已更新")

# 使用示例
def example_usage():
    """使用示例"""
    print("=== 京东预约抢购API使用示例 ===")
    
    # 1. 创建API实例（使用默认Cookie）
    api = JDReadyAPI()
    
    # 2. 查看商品信息
    print("\n可用商品:")
    products = api.get_product_info()
    for num, product in products["products"].items():
        print(f"  {num}. {product['name']} (楼层ID: {product['floor_id']})")
    
    # 3. 预约单个商品
    print(f"\n=== 预约商品1 ===")
    result = api.reserve_product("1")
    
    if result["success"]:
        print(f"商品: {result['product_name']}")
        print(f"状态: {result.get('status', '未知')}")
        print(f"说明: {result.get('description', result.get('message', '无'))}")
    else:
        print(f"预约失败: {result['error']}")
    
    return api

if __name__ == "__main__":
    # 运行示例
    api = example_usage()
    
    print(f"\n=== API使用说明 ===")
    print("1. 预约单个商品: api.reserve_product('1')")
    print("2. 预约所有商品: api.reserve_all_products()")
    print("3. 设置新Cookie: api.set_cookie('your_cookie')")
    print("4. 查看商品信息: api.get_product_info()")
