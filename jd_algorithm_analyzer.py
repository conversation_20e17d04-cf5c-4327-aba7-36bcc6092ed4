#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东参数生成算法分析工具
专门分析加载的JavaScript文件中的参数生成算法
"""

import json
import time
import re
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import urljoin, urlparse
import hashlib

class JDAlgorithmAnalyzer:
    def __init__(self):
        self.driver = None
        self.js_files = []
        self.algorithms = []
        
        # 京东APP的User-Agent
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 使用京东APP的User-Agent
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用性能日志来捕获网络请求
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # 使用ChromeDriverManager自动下载和管理驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def extract_js_files(self, base_url):
        """提取页面中加载的所有JavaScript文件"""
        print("正在提取JavaScript文件...")
        
        # 从页面源码中提取script标签
        script_elements = self.driver.find_elements(By.TAG_NAME, "script")
        js_urls = []
        
        for script in script_elements:
            src = script.get_attribute('src')
            if src:
                # 转换为绝对URL
                absolute_url = urljoin(base_url, src)
                js_urls.append(absolute_url)
        
        # 从网络日志中提取JavaScript文件
        logs = self.driver.get_log('performance')
        for log in logs:
            try:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.responseReceived':
                    response = message['message']['params']['response']
                    url = response['url']
                    content_type = response.get('headers', {}).get('content-type', '')
                    
                    if 'javascript' in content_type.lower() or url.endswith('.js'):
                        js_urls.append(url)
            except:
                continue
        
        # 去重
        js_urls = list(set(js_urls))
        print(f"找到 {len(js_urls)} 个JavaScript文件")
        
        return js_urls
        
    def download_and_analyze_js(self, js_urls):
        """下载并分析JavaScript文件"""
        print("正在下载和分析JavaScript文件...")
        
        headers = {
            'User-Agent': self.jd_ua,
            'Referer': 'https://pro.m.jd.com/',
            'Accept': 'application/javascript, */*'
        }
        
        for i, url in enumerate(js_urls, 1):
            try:
                print(f"分析文件 {i}/{len(js_urls)}: {url}")
                
                response = requests.get(url, headers=headers, timeout=10)
                if response.status_code == 200:
                    js_content = response.text
                    
                    # 分析JavaScript内容
                    analysis = self.analyze_js_content(js_content, url)
                    if analysis['has_algorithm']:
                        self.js_files.append({
                            'url': url,
                            'content': js_content,
                            'analysis': analysis,
                            'size': len(js_content)
                        })
                        
            except Exception as e:
                print(f"下载文件失败 {url}: {e}")
                continue
                
    def analyze_js_content(self, content, url):
        """分析JavaScript内容，寻找参数生成算法"""
        analysis = {
            'has_algorithm': False,
            'algorithm_patterns': [],
            'crypto_functions': [],
            'key_generation': [],
            'role_generation': [],
            'strengthen_generation': [],
            'hash_functions': [],
            'encryption_methods': [],
            'suspicious_functions': []
        }
        
        # 查找加密相关的模式
        crypto_patterns = [
            r'(md5|sha1|sha256|sha512|hmac|aes|des|rsa)\s*\(',
            r'CryptoJS\.[^(]+\(',
            r'crypto\.[^(]+\(',
            r'encrypt\s*\(',
            r'decrypt\s*\(',
            r'hash\s*\(',
            r'digest\s*\(',
        ]
        
        for pattern in crypto_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                analysis['crypto_functions'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找key生成相关的模式
        key_patterns = [
            r'function\s+\w*[Kk]ey\w*\s*\([^)]*\)\s*\{[^}]{50,}',
            r'[Kk]ey\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}',
            r'generateKey\s*\([^)]*\)',
            r'createKey\s*\([^)]*\)',
            r'buildKey\s*\([^)]*\)',
        ]
        
        for pattern in key_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['key_generation'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找roleId生成相关的模式
        role_patterns = [
            r'function\s+\w*[Rr]ole\w*\s*\([^)]*\)\s*\{[^}]{50,}',
            r'[Rr]oleId\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}',
            r'generateRole\s*\([^)]*\)',
            r'createRole\s*\([^)]*\)',
            r'buildRole\s*\([^)]*\)',
        ]
        
        for pattern in role_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['role_generation'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找strengthenKey生成相关的模式
        strengthen_patterns = [
            r'function\s+\w*[Ss]trengthen\w*\s*\([^)]*\)\s*\{[^}]{50,}',
            r'[Ss]trengthenKey\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}',
            r'generateStrengthen\s*\([^)]*\)',
            r'createStrengthen\s*\([^)]*\)',
            r'buildStrengthen\s*\([^)]*\)',
        ]
        
        for pattern in strengthen_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['strengthen_generation'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找哈希函数
        hash_patterns = [
            r'function\s+\w*[Hh]ash\w*\s*\([^)]*\)\s*\{[^}]{20,}',
            r'[Hh]ash\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{20,}',
            r'\.hash\s*\(',
            r'hashCode\s*\(',
        ]
        
        for pattern in hash_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['hash_functions'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找加密方法
        encryption_patterns = [
            r'function\s+\w*[Ee]ncrypt\w*\s*\([^)]*\)\s*\{[^}]{20,}',
            r'[Ee]ncrypt\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{20,}',
            r'function\s+\w*[Cc]ipher\w*\s*\([^)]*\)\s*\{[^}]{20,}',
            r'[Cc]ipher\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{20,}',
        ]
        
        for pattern in encryption_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['encryption_methods'].extend(matches)
                analysis['has_algorithm'] = True
        
        # 查找可疑的函数（可能包含算法）
        suspicious_patterns = [
            r'function\s+[a-zA-Z_$][a-zA-Z0-9_$]*\s*\([^)]*\)\s*\{[^}]*(?:key|role|strengthen|args|encrypt|hash)[^}]*\}',
            r'[a-zA-Z_$][a-zA-Z0-9_$]*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]*(?:key|role|strengthen|args|encrypt|hash)[^}]*\}',
        ]
        
        for pattern in suspicious_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['suspicious_functions'].extend(matches[:5])  # 只保留前5个
                analysis['has_algorithm'] = True
        
        # 查找算法模式
        algorithm_patterns = [
            r'newBabelAwardCollection[^}]*\{[^}]{100,}\}',
            r'args\s*[:=][^;]{50,}',
            r'function\s+\w*[Aa]rgs\w*\s*\([^)]*\)\s*\{[^}]{50,}',
        ]
        
        for pattern in algorithm_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['algorithm_patterns'].extend(matches)
                analysis['has_algorithm'] = True
        
        return analysis
        
    def extract_inline_scripts(self):
        """提取页面内联脚本"""
        print("正在分析页面内联脚本...")
        
        script_elements = self.driver.find_elements(By.TAG_NAME, "script")
        inline_scripts = []
        
        for i, script in enumerate(script_elements):
            try:
                script_content = script.get_attribute('innerHTML')
                if script_content and len(script_content) > 100:
                    analysis = self.analyze_js_content(script_content, f"inline_script_{i}")
                    if analysis['has_algorithm']:
                        inline_scripts.append({
                            'index': i,
                            'content': script_content,
                            'analysis': analysis,
                            'size': len(script_content)
                        })
            except Exception as e:
                continue
                
        return inline_scripts
        
    def run(self, url):
        """运行算法分析"""
        try:
            print("正在设置浏览器...")
            self.setup_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(8)  # 等待页面完全加载
            
            # 滚动页面确保所有资源加载
            print("正在滚动页面加载所有资源...")
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(3)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # 提取JavaScript文件
            js_urls = self.extract_js_files(url)
            
            # 下载并分析JavaScript文件
            self.download_and_analyze_js(js_urls)
            
            # 分析内联脚本
            inline_scripts = self.extract_inline_scripts()
            
            result = {
                'total_js_files': len(js_urls),
                'analyzed_files': len(self.js_files),
                'external_js_files': self.js_files,
                'inline_scripts': inline_scripts,
                'summary': {
                    'files_with_algorithms': len(self.js_files),
                    'inline_scripts_with_algorithms': len(inline_scripts)
                }
            }
            
            return result
            
        except Exception as e:
            print(f"运行时出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    analyzer = JDAlgorithmAnalyzer()
    result = analyzer.run(url)
    
    if result:
        print("\n=== 算法分析结果 ===")
        print(f"总共找到 {result['total_js_files']} 个JavaScript文件")
        print(f"分析了 {result['analyzed_files']} 个包含算法的外部文件")
        print(f"分析了 {result['summary']['inline_scripts_with_algorithms']} 个包含算法的内联脚本")
        
        # 保存完整结果
        with open('jd_algorithm_analysis.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print("\n完整分析结果已保存到 jd_algorithm_analysis.json")
        
        # 显示关键发现
        print("\n=== 关键发现 ===")
        
        for i, js_file in enumerate(result['external_js_files'], 1):
            print(f"\n外部文件 {i}: {js_file['url']}")
            analysis = js_file['analysis']
            
            if analysis['crypto_functions']:
                print(f"  加密函数: {analysis['crypto_functions'][:3]}")
            if analysis['key_generation']:
                print(f"  Key生成: 找到 {len(analysis['key_generation'])} 个相关函数")
            if analysis['role_generation']:
                print(f"  Role生成: 找到 {len(analysis['role_generation'])} 个相关函数")
            if analysis['strengthen_generation']:
                print(f"  Strengthen生成: 找到 {len(analysis['strengthen_generation'])} 个相关函数")
            if analysis['hash_functions']:
                print(f"  哈希函数: 找到 {len(analysis['hash_functions'])} 个相关函数")
        
        for i, script in enumerate(result['inline_scripts'], 1):
            print(f"\n内联脚本 {i} (索引 {script['index']}):")
            analysis = script['analysis']
            
            if analysis['crypto_functions']:
                print(f"  加密函数: {analysis['crypto_functions'][:3]}")
            if analysis['algorithm_patterns']:
                print(f"  算法模式: 找到 {len(analysis['algorithm_patterns'])} 个相关模式")
        
        # 保存关键算法代码
        algorithm_codes = []
        
        for js_file in result['external_js_files']:
            analysis = js_file['analysis']
            if analysis['key_generation'] or analysis['role_generation'] or analysis['strengthen_generation']:
                algorithm_codes.append({
                    'source': js_file['url'],
                    'type': 'external',
                    'key_generation': analysis['key_generation'],
                    'role_generation': analysis['role_generation'],
                    'strengthen_generation': analysis['strengthen_generation'],
                    'suspicious_functions': analysis['suspicious_functions']
                })
        
        for script in result['inline_scripts']:
            analysis = script['analysis']
            if analysis['algorithm_patterns'] or analysis['suspicious_functions']:
                algorithm_codes.append({
                    'source': f"inline_script_{script['index']}",
                    'type': 'inline',
                    'algorithm_patterns': analysis['algorithm_patterns'],
                    'suspicious_functions': analysis['suspicious_functions']
                })
        
        with open('jd_algorithm_codes.json', 'w', encoding='utf-8') as f:
            json.dump(algorithm_codes, f, indent=2, ensure_ascii=False)
        print("\n关键算法代码已保存到 jd_algorithm_codes.json")
        
    else:
        print("分析失败")

if __name__ == "__main__":
    main()
