#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东JavaScript深度分析器
专门分析templates.cd9d0a0e.js文件，提取args生成逻辑和API调用模式
"""

import json
import re
import requests
from urllib.parse import unquote

class JDJSDeepAnalyzer:
    def __init__(self):
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def download_js_file(self, url):
        """下载JavaScript文件"""
        try:
            headers = {
                'User-Agent': self.jd_ua,
                'Accept': 'application/javascript, */*',
                'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
                'Cache-Control': 'no-cache'
            }
            
            print(f"正在下载: {url}")
            response = requests.get(url, headers=headers, timeout=30)
            
            if response.status_code == 200:
                print(f"下载成功，文件大小: {len(response.text)} 字符")
                return response.text
            else:
                print(f"下载失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"下载文件失败: {e}")
            return None
            
    def extract_args_generation_logic(self, content):
        """提取args生成逻辑"""
        args_patterns = []
        
        # 查找args相关的代码段
        patterns = [
            # 查找args赋值
            r'args\s*[:=]\s*[^,;}\n]{10,}',
            # 查找包含args的函数调用
            r'[^,\s]+\s*\([^)]*args[^)]*\)[^,;}\n]{0,50}',
            # 查找args构造逻辑
            r'[^,\s]+\s*\+\s*["\']_bingo["\'][^,;}\n]{0,30}',
            # 查找key, roleId, strengthenKey模式
            r'key\s*[:=][^,;}\n]{5,50}',
            r'roleId\s*[:=][^,;}\n]{5,50}',
            r'strengthenKey\s*[:=][^,;}\n]{5,50}',
            # 查找完整的args构造
            r'["\']key=[^"\']*_bingo[^"\']*["\']',
            r'["\']roleId=[^"\']*_bingo[^"\']*["\']',
            r'["\']strengthenKey=[^"\']*_bingo[^"\']*["\']'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE)
            if matches:
                args_patterns.extend(matches)
                
        return args_patterns
        
    def extract_api_call_logic(self, content):
        """提取API调用逻辑"""
        api_calls = []
        
        # 查找newBabelAwardCollection相关的调用
        patterns = [
            # 查找完整的API调用
            r'[^,\s]+\s*\([^)]*newBabelAwardCollection[^)]*\)[^;}{]{0,200}',
            # 查找URL构造
            r'["\'][^"\']*api\.m\.jd\.com[^"\']*["\']',
            r'["\'][^"\']*client\.action[^"\']*["\']',
            # 查找body构造
            r'body\s*[:=]\s*JSON\.stringify\s*\([^)]+\)',
            # 查找h5st相关
            r'h5st\s*[:=][^,;}\n]{10,}',
            # 查找完整的请求参数
            r'\{[^}]*functionId[^}]*newBabelAwardCollection[^}]*\}',
            # 查找POST请求
            r'POST[^,;}\n]{10,100}'
        ]
        
        for pattern in patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.MULTILINE | re.DOTALL)
            if matches:
                api_calls.extend(matches)
                
        return api_calls
        
    def extract_parameter_generation(self, content):
        """提取参数生成逻辑"""
        parameters = {}
        
        # 查找各种参数的生成逻辑
        param_patterns = {
            'activityId': r'activityId\s*[:=]\s*[^,;}\n]+',
            'scene': r'scene\s*[:=]\s*[^,;}\n]+',
            'floorId': r'floorId\s*[:=]\s*[^,;}\n]+',
            'from': r'from\s*[:=]\s*[^,;}\n]+',
            'pageClick': r'pageClick\s*[:=]\s*[^,;}\n]+',
            'platform': r'platform\s*[:=]\s*[^,;}\n]+',
            'client': r'client\s*[:=]\s*[^,;}\n]+',
            'clientVersion': r'clientVersion\s*[:=]\s*[^,;}\n]+',
            'appid': r'appid\s*[:=]\s*[^,;}\n]+'
        }
        
        for param_name, pattern in param_patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                parameters[param_name] = matches
                
        return parameters
        
    def extract_function_definitions(self, content):
        """提取函数定义"""
        functions = []
        
        # 查找包含关键词的函数
        keywords = ['newBabel', 'args', 'h5st', 'award', 'collection', 'coupon']
        
        for keyword in keywords:
            # 查找包含关键词的函数定义
            pattern = rf'function\s+\w*{keyword}\w*\s*\([^)]*\)\s*\{{[^}}]{{50,500}}\}}'
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                functions.extend(matches)
                
            # 查找箭头函数
            pattern = rf'\w*{keyword}\w*\s*[:=]\s*\([^)]*\)\s*=>\s*\{{[^}}]{{50,500}}\}}'
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                functions.extend(matches)
                
        return functions
        
    def extract_string_constants(self, content):
        """提取字符串常量"""
        constants = {}
        
        # 查找重要的字符串常量
        patterns = {
            'urls': r'["\'][^"\']*(?:api\.m\.jd\.com|client\.action)[^"\']*["\']',
            'function_ids': r'["\'][^"\']*(?:newBabel|babel)[^"\']*["\']',
            'parameters': r'["\'][^"\']*(?:args|h5st|body)[^"\']*["\']',
            'endpoints': r'["\'][^"\']*(?:AwardCollection|GetGuideTips)[^"\']*["\']',
            'bingo_patterns': r'["\'][^"\']*_bingo[^"\']*["\']'
        }
        
        for category, pattern in patterns.items():
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                constants[category] = list(set(matches))  # 去重
                
        return constants
        
    def find_critical_code_blocks(self, content):
        """查找关键代码块"""
        critical_blocks = []
        
        # 查找包含完整API调用逻辑的代码块
        patterns = [
            # 查找包含newBabelAwardCollection的大代码块
            r'[^{]*newBabelAwardCollection[^{]*\{[^}]{200,1000}\}',
            # 查找包含args构造的代码块
            r'[^{]*args[^{]*\{[^}]{100,500}\}',
            # 查找包含h5st的代码块
            r'[^{]*h5st[^{]*\{[^}]{100,500}\}',
            # 查找包含POST请求的代码块
            r'[^{]*POST[^{]*\{[^}]{100,500}\}'
        ]
        
        for i, pattern in enumerate(patterns):
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                for match in matches:
                    critical_blocks.append({
                        'type': f'pattern_{i+1}',
                        'content': match,
                        'length': len(match)
                    })
                    
        return critical_blocks
        
    def analyze_js_file(self, url):
        """分析JavaScript文件"""
        # 下载文件
        content = self.download_js_file(url)
        if not content:
            return None
            
        print("开始深度分析...")
        
        # 提取各种逻辑
        args_logic = self.extract_args_generation_logic(content)
        api_logic = self.extract_api_call_logic(content)
        parameters = self.extract_parameter_generation(content)
        functions = self.extract_function_definitions(content)
        constants = self.extract_string_constants(content)
        critical_blocks = self.find_critical_code_blocks(content)
        
        analysis_result = {
            'url': url,
            'file_size': len(content),
            'args_generation_logic': args_logic,
            'api_call_logic': api_logic,
            'parameter_generation': parameters,
            'function_definitions': functions,
            'string_constants': constants,
            'critical_code_blocks': critical_blocks,
            'summary': {
                'args_patterns_count': len(args_logic),
                'api_patterns_count': len(api_logic),
                'parameter_types_count': len(parameters),
                'function_count': len(functions),
                'constant_categories': len(constants),
                'critical_blocks_count': len(critical_blocks)
            }
        }
        
        return analysis_result
        
    def extract_real_api_template(self, analysis_result):
        """提取真实的API模板"""
        templates = []
        
        # 基于分析结果构造API模板
        if analysis_result['api_call_logic']:
            for api_call in analysis_result['api_call_logic']:
                if 'newBabelAwardCollection' in api_call:
                    template = {
                        'function_id': 'newBabelAwardCollection',
                        'method': 'POST',
                        'url': 'https://api.m.jd.com/client.action',
                        'base_params': {
                            'functionId': 'newBabelAwardCollection',
                            'client': 'wh5',
                            'clientVersion': '1.0.0',
                            'appid': 'babelh5'
                        },
                        'body_template': {
                            'activityId': '4P9a2T9osR9JvtzHVaYTPvsecRtg',
                            'scene': '1',
                            'args': 'key={key}_bingo,roleId={roleId}_bingo,strengthenKey={strengthenKey}_bingo'
                        },
                        'source_pattern': api_call[:100] + '...' if len(api_call) > 100 else api_call
                    }
                    templates.append(template)
                    
        return templates

def main():
    analyzer = JDJSDeepAnalyzer()
    
    # 分析最重要的JavaScript文件
    important_files = [
        "https://storage11.360buyimg.com/tower/babelnode/js/templates.cd9d0a0e.js",
        "https://storage11.360buyimg.com/tower/babelnode/babellib-ab1c1b95fb.js",
        "https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js"
    ]
    
    all_results = []
    
    for url in important_files:
        print(f"\n=== 分析文件: {url} ===")
        result = analyzer.analyze_js_file(url)
        
        if result:
            all_results.append(result)
            
            print(f"文件大小: {result['file_size']} 字符")
            print(f"Args模式数量: {result['summary']['args_patterns_count']}")
            print(f"API模式数量: {result['summary']['api_patterns_count']}")
            print(f"参数类型数量: {result['summary']['parameter_types_count']}")
            print(f"函数数量: {result['summary']['function_count']}")
            print(f"关键代码块数量: {result['summary']['critical_blocks_count']}")
            
            # 显示重要的args模式
            if result['args_generation_logic']:
                print(f"\n重要的Args模式:")
                for i, pattern in enumerate(result['args_generation_logic'][:5], 1):
                    print(f"  {i}. {pattern[:100]}...")
                    
            # 显示API调用模式
            if result['api_call_logic']:
                print(f"\nAPI调用模式:")
                for i, pattern in enumerate(result['api_call_logic'][:3], 1):
                    print(f"  {i}. {pattern[:100]}...")
                    
            # 提取API模板
            templates = analyzer.extract_real_api_template(result)
            if templates:
                print(f"\n提取的API模板:")
                for i, template in enumerate(templates, 1):
                    print(f"  {i}. {template['function_id']}")
                    print(f"     URL: {template['url']}")
                    print(f"     Body: {template['body_template']}")
        else:
            print("分析失败")
            
    # 保存所有结果
    final_result = {
        'analysis_results': all_results,
        'summary': {
            'analyzed_files': len(all_results),
            'total_args_patterns': sum(r['summary']['args_patterns_count'] for r in all_results),
            'total_api_patterns': sum(r['summary']['api_patterns_count'] for r in all_results)
        }
    }
    
    with open('jd_js_deep_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(final_result, f, indent=2, ensure_ascii=False)
        
    print(f"\n=== 深度分析完成 ===")
    print(f"分析了 {len(all_results)} 个文件")
    print(f"总Args模式: {final_result['summary']['total_args_patterns']}")
    print(f"总API模式: {final_result['summary']['total_api_patterns']}")
    print("详细结果已保存到 jd_js_deep_analysis.json")

if __name__ == "__main__":
    main()
