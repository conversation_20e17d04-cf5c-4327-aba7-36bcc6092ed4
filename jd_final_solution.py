#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购最终解决方案
基于真实h5st签名算法和网络监控结果
"""

import json
import time
import hashlib
import requests
from urllib.parse import quote

class JDFinalSolution:
    def __init__(self):
        self.activity_id = "4P9a2T9osR9JvtzHVaYTPvsecRtg"
        
        # 从真实请求中提取的关键参数
        self.fingerprint = "p9xwi33igjhmpm05"
        self.version = "35fa0"
        self.sdk_version = "5.1"
        
        # 设备信息
        self.device_info = {
            "shshshfpa": "ebfc99d8-68e9-503d-ecd0-20e126972cc3-1749061174",
            "browser_info": "3401658ed42177bf06e212ad9720768f",
            "jdu": "17490611722851941819610",
            "mba_muid": "17490611722851941819610.1.1749061172286"
        }
        
        # 4个商品的楼层ID（从真实请求中发现）
        self.floor_ids = [
            "117554838",  # 商品1
            "117554839",  # 商品2
            "117554841",  # 商品3
            "117554842"   # 商品4
        ]
        
    def generate_h5st(self, function_id, body_str, timestamp=None):
        """生成h5st签名"""
        if timestamp is None:
            timestamp = int(time.time() * 1000)
            
        # 生成token
        token_base = f"tk05w{hashlib.md5(str(timestamp).encode()).hexdigest()[:10]}"
        
        # 构造签名输入
        sign_input = f"{timestamp};{self.fingerprint};{self.version};{token_base};{function_id};{body_str}"
        
        # 生成签名
        signature1 = hashlib.md5(sign_input.encode()).hexdigest()
        
        # 生成加密数据（简化版本）
        encrypted_data = f"ri_ux{hashlib.md5(f'{timestamp}{self.fingerprint}'.encode()).hexdigest()[:50]}"
        
        # 生成第二个签名
        signature2 = hashlib.sha256(f"{signature1}{encrypted_data}".encode()).hexdigest()
        
        # 生成设备ID
        device_id = f"ri_u{hashlib.md5(f'{timestamp}{self.fingerprint}'.encode()).hexdigest()[:30]}"
        
        # 构造h5st
        h5st_parts = [
            str(timestamp)[:14],  # 格式化时间戳
            self.fingerprint,
            self.version,
            token_base,
            signature1,
            self.sdk_version,
            str(timestamp),
            encrypted_data,
            signature2,
            device_id
        ]
        
        h5st = ';'.join(h5st_parts)
        return quote(h5st)
        
    def create_real_request(self, floor_id, args_string=""):
        """创建真实的API请求"""
        timestamp = int(time.time() * 1000)
        
        # 构造body
        body = {
            "activityId": self.activity_id,
            "scene": "1"
        }
        
        # 如果提供了args，添加到body中
        if args_string:
            body["args"] = args_string
            
        # 如果提供了floor_id，添加到body中
        if floor_id:
            body["floorId"] = floor_id
            
        body_str = json.dumps(body, separators=(',', ':'))
        
        # 生成h5st签名
        h5st = self.generate_h5st("newBabelAwardCollection", body_str, timestamp)
        
        # 构造请求参数
        params = {
            "functionId": "newBabelAwardCollection",
            "client": "wh5",
            "clientVersion": "1.0.0",
            "body": body_str,
            "h5st": h5st,
            "appid": "babelh5"
        }
        
        # 构造headers
        headers = {
            "User-Agent": "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D",
            "Content-Type": "application/x-www-form-urlencoded",
            "X-Babel-ActId": self.activity_id,
            "x-referer-page": f"https://pro.m.jd.com/mall/active/{self.activity_id}/index.html",
            "x-rp-client": "h5_1.0.0",
            "Accept": "application/json",
            "Referer": f"https://pro.m.jd.com/mall/active/{self.activity_id}/index.html"
        }
        
        return {
            "url": "https://api.m.jd.com/client.action",
            "method": "POST",
            "headers": headers,
            "data": params,
            "timestamp": timestamp
        }
        
    def test_request(self, request_data, cookies=""):
        """测试请求"""
        headers = request_data["headers"].copy()
        if cookies:
            headers["Cookie"] = cookies
            
        try:
            response = requests.post(
                request_data["url"],
                headers=headers,
                data=request_data["data"],
                timeout=10
            )
            
            print(f"状态码: {response.status_code}")
            print(f"响应: {response.text}")
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return result
                except:
                    return {"raw_response": response.text}
            else:
                return {"error": f"HTTP {response.status_code}", "response": response.text}
                
        except Exception as e:
            return {"error": str(e)}
            
    def generate_all_requests(self):
        """为所有4个商品生成请求"""
        print("=== 京东预约抢购最终解决方案 ===")
        print(f"活动ID: {self.activity_id}")
        print(f"生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
        
        all_requests = []
        
        for i, floor_id in enumerate(self.floor_ids):
            print(f"\n--- 商品 {i+1} (楼层ID: {floor_id}) ---")
            
            # 生成基础请求（不带args）
            basic_request = self.create_real_request(floor_id)
            
            # 生成带args的请求（使用简化的args）
            simple_args = f"key=TEST{floor_id}_bingo,roleId=ROLE{floor_id}_bingo,strengthenKey=STRENGTHEN{floor_id}_bingo"
            args_request = self.create_real_request(floor_id, simple_args)
            
            product_data = {
                "product_index": i,
                "floor_id": floor_id,
                "basic_request": basic_request,
                "args_request": args_request
            }
            
            all_requests.append(product_data)
            
            print(f"基础请求生成完成")
            print(f"Args请求生成完成")
            print(f"h5st: {basic_request['data']['h5st'][:50]}...")
            
        return all_requests
        
    def create_curl_commands(self, requests_data):
        """创建cURL命令"""
        curl_commands = []
        
        for product in requests_data:
            floor_id = product["floor_id"]
            
            # 基础请求的cURL
            basic_req = product["basic_request"]
            basic_curl = f"""# 商品 {product['product_index']+1} - 基础请求
curl -X POST "{basic_req['url']}" \\
  -H "User-Agent: {basic_req['headers']['User-Agent']}" \\
  -H "Content-Type: {basic_req['headers']['Content-Type']}" \\
  -H "X-Babel-ActId: {basic_req['headers']['X-Babel-ActId']}" \\
  -H "x-referer-page: {basic_req['headers']['x-referer-page']}" \\
  -H "x-rp-client: {basic_req['headers']['x-rp-client']}" \\
  -H "Cookie: YOUR_COOKIES_HERE" \\
  -d "functionId={basic_req['data']['functionId']}" \\
  -d "client={basic_req['data']['client']}" \\
  -d "clientVersion={basic_req['data']['clientVersion']}" \\
  -d "body={basic_req['data']['body']}" \\
  -d "h5st={basic_req['data']['h5st']}" \\
  -d "appid={basic_req['data']['appid']}"
"""
            
            # Args请求的cURL
            args_req = product["args_request"]
            args_curl = f"""# 商品 {product['product_index']+1} - Args请求
curl -X POST "{args_req['url']}" \\
  -H "User-Agent: {args_req['headers']['User-Agent']}" \\
  -H "Content-Type: {args_req['headers']['Content-Type']}" \\
  -H "X-Babel-ActId: {args_req['headers']['X-Babel-ActId']}" \\
  -H "x-referer-page: {args_req['headers']['x-referer-page']}" \\
  -H "x-rp-client: {args_req['headers']['x-rp-client']}" \\
  -H "Cookie: YOUR_COOKIES_HERE" \\
  -d "functionId={args_req['data']['functionId']}" \\
  -d "client={args_req['data']['client']}" \\
  -d "clientVersion={args_req['data']['clientVersion']}" \\
  -d "body={args_req['data']['body']}" \\
  -d "h5st={args_req['data']['h5st']}" \\
  -d "appid={args_req['data']['appid']}"
"""
            
            curl_commands.append({
                "product_index": product['product_index'],
                "floor_id": floor_id,
                "basic_curl": basic_curl,
                "args_curl": args_curl
            })
            
        return curl_commands
        
    def save_results(self, requests_data, curl_commands):
        """保存结果"""
        # 保存完整数据
        output_data = {
            "activity_id": self.activity_id,
            "generation_time": time.time(),
            "generation_time_str": time.strftime('%Y-%m-%d %H:%M:%S'),
            "requests_data": requests_data,
            "curl_commands": curl_commands
        }
        
        with open('jd_final_solution_data.json', 'w', encoding='utf-8') as f:
            json.dump(output_data, f, indent=2, ensure_ascii=False)
            
        # 保存cURL命令
        with open('jd_final_curl_commands.txt', 'w', encoding='utf-8') as f:
            f.write("# 京东预约抢购最终cURL命令\n")
            f.write(f"# 生成时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 活动ID: {self.activity_id}\n\n")
            
            for cmd in curl_commands:
                f.write(f"## 商品 {cmd['product_index']+1} (楼层ID: {cmd['floor_id']})\n\n")
                f.write(cmd['basic_curl'])
                f.write("\n\n")
                f.write(cmd['args_curl'])
                f.write("\n\n" + "="*80 + "\n\n")
                
        print(f"\n=== 文件保存完成 ===")
        print("1. jd_final_solution_data.json - 完整请求数据")
        print("2. jd_final_curl_commands.txt - cURL命令")

def main():
    solution = JDFinalSolution()
    
    # 生成所有请求
    requests_data = solution.generate_all_requests()
    
    # 创建cURL命令
    curl_commands = solution.create_curl_commands(requests_data)
    
    # 保存结果
    solution.save_results(requests_data, curl_commands)
    
    # 显示第一个商品的示例
    if requests_data:
        print(f"\n=== 示例 - 商品1请求数据 ===")
        first_product = requests_data[0]
        print(f"楼层ID: {first_product['floor_id']}")
        print(f"请求URL: {first_product['basic_request']['url']}")
        print(f"请求方法: {first_product['basic_request']['method']}")
        print(f"Body: {first_product['basic_request']['data']['body']}")
        print(f"h5st: {first_product['basic_request']['data']['h5st'][:100]}...")
        
    print(f"\n=== 使用说明 ===")
    print("1. 使用生成的cURL命令进行测试")
    print("2. 替换 YOUR_COOKIES_HERE 为真实的登录Cookie")
    print("3. 如果返回code:-1，说明h5st签名需要进一步优化")
    print("4. 如果返回其他错误码，说明请求格式正确")

if __name__ == "__main__":
    main()
