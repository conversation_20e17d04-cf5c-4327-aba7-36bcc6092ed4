#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东专业网络监控器
使用Chrome DevTools Protocol深度监控网络请求和文件加载
"""

import json
import time
import asyncio
import websockets
import requests
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from webdriver_manager.chrome import ChromeDriverManager
import threading
from urllib.parse import unquote, urlparse
import re

class JDProfessionalNetworkMonitor:
    def __init__(self):
        self.driver = None
        self.websocket = None
        self.network_requests = []
        self.loaded_files = []
        self.api_calls = []
        self.js_executions = []
        
        # 京东APP User-Agent
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
    def setup_professional_driver(self):
        """设置专业级Chrome驱动"""
        chrome_options = Options()
        
        # 基础设置
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 设置User-Agent
        chrome_options.add_argument(f'--user-agent={self.jd_ua}')
        
        # 启用远程调试
        chrome_options.add_argument('--remote-debugging-port=9222')
        chrome_options.add_argument('--enable-automation')
        
        # 启用详细日志
        chrome_options.set_capability('goog:loggingPrefs', {
            'performance': 'ALL',
            'browser': 'ALL',
            'driver': 'ALL'
        })
        
        # 禁用图片和CSS加载以提高性能
        prefs = {
            "profile.managed_default_content_settings.images": 2,
            "profile.default_content_setting_values.notifications": 2
        }
        chrome_options.add_experimental_option("prefs", prefs)
        
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
        # 启用CDP域
        self.driver.execute_cdp_cmd('Network.enable', {})
        self.driver.execute_cdp_cmd('Runtime.enable', {})
        self.driver.execute_cdp_cmd('Debugger.enable', {})
        self.driver.execute_cdp_cmd('Page.enable', {})
        
    async def connect_to_devtools(self):
        """连接到Chrome DevTools WebSocket"""
        try:
            # 获取WebSocket调试URL
            response = requests.get('http://localhost:9222/json')
            tabs = response.json()
            
            if tabs:
                websocket_url = tabs[0]['webSocketDebuggerUrl']
                self.websocket = await websockets.connect(websocket_url)
                print(f"已连接到DevTools WebSocket: {websocket_url}")
                
                # 启用网络域
                await self.websocket.send(json.dumps({
                    "id": 1,
                    "method": "Network.enable"
                }))
                
                # 启用运行时域
                await self.websocket.send(json.dumps({
                    "id": 2,
                    "method": "Runtime.enable"
                }))
                
                # 启用调试器域
                await self.websocket.send(json.dumps({
                    "id": 3,
                    "method": "Debugger.enable"
                }))
                
                return True
        except Exception as e:
            print(f"连接DevTools失败: {e}")
            return False
            
    async def monitor_network_events(self):
        """监控网络事件"""
        if not self.websocket:
            return
            
        try:
            while True:
                message = await self.websocket.recv()
                event = json.loads(message)
                
                if 'method' in event:
                    await self.handle_network_event(event)
                    
        except websockets.exceptions.ConnectionClosed:
            print("WebSocket连接已关闭")
        except Exception as e:
            print(f"监控网络事件时出错: {e}")
            
    async def handle_network_event(self, event):
        """处理网络事件"""
        method = event['method']
        params = event.get('params', {})
        
        if method == 'Network.requestWillBeSent':
            request = params['request']
            request_id = params['requestId']
            
            # 记录请求
            request_data = {
                'requestId': request_id,
                'url': request['url'],
                'method': request['method'],
                'headers': request['headers'],
                'postData': request.get('postData', ''),
                'timestamp': params['timestamp'],
                'type': 'request'
            }
            
            self.network_requests.append(request_data)
            
            # 特别关注API请求
            if 'api.m.jd.com' in request['url'] or 'newBabelAwardCollection' in request['url']:
                self.api_calls.append(request_data)
                print(f"🔥 发现API调用: {request['url']}")
                
                # 解析args参数
                if 'body=' in request['url']:
                    self.parse_api_args(request['url'])
                    
        elif method == 'Network.responseReceived':
            response = params['response']
            request_id = params['requestId']
            
            # 记录响应
            response_data = {
                'requestId': request_id,
                'url': response['url'],
                'status': response['status'],
                'headers': response['headers'],
                'mimeType': response['mimeType'],
                'timestamp': params['timestamp'],
                'type': 'response'
            }
            
            self.network_requests.append(response_data)
            
            # 分析JavaScript文件
            if response['mimeType'] == 'application/javascript' or response['url'].endswith('.js'):
                await self.analyze_js_file(request_id, response['url'])
                
        elif method == 'Runtime.consoleAPICalled':
            # 监控console输出
            console_data = {
                'type': params['type'],
                'args': params['args'],
                'timestamp': params['timestamp']
            }
            print(f"Console: {console_data}")
            
    def parse_api_args(self, url):
        """解析API中的args参数"""
        try:
            if 'body=' in url:
                body_start = url.find('body=') + 5
                body_end = url.find('&', body_start)
                if body_end == -1:
                    body_end = len(url)
                    
                body_encoded = url[body_start:body_end]
                body_decoded = unquote(body_encoded)
                body_json = json.loads(body_decoded)
                
                if 'args' in body_json:
                    print(f"🎯 发现真实args参数: {body_json['args']}")
                    
                    # 详细分析args结构
                    self.analyze_args_structure(body_json['args'])
                    
        except Exception as e:
            print(f"解析args参数失败: {e}")
            
    def analyze_args_structure(self, args_string):
        """分析args参数结构"""
        print(f"\n=== 真实Args参数分析 ===")
        print(f"完整args: {args_string}")
        
        # 解析各个部分
        parts = args_string.split(',')
        for part in parts:
            if '=' in part:
                key, value = part.split('=', 1)
                print(f"{key}: {value}")
                
                if value.endswith('_bingo'):
                    clean_value = value[:-6]
                    print(f"  清理后: {clean_value}")
                    print(f"  长度: {len(clean_value)}")
                    print(f"  类型: {'十六进制' if all(c in '0123456789ABCDEFabcdef' for c in clean_value) else '其他'}")
                    
    async def analyze_js_file(self, request_id, url):
        """分析JavaScript文件内容"""
        try:
            # 获取响应体
            await self.websocket.send(json.dumps({
                "id": int(time.time()),
                "method": "Network.getResponseBody",
                "params": {"requestId": request_id}
            }))
            
            # 等待响应
            response = await self.websocket.recv()
            response_data = json.loads(response)
            
            if 'result' in response_data and 'body' in response_data['result']:
                js_content = response_data['result']['body']
                
                # 分析JavaScript内容
                analysis = self.analyze_js_content(js_content, url)
                if analysis['has_args_logic']:
                    self.loaded_files.append({
                        'url': url,
                        'content': js_content,
                        'analysis': analysis,
                        'size': len(js_content)
                    })
                    
                    print(f"📄 发现包含args逻辑的JS文件: {url}")
                    
        except Exception as e:
            print(f"分析JS文件失败 {url}: {e}")
            
    def analyze_js_content(self, content, url):
        """分析JavaScript内容"""
        analysis = {
            'has_args_logic': False,
            'args_functions': [],
            'crypto_functions': [],
            'api_calls': [],
            'key_patterns': []
        }
        
        # 查找args相关的函数
        args_patterns = [
            r'function\s+\w*[Aa]rgs\w*\s*\([^)]*\)\s*\{[^}]{50,}',
            r'newBabelAwardCollection[^}]*\{[^}]{50,}',
            r'args\s*[:=][^;]{50,}',
            r'key\s*[:=][^,]{20,}',
            r'roleId\s*[:=][^,]{50,}',
            r'strengthenKey\s*[:=][^,]{20,}'
        ]
        
        for pattern in args_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            if matches:
                analysis['args_functions'].extend(matches)
                analysis['has_args_logic'] = True
                
        # 查找加密函数
        crypto_patterns = [
            r'md5\s*\([^)]+\)',
            r'MD5\s*\([^)]+\)',
            r'sha\d*\s*\([^)]+\)',
            r'encrypt\s*\([^)]+\)',
            r'hash\s*\([^)]+\)'
        ]
        
        for pattern in crypto_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                analysis['crypto_functions'].extend(matches)
                analysis['has_args_logic'] = True
                
        # 查找API调用
        api_patterns = [
            r'api\.m\.jd\.com[^"\']*',
            r'client\.action[^"\']*',
            r'functionId[^"\']*newBabel[^"\']*'
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                analysis['api_calls'].extend(matches)
                analysis['has_args_logic'] = True
                
        return analysis
        
    def trigger_user_interactions(self):
        """触发用户交互以产生网络请求"""
        try:
            print("正在触发用户交互...")
            
            # 等待页面加载
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 滚动页面
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # 查找并点击可能的预约按钮
            selectors = [
                "button", "div[onclick]", "a[onclick]", "[data-role]",
                ".btn", ".button", "[class*='reserve']", "[class*='book']",
                "[class*='award']", "[class*='collection']"
            ]
            
            clicked_count = 0
            for selector in selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    for element in elements[:3]:  # 每种选择器最多点击3个
                        try:
                            if element.is_displayed() and element.is_enabled():
                                self.driver.execute_script("arguments[0].scrollIntoView();", element)
                                time.sleep(0.5)
                                
                                # 点击元素
                                try:
                                    element.click()
                                except:
                                    self.driver.execute_script("arguments[0].click();", element)
                                
                                clicked_count += 1
                                time.sleep(1)
                                
                                print(f"点击了元素: {selector} ({clicked_count})")
                                
                        except Exception as e:
                            continue
                            
                except Exception as e:
                    continue
                    
            print(f"总共点击了 {clicked_count} 个元素")
            
        except Exception as e:
            print(f"触发用户交互时出错: {e}")
            
    async def run_professional_monitoring(self, url):
        """运行专业监控"""
        try:
            print("=== 启动专业网络监控 ===")
            
            # 设置驱动
            self.setup_professional_driver()
            
            # 连接DevTools
            connected = await self.connect_to_devtools()
            if not connected:
                print("无法连接到DevTools，使用基础监控模式")
                return await self.run_basic_monitoring(url)
            
            # 启动网络监控任务
            monitor_task = asyncio.create_task(self.monitor_network_events())
            
            # 加载页面
            print(f"正在加载页面: {url}")
            self.driver.get(url)
            
            # 等待页面加载
            time.sleep(5)
            
            # 触发用户交互
            self.trigger_user_interactions()
            
            # 继续监控一段时间
            print("继续监控网络活动...")
            await asyncio.sleep(10)
            
            # 停止监控
            monitor_task.cancel()
            
            # 分析结果
            return self.analyze_monitoring_results()
            
        except Exception as e:
            print(f"专业监控过程中出错: {e}")
            return None
            
        finally:
            if self.websocket:
                await self.websocket.close()
            if self.driver:
                self.driver.quit()
                
    async def run_basic_monitoring(self, url):
        """基础监控模式"""
        print("使用基础监控模式...")
        
        # 加载页面
        self.driver.get(url)
        time.sleep(5)
        
        # 触发交互
        self.trigger_user_interactions()
        
        # 获取性能日志
        logs = self.driver.get_log('performance')
        for log in logs:
            try:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request = message['message']['params']['request']
                    if 'api.m.jd.com' in request['url'] or 'newBabelAwardCollection' in request['url']:
                        self.api_calls.append(request)
                        print(f"发现API调用: {request['url']}")
                        
                        if 'body=' in request['url']:
                            self.parse_api_args(request['url'])
                            
            except Exception as e:
                continue
                
        return self.analyze_monitoring_results()
        
    def analyze_monitoring_results(self):
        """分析监控结果"""
        result = {
            'total_requests': len(self.network_requests),
            'api_calls': self.api_calls,
            'loaded_js_files': self.loaded_files,
            'js_executions': self.js_executions,
            'analysis_summary': {
                'found_api_calls': len(self.api_calls),
                'found_js_files': len(self.loaded_files),
                'has_args_generation': any('args' in str(call) for call in self.api_calls)
            }
        }
        
        return result

def main():
    async def run_monitor():
        url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
        
        monitor = JDProfessionalNetworkMonitor()
        result = await monitor.run_professional_monitoring(url)
        
        if result:
            print("\n=== 监控结果 ===")
            print(f"总请求数: {result['total_requests']}")
            print(f"API调用数: {result['analysis_summary']['found_api_calls']}")
            print(f"JS文件数: {result['analysis_summary']['found_js_files']}")
            
            # 保存结果
            with open('jd_professional_monitoring_result.json', 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=2, ensure_ascii=False)
            print("详细结果已保存到 jd_professional_monitoring_result.json")
            
            # 显示发现的API调用
            if result['api_calls']:
                print("\n=== 发现的API调用 ===")
                for i, call in enumerate(result['api_calls'], 1):
                    print(f"{i}. {call.get('url', 'N/A')}")
                    
        else:
            print("监控失败")
    
    # 运行异步监控
    asyncio.run(run_monitor())

if __name__ == "__main__":
    main()
