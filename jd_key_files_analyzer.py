#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东关键文件深度分析工具
专门分析包含参数生成算法的关键JavaScript文件
"""

import json
import re
import requests
from urllib.parse import unquote

class JDKeyFilesAnalyzer:
    def __init__(self):
        # 京东APP的User-Agent
        self.jd_ua = "jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D"
        
        # 关键文件列表（从之前的分析结果中提取）
        self.key_files = [
            "https://storage.360buyimg.com/webcontainer/main/js-security-v3-rac.js?v=20250605",
            "https://storage.360buyimg.com/babelnode/jd-jssdk/4.5.2/jd-jssdk.js",
            "https://storage.360buyimg.com/jsresource/ws_js/jdwebm.js?v=babel",
            "https://wl.jd.com/unify.min.js",
            "https://storage.360buyimg.com/babelview/sdk/156/1_0_0/jc-com-sdk.min.js",
            "https://storage11.360buyimg.com/tower/babelnode/babellib-ab1c1b95fb.js",
            "https://storage.360buyimg.com/jsresource/risk/handler.js",
            "https://storage11.360buyimg.com/tower/babelnode/js/security.89d49f1d.js"
        ]
        
    def download_file(self, url):
        """下载JavaScript文件"""
        headers = {
            'User-Agent': self.jd_ua,
            'Referer': 'https://pro.m.jd.com/',
            'Accept': 'application/javascript, */*'
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=15)
            if response.status_code == 200:
                return response.text
        except Exception as e:
            print(f"下载文件失败 {url}: {e}")
        return None
        
    def extract_args_generation_functions(self, content, filename):
        """提取args参数生成相关的函数"""
        functions = []
        
        # 查找包含newBabelAwardCollection的函数
        babel_patterns = [
            r'function\s+\w*[Bb]abel\w*\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'\w*[Bb]abel\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'newBabelAwardCollection[^}]*\{[^}]{50,}\}',
        ]
        
        for pattern in babel_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                functions.append({
                    'type': 'babel_function',
                    'code': match[:1000],  # 限制长度
                    'file': filename
                })
        
        # 查找args参数构建函数
        args_patterns = [
            r'function\s+\w*[Aa]rgs\w*\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'\w*[Aa]rgs\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'args\s*[:=][^;]{100,}',
            r'["\']args["\']\s*:\s*[^,]{50,}',
        ]
        
        for pattern in args_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                functions.append({
                    'type': 'args_function',
                    'code': match[:1000],
                    'file': filename
                })
        
        # 查找key生成函数
        key_patterns = [
            r'function\s+\w*[Kk]ey\w*\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'\w*[Kk]ey\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'generateKey\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'createKey\s*\([^)]*\)\s*\{[^}]{50,}\}',
        ]
        
        for pattern in key_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                functions.append({
                    'type': 'key_generation',
                    'code': match[:1000],
                    'file': filename
                })
        
        # 查找roleId生成函数
        role_patterns = [
            r'function\s+\w*[Rr]ole\w*\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'\w*[Rr]ole\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'generateRole\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'roleId\s*[:=][^;]{50,}',
        ]
        
        for pattern in role_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                functions.append({
                    'type': 'role_generation',
                    'code': match[:1000],
                    'file': filename
                })
        
        # 查找strengthenKey生成函数
        strengthen_patterns = [
            r'function\s+\w*[Ss]trengthen\w*\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'\w*[Ss]trengthen\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{100,}\}',
            r'strengthenKey\s*[:=][^;]{50,}',
        ]
        
        for pattern in strengthen_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                functions.append({
                    'type': 'strengthen_generation',
                    'code': match[:1000],
                    'file': filename
                })
        
        return functions
        
    def extract_encryption_algorithms(self, content, filename):
        """提取加密算法"""
        algorithms = []
        
        # MD5算法
        md5_patterns = [
            r'function\s+\w*[Mm][Dd]5\w*\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'\w*[Mm][Dd]5\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}\}',
        ]
        
        for pattern in md5_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                algorithms.append({
                    'type': 'md5',
                    'code': match[:1000],
                    'file': filename
                })
        
        # SHA算法
        sha_patterns = [
            r'function\s+\w*[Ss][Hh][Aa]\w*\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'\w*[Ss][Hh][Aa]\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}\}',
        ]
        
        for pattern in sha_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                algorithms.append({
                    'type': 'sha',
                    'code': match[:1000],
                    'file': filename
                })
        
        # AES算法
        aes_patterns = [
            r'function\s+\w*[Aa][Ee][Ss]\w*\s*\([^)]*\)\s*\{[^}]{50,}\}',
            r'\w*[Aa][Ee][Ss]\w*\s*[:=]\s*function\s*\([^)]*\)\s*\{[^}]{50,}\}',
        ]
        
        for pattern in aes_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                algorithms.append({
                    'type': 'aes',
                    'code': match[:1000],
                    'file': filename
                })
        
        return algorithms
        
    def extract_api_related_code(self, content, filename):
        """提取API相关的代码"""
        api_code = []
        
        # 查找包含newBabelAwardCollection的代码段
        api_patterns = [
            r'newBabelAwardCollection[^}]*\{[^}]{100,}\}',
            r'functionId["\']?\s*[:=]\s*["\']newBabelAwardCollection["\'][^}]{50,}',
            r'client\.action[^}]{100,}',
            r'api\.m\.jd\.com[^}]{50,}',
        ]
        
        for pattern in api_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                api_code.append({
                    'type': 'api_related',
                    'code': match[:1000],
                    'file': filename
                })
        
        return api_code
        
    def find_parameter_construction(self, content, filename):
        """查找参数构造逻辑"""
        constructions = []
        
        # 查找参数拼接逻辑
        param_patterns = [
            r'key\s*\+\s*["\'][^"\']*["\'][^}]{50,}',
            r'roleId\s*\+\s*["\'][^"\']*["\'][^}]{50,}',
            r'strengthenKey\s*\+\s*["\'][^"\']*["\'][^}]{50,}',
            r'["\']key=["\'][^}]{50,}',
            r'["\']roleId=["\'][^}]{50,}',
            r'["\']strengthenKey=["\'][^}]{50,}',
            r'args\s*\+\s*["\'][^"\']*["\'][^}]{50,}',
        ]
        
        for pattern in param_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE | re.DOTALL)
            for match in matches:
                constructions.append({
                    'type': 'parameter_construction',
                    'code': match[:500],
                    'file': filename
                })
        
        return constructions
        
    def analyze_file(self, url):
        """分析单个文件"""
        print(f"正在分析: {url}")
        
        content = self.download_file(url)
        if not content:
            return None
            
        filename = url.split('/')[-1]
        
        # 提取各种类型的代码
        args_functions = self.extract_args_generation_functions(content, filename)
        encryption_algorithms = self.extract_encryption_algorithms(content, filename)
        api_code = self.extract_api_related_code(content, filename)
        param_constructions = self.find_parameter_construction(content, filename)
        
        return {
            'url': url,
            'filename': filename,
            'file_size': len(content),
            'args_functions': args_functions,
            'encryption_algorithms': encryption_algorithms,
            'api_code': api_code,
            'parameter_constructions': param_constructions,
            'total_findings': len(args_functions) + len(encryption_algorithms) + len(api_code) + len(param_constructions)
        }
        
    def run(self):
        """运行分析"""
        print("开始分析关键JavaScript文件...")
        
        results = []
        
        for url in self.key_files:
            result = self.analyze_file(url)
            if result and result['total_findings'] > 0:
                results.append(result)
        
        return results

def main():
    analyzer = JDKeyFilesAnalyzer()
    results = analyzer.run()
    
    print(f"\n=== 分析完成 ===")
    print(f"分析了 {len(results)} 个包含关键代码的文件")
    
    # 保存完整结果
    with open('jd_key_files_analysis.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, indent=2, ensure_ascii=False)
    print("完整分析结果已保存到 jd_key_files_analysis.json")
    
    # 显示摘要
    total_args_functions = 0
    total_encryption_algorithms = 0
    total_api_code = 0
    total_param_constructions = 0
    
    for result in results:
        print(f"\n文件: {result['filename']}")
        print(f"  文件大小: {result['file_size']} 字节")
        print(f"  Args函数: {len(result['args_functions'])}")
        print(f"  加密算法: {len(result['encryption_algorithms'])}")
        print(f"  API代码: {len(result['api_code'])}")
        print(f"  参数构造: {len(result['parameter_constructions'])}")
        
        total_args_functions += len(result['args_functions'])
        total_encryption_algorithms += len(result['encryption_algorithms'])
        total_api_code += len(result['api_code'])
        total_param_constructions += len(result['parameter_constructions'])
        
        # 显示一些关键发现
        if result['args_functions']:
            print("  Args函数示例:")
            for func in result['args_functions'][:2]:
                print(f"    {func['type']}: {func['code'][:100]}...")
                
        if result['parameter_constructions']:
            print("  参数构造示例:")
            for const in result['parameter_constructions'][:2]:
                print(f"    {const['code'][:100]}...")
    
    print(f"\n=== 总计 ===")
    print(f"Args函数: {total_args_functions}")
    print(f"加密算法: {total_encryption_algorithms}")
    print(f"API代码: {total_api_code}")
    print(f"参数构造: {total_param_constructions}")
    
    # 提取最重要的代码片段
    important_code = []
    
    for result in results:
        # 优先收集args相关的函数
        for func in result['args_functions']:
            important_code.append({
                'priority': 'high',
                'type': func['type'],
                'file': result['filename'],
                'code': func['code']
            })
        
        # 收集参数构造逻辑
        for const in result['parameter_constructions']:
            important_code.append({
                'priority': 'medium',
                'type': const['type'],
                'file': result['filename'],
                'code': const['code']
            })
    
    # 保存重要代码片段
    with open('jd_important_code.json', 'w', encoding='utf-8') as f:
        json.dump(important_code, f, indent=2, ensure_ascii=False)
    print("\n重要代码片段已保存到 jd_important_code.json")
    
    print(f"\n找到 {len(important_code)} 个重要代码片段")

if __name__ == "__main__":
    main()
