#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购商品args参数提取工具 V2
专门提取4个预约抢购商品的完整args参数
"""

import json
import time
import re
from selenium import webdriver
from selenium.webdriver.chrome.options import Options
from selenium.webdriver.chrome.service import Service
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException
import requests
from webdriver_manager.chrome import ChromeDriverManager
from urllib.parse import unquote, parse_qs, urlparse
import base64

class JDArgsExtractorV2:
    def __init__(self):
        self.driver = None
        self.products_args = []
        
    def setup_driver(self):
        """设置Chrome浏览器驱动"""
        chrome_options = Options()
        chrome_options.add_argument('--no-sandbox')
        chrome_options.add_argument('--disable-dev-shm-usage')
        chrome_options.add_argument('--disable-gpu')
        chrome_options.add_argument('--window-size=1920,1080')
        chrome_options.add_argument('--disable-web-security')
        chrome_options.add_argument('--allow-running-insecure-content')
        chrome_options.add_argument('--disable-blink-features=AutomationControlled')
        
        # 设置用户代理，模拟移动端
        chrome_options.add_argument('--user-agent=Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1')
        
        # 启用性能日志来捕获网络请求
        chrome_options.set_capability('goog:loggingPrefs', {'performance': 'ALL'})
        
        # 使用ChromeDriverManager自动下载和管理驱动
        service = Service(ChromeDriverManager().install())
        self.driver = webdriver.Chrome(service=service, options=chrome_options)
        
    def parse_args_string(self, args_string):
        """解析args字符串，提取key、roleId、strengthenKey"""
        try:
            # URL解码
            decoded_args = unquote(args_string)
            
            # 提取各个参数
            key_match = re.search(r'key=([^,]+)', decoded_args)
            role_id_match = re.search(r'roleId=([^,]+)', decoded_args)
            strengthen_key_match = re.search(r'strengthenKey=([^,]+)', decoded_args)
            
            result = {}
            if key_match:
                result['key'] = key_match.group(1).replace('_bingo', '')
            if role_id_match:
                result['roleId'] = role_id_match.group(1).replace('_bingo', '')
            if strengthen_key_match:
                result['strengthenKey'] = strengthen_key_match.group(1).replace('_bingo', '')
                
            return result
        except Exception as e:
            print(f"解析args字符串时出错: {e}")
            return {}
            
    def extract_from_network_logs(self):
        """从网络日志中提取API请求"""
        logs = self.driver.get_log('performance')
        api_requests = []
        
        for log in logs:
            try:
                message = json.loads(log['message'])
                if message['message']['method'] == 'Network.requestWillBeSent':
                    request = message['message']['params']['request']
                    url = request['url']
                    
                    # 查找包含newBabelAwardCollection的请求
                    if 'newBabelAwardCollection' in url or 'args' in url:
                        api_requests.append({
                            'url': url,
                            'method': request.get('method', 'GET'),
                            'headers': request.get('headers', {}),
                            'postData': request.get('postData', ''),
                            'timestamp': log['timestamp']
                        })
                        
                        # 解析URL中的args参数
                        parsed_url = urlparse(url)
                        query_params = parse_qs(parsed_url.query)
                        
                        if 'body' in query_params:
                            try:
                                body_data = json.loads(unquote(query_params['body'][0]))
                                if 'args' in body_data:
                                    args_data = self.parse_args_string(body_data['args'])
                                    if args_data:
                                        self.products_args.append({
                                            'activityId': body_data.get('activityId', ''),
                                            'scene': body_data.get('scene', ''),
                                            'args': args_data,
                                            'raw_args': body_data['args'],
                                            'api_url': url
                                        })
                            except:
                                pass
                                
            except Exception as e:
                continue
                
        return api_requests
        
    def find_and_click_reserve_buttons(self):
        """查找并点击预约按钮来触发API请求"""
        try:
            # 等待页面加载完成
            WebDriverWait(self.driver, 10).until(
                EC.presence_of_element_located((By.TAG_NAME, "body"))
            )
            
            # 滚动页面确保所有内容加载
            self.driver.execute_script("window.scrollTo(0, document.body.scrollHeight);")
            time.sleep(2)
            self.driver.execute_script("window.scrollTo(0, 0);")
            time.sleep(2)
            
            # 查找可能的预约按钮
            button_selectors = [
                "button",
                "div[onclick]",
                "a[onclick]",
                "[data-role]",
                ".btn",
                ".button",
                "[class*='reserve']",
                "[class*='book']",
                "[class*='btn']",
                "img[onclick]"
            ]
            
            all_buttons = []
            for selector in button_selectors:
                try:
                    elements = self.driver.find_elements(By.CSS_SELECTOR, selector)
                    all_buttons.extend(elements)
                except:
                    continue
            
            print(f"找到 {len(all_buttons)} 个可点击元素")
            
            # 点击每个按钮并监控网络请求
            clicked_count = 0
            for i, button in enumerate(all_buttons):
                try:
                    # 检查元素是否可见和可点击
                    if not button.is_displayed():
                        continue
                        
                    # 滚动到元素位置
                    self.driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", button)
                    time.sleep(0.5)
                    
                    # 记录点击前的网络日志数量
                    logs_before = len(self.driver.get_log('performance'))
                    
                    # 尝试点击
                    try:
                        button.click()
                    except:
                        # 如果普通点击失败，尝试JavaScript点击
                        self.driver.execute_script("arguments[0].click();", button)
                    
                    time.sleep(1)
                    clicked_count += 1
                    
                    # 检查是否有新的网络请求
                    logs_after = len(self.driver.get_log('performance'))
                    if logs_after > logs_before:
                        print(f"点击元素 {i+1} 触发了 {logs_after - logs_before} 个网络请求")
                        
                    # 限制点击次数，避免过度操作
                    if clicked_count >= 20:
                        break
                        
                except Exception as e:
                    continue
                    
            print(f"总共点击了 {clicked_count} 个元素")
            
        except TimeoutException:
            print("页面加载超时")
            
    def extract_from_page_source(self):
        """从页面源码中提取args参数"""
        page_source = self.driver.page_source
        
        # 查找包含args的JavaScript代码
        patterns = [
            r'args["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'newBabelAwardCollection[^}]*args["\']?\s*[:=]\s*["\']([^"\']+)["\']',
            r'key=([A-F0-9]+)[^,]*,roleId=([A-F0-9]+)[^,]*,strengthenKey=([A-F0-9]+)',
            r'"args"\s*:\s*"([^"]+)"',
            r'args:\s*"([^"]+)"'
        ]
        
        found_args = []
        for pattern in patterns:
            matches = re.findall(pattern, page_source, re.IGNORECASE)
            for match in matches:
                if isinstance(match, tuple):
                    # 如果是元组，说明匹配了多个组
                    if len(match) == 3:  # key, roleId, strengthenKey
                        found_args.append({
                            'key': match[0],
                            'roleId': match[1],
                            'strengthenKey': match[2],
                            'source': 'page_source_tuple'
                        })
                else:
                    # 单个匹配，尝试解析
                    parsed = self.parse_args_string(match)
                    if parsed:
                        parsed['source'] = 'page_source'
                        parsed['raw_args'] = match
                        found_args.append(parsed)
                        
        return found_args
        
    def extract_from_scripts(self):
        """从页面脚本中提取args参数"""
        script_elements = self.driver.find_elements(By.TAG_NAME, "script")
        found_args = []
        
        for i, script in enumerate(script_elements):
            try:
                script_content = script.get_attribute('innerHTML')
                if not script_content:
                    continue
                    
                # 查找包含args的内容
                if any(keyword in script_content.lower() for keyword in ['args', 'newbabelawardcollection', 'key=', 'roleid=', 'strengthenkey=']):
                    
                    # 提取args参数
                    patterns = [
                        r'args["\']?\s*[:=]\s*["\']([^"\']+)["\']',
                        r'"args"\s*:\s*"([^"]+)"',
                        r'args:\s*"([^"]+)"',
                        r'key=([A-F0-9]+)[^,]*,roleId=([A-F0-9]+)[^,]*,strengthenKey=([A-F0-9]+)'
                    ]
                    
                    for pattern in patterns:
                        matches = re.findall(pattern, script_content, re.IGNORECASE)
                        for match in matches:
                            if isinstance(match, tuple) and len(match) == 3:
                                found_args.append({
                                    'key': match[0],
                                    'roleId': match[1],
                                    'strengthenKey': match[2],
                                    'source': f'script_{i}',
                                    'script_index': i
                                })
                            elif isinstance(match, str):
                                parsed = self.parse_args_string(match)
                                if parsed:
                                    parsed['source'] = f'script_{i}'
                                    parsed['script_index'] = i
                                    parsed['raw_args'] = match
                                    found_args.append(parsed)
                                    
            except Exception as e:
                continue
                
        return found_args
        
    def run(self, url):
        """运行提取器"""
        try:
            print("正在设置浏览器...")
            self.setup_driver()
            
            print("正在加载页面...")
            self.driver.get(url)
            time.sleep(5)
            
            print("正在从页面源码提取args...")
            page_args = self.extract_from_page_source()
            
            print("正在从脚本提取args...")
            script_args = self.extract_from_scripts()
            
            print("正在查找并点击预约按钮...")
            self.find_and_click_reserve_buttons()
            
            print("正在从网络日志提取API请求...")
            api_requests = self.extract_from_network_logs()
            
            # 合并所有结果
            all_args = []
            all_args.extend(page_args)
            all_args.extend(script_args)
            all_args.extend(self.products_args)
            
            # 去重
            unique_args = []
            seen = set()
            for arg in all_args:
                # 使用key作为唯一标识
                key = arg.get('key', '')
                if key and key not in seen:
                    seen.add(key)
                    unique_args.append(arg)
            
            result = {
                'products_args': unique_args,
                'api_requests': api_requests,
                'total_found': len(unique_args)
            }
            
            return result
            
        except Exception as e:
            print(f"运行时出错: {e}")
            return None
            
        finally:
            if self.driver:
                self.driver.quit()

def main():
    url = "https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html?utm_term=Wxfriends_shareid284904fc33c87db3f1feed20927ea7e9de4cf01217484290615124_none_none&utm_user=plusmember&utm_source=iosapp&utm_campaign=t_335139774&utm_medium=appshare&_ts=1748429047707&ad_od=share&gxd=RnAowW9dYTTdncsWrNd_Wie_7AVEhwtLQ57Q5POY5NQF1Nnu39HRzEDnztROYz8&gx=RnAomTM2bGbbzs0UrdElX7eHy32dRXI&preventPV=1&forceCurrentView=1"
    
    extractor = JDArgsExtractorV2()
    result = extractor.run(url)
    
    if result:
        print("\n=== 提取结果 ===")
        print(f"找到 {result['total_found']} 个唯一的args参数")
        
        # 显示每个商品的args参数
        for i, product in enumerate(result['products_args'], 1):
            print(f"\n商品 {i}:")
            print(f"  key: {product.get('key', 'N/A')}")
            print(f"  roleId: {product.get('roleId', 'N/A')}")
            print(f"  strengthenKey: {product.get('strengthenKey', 'N/A')}")
            print(f"  来源: {product.get('source', 'N/A')}")
            if 'activityId' in product:
                print(f"  activityId: {product['activityId']}")
        
        # 保存完整结果
        with open('jd_products_args.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n完整结果已保存到 jd_products_args.json")
        
        # 保存简化的args参数
        simplified_args = []
        for product in result['products_args']:
            simplified_args.append({
                'key': product.get('key', ''),
                'roleId': product.get('roleId', ''),
                'strengthenKey': product.get('strengthenKey', ''),
                'activityId': product.get('activityId', ''),
                'source': product.get('source', '')
            })
            
        with open('jd_args_only.json', 'w', encoding='utf-8') as f:
            json.dump(simplified_args, f, indent=2, ensure_ascii=False)
        print("简化的args参数已保存到 jd_args_only.json")
        
    else:
        print("提取失败")

if __name__ == "__main__":
    main()
