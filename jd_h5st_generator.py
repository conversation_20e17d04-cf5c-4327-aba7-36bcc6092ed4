#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东h5st签名生成器
输入当前活动的body和h5st，返回新的h5st签名
"""

import json
import time
import hashlib
import hmac
import base64
import re
from urllib.parse import unquote, quote

class JDH5STGenerator:
    def __init__(self):
        self.function_id = "newBabelAwardCollection"
        
    def parse_existing_h5st(self, h5st_encoded):
        """解析现有的h5st参数，提取关键信息"""
        try:
            # URL解码
            h5st_decoded = unquote(h5st_encoded)
            print(f"解析现有h5st: {h5st_decoded}")
            
            # 分割各个部分
            parts = h5st_decoded.split(';')
            
            if len(parts) >= 10:
                parsed = {
                    'timestamp': parts[0],
                    'fingerprint': parts[1],
                    'version': parts[2], 
                    'token': parts[3],
                    'signature1': parts[4],
                    'sdk_version': parts[5],
                    'timestamp2': parts[6],
                    'encrypted_data': parts[7],
                    'signature2': parts[8],
                    'device_id': parts[9]
                }
                
                print("解析结果:")
                for key, value in parsed.items():
                    print(f"  {key}: {value}")
                    
                return parsed
            else:
                print(f"h5st格式不完整，只有 {len(parts)} 个部分")
                return None
                
        except Exception as e:
            print(f"解析h5st失败: {e}")
            return None
            
    def extract_token_algorithm(self, existing_token):
        """从现有token中提取算法模式"""
        try:
            # 分析token结构
            if existing_token.startswith('tk'):
                # 提取token的基础部分和变化部分
                if '_' in existing_token:
                    parts = existing_token.split('_')
                    base_part = parts[0]  # tk05w...
                    variable_part = parts[1] if len(parts) > 1 else ''
                    
                    return {
                        'prefix': base_part[:5],  # tk05w
                        'hash_part': base_part[5:] if len(base_part) > 5 else '',
                        'suffix': variable_part,
                        'pattern': 'tk05w_pattern'
                    }
                else:
                    return {
                        'prefix': existing_token[:5],
                        'hash_part': existing_token[5:15] if len(existing_token) > 15 else existing_token[5:],
                        'suffix': existing_token[15:] if len(existing_token) > 15 else '',
                        'pattern': 'tk05w_simple'
                    }
            else:
                return {
                    'prefix': existing_token[:5],
                    'hash_part': existing_token[5:],
                    'suffix': '',
                    'pattern': 'unknown'
                }
                
        except Exception as e:
            print(f"提取token算法失败: {e}")
            return None
            
    def generate_new_token(self, token_info, new_timestamp):
        """基于现有token信息生成新token"""
        try:
            if not token_info:
                # 默认token生成
                return f"tk05w{hashlib.md5(str(new_timestamp).encode()).hexdigest()[:10]}"
                
            prefix = token_info.get('prefix', 'tk05w')
            
            # 生成新的哈希部分
            hash_input = f"{new_timestamp}{self.function_id}"
            new_hash = hashlib.md5(hash_input.encode()).hexdigest()[:10]
            
            # 保持原有的后缀模式
            suffix = token_info.get('suffix', '')
            if suffix:
                new_token = f"{prefix}{new_hash}_{suffix}"
            else:
                new_token = f"{prefix}{new_hash}"
                
            return new_token
            
        except Exception as e:
            print(f"生成新token失败: {e}")
            return f"tk05w{hashlib.md5(str(new_timestamp).encode()).hexdigest()[:10]}"
            
    def generate_signature(self, timestamp, fingerprint, version, token, body_str):
        """生成签名"""
        try:
            # 构造签名输入字符串
            sign_input = f"{timestamp};{fingerprint};{version};{token};{self.function_id};{body_str}"
            
            # 生成MD5签名
            signature = hashlib.md5(sign_input.encode()).hexdigest()
            
            return signature
            
        except Exception as e:
            print(f"生成签名失败: {e}")
            return hashlib.md5(f"{timestamp}{body_str}".encode()).hexdigest()
            
    def generate_encrypted_data(self, fingerprint, timestamp, token):
        """生成加密数据部分"""
        try:
            # 基于指纹和时间戳生成加密数据
            base_input = f"{fingerprint}{timestamp}{token}"
            hash_result = hashlib.md5(base_input.encode()).hexdigest()
            
            # 构造加密数据格式
            encrypted_data = f"ri_ux{hash_result[:50]}"
            
            return encrypted_data
            
        except Exception as e:
            print(f"生成加密数据失败: {e}")
            return f"ri_ux{hashlib.md5(str(timestamp).encode()).hexdigest()[:50]}"
            
    def generate_second_signature(self, first_signature, encrypted_data, body_str):
        """生成第二个签名"""
        try:
            # 组合多个元素生成第二个签名
            sign_input = f"{first_signature}{encrypted_data}{body_str}"
            signature2 = hashlib.sha256(sign_input.encode()).hexdigest()
            
            return signature2
            
        except Exception as e:
            print(f"生成第二个签名失败: {e}")
            return hashlib.sha256(f"{first_signature}{encrypted_data}".encode()).hexdigest()
            
    def generate_device_id(self, fingerprint, timestamp):
        """生成设备ID"""
        try:
            # 基于指纹和时间戳生成设备ID
            device_input = f"{fingerprint}{timestamp}"
            hash_result = hashlib.md5(device_input.encode()).hexdigest()
            
            device_id = f"ri_u{hash_result[:30]}"
            
            return device_id
            
        except Exception as e:
            print(f"生成设备ID失败: {e}")
            return f"ri_u{hashlib.md5(str(timestamp).encode()).hexdigest()[:30]}"
            
    def generate_new_h5st(self, body_str, existing_h5st):
        """生成新的h5st签名"""
        print("=== 开始生成新的h5st签名 ===")
        
        # 解析现有h5st
        parsed_h5st = self.parse_existing_h5st(existing_h5st)
        if not parsed_h5st:
            print("无法解析现有h5st，使用默认参数")
            parsed_h5st = {
                'fingerprint': 'p9xwi33igjhmpm05',
                'version': '35fa0',
                'sdk_version': '5.1',
                'token': 'tk05wdefault'
            }
            
        # 生成新的时间戳
        new_timestamp = int(time.time() * 1000)
        
        # 提取token算法信息
        token_info = self.extract_token_algorithm(parsed_h5st.get('token', ''))
        
        # 生成新的token
        new_token = self.generate_new_token(token_info, new_timestamp)
        
        # 使用现有的指纹和版本信息
        fingerprint = parsed_h5st.get('fingerprint', 'p9xwi33igjhmpm05')
        version = parsed_h5st.get('version', '35fa0')
        sdk_version = parsed_h5st.get('sdk_version', '5.1')
        
        # 生成第一个签名
        signature1 = self.generate_signature(new_timestamp, fingerprint, version, new_token, body_str)
        
        # 生成加密数据
        encrypted_data = self.generate_encrypted_data(fingerprint, new_timestamp, new_token)
        
        # 生成第二个签名
        signature2 = self.generate_second_signature(signature1, encrypted_data, body_str)
        
        # 生成设备ID
        device_id = self.generate_device_id(fingerprint, new_timestamp)
        
        # 构造新的h5st
        new_h5st_parts = [
            str(new_timestamp)[:14],  # 时间戳（14位）
            fingerprint,              # 指纹
            version,                  # 版本
            new_token,               # 新token
            signature1,              # 第一个签名
            sdk_version,             # SDK版本
            str(new_timestamp),      # 完整时间戳
            encrypted_data,          # 加密数据
            signature2,              # 第二个签名
            device_id                # 设备ID
        ]
        
        # 拼接并编码
        new_h5st = ';'.join(new_h5st_parts)
        new_h5st_encoded = quote(new_h5st)
        
        print(f"\n=== 生成结果 ===")
        print(f"新时间戳: {new_timestamp}")
        print(f"新token: {new_token}")
        print(f"第一个签名: {signature1}")
        print(f"第二个签名: {signature2[:32]}...")
        print(f"新h5st: {new_h5st}")
        print(f"编码后: {new_h5st_encoded}")
        
        return {
            'h5st_raw': new_h5st,
            'h5st_encoded': new_h5st_encoded,
            'timestamp': new_timestamp,
            'token': new_token,
            'signature1': signature1,
            'signature2': signature2,
            'components': {
                'timestamp': str(new_timestamp)[:14],
                'fingerprint': fingerprint,
                'version': version,
                'token': new_token,
                'signature1': signature1,
                'sdk_version': sdk_version,
                'timestamp2': str(new_timestamp),
                'encrypted_data': encrypted_data,
                'signature2': signature2,
                'device_id': device_id
            }
        }
        
    def validate_inputs(self, body_str, existing_h5st):
        """验证输入参数"""
        if not body_str:
            return False, "body参数不能为空"
            
        if not existing_h5st:
            return False, "现有h5st不能为空"
            
        # 验证body是否为有效JSON
        try:
            json.loads(body_str)
        except:
            return False, "body必须是有效的JSON字符串"
            
        return True, "输入验证通过"

def main():
    generator = JDH5STGenerator()
    
    print("=== 京东h5st签名生成器 ===")
    print("请输入当前活动的body和h5st，生成新的h5st签名")
    
    # 获取用户输入
    print("\n请输入body参数 (JSON格式):")
    body_input = input().strip()
    
    print("\n请输入现有的h5st参数:")
    h5st_input = input().strip()
    
    # 验证输入
    is_valid, message = generator.validate_inputs(body_input, h5st_input)
    if not is_valid:
        print(f"输入验证失败: {message}")
        return
        
    # 生成新的h5st
    try:
        result = generator.generate_new_h5st(body_input, h5st_input)
        
        print(f"\n=== 最终结果 ===")
        print(f"新的h5st签名: {result['h5st_encoded']}")
        
        # 保存结果
        with open('new_h5st_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        print(f"\n详细结果已保存到 new_h5st_result.json")
        
        # 显示使用示例
        print(f"\n=== 使用示例 ===")
        print(f"curl -X POST 'https://api.m.jd.com/client.action' \\")
        print(f"  -H 'Content-Type: application/x-www-form-urlencoded' \\")
        print(f"  -d 'functionId=newBabelAwardCollection' \\")
        print(f"  -d 'client=wh5' \\")
        print(f"  -d 'body={body_input}' \\")
        print(f"  -d 'h5st={result['h5st_encoded']}' \\")
        print(f"  -d 'appid=babelh5'")
        
    except Exception as e:
        print(f"生成h5st失败: {e}")

if __name__ == "__main__":
    main()
