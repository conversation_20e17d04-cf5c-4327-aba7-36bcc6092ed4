#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东h5st签名API
简洁的API接口，输入body和h5st，返回新的h5st
"""

import json
import time
import hashlib
from urllib.parse import unquote, quote

def generate_new_h5st(body_str, existing_h5st):
    """
    生成新的h5st签名
    
    Args:
        body_str: 当前活动的body参数 (JSON字符串)
        existing_h5st: 现有的h5st参数
        
    Returns:
        新的h5st签名 (URL编码后的字符串)
    """
    
    # 解析现有h5st
    try:
        h5st_decoded = unquote(existing_h5st)
        parts = h5st_decoded.split(';')
        
        if len(parts) >= 4:
            fingerprint = parts[1]
            version = parts[2]
            old_token = parts[3]
        else:
            # 使用默认值
            fingerprint = 'p9xwi33igjhmpm05'
            version = '35fa0'
            old_token = 'tk05wdefault'
    except:
        # 解析失败，使用默认值
        fingerprint = 'p9xwi33igjhmpm05'
        version = '35fa0'
        old_token = 'tk05wdefault'
    
    # 生成新时间戳
    new_timestamp = int(time.time() * 1000)
    
    # 生成新token (保持原有格式)
    if old_token.startswith('tk05w'):
        token_hash = hashlib.md5(f"{new_timestamp}newBabelAwardCollection".encode()).hexdigest()[:10]
        new_token = f"tk05w{token_hash}"
    else:
        new_token = f"tk05w{hashlib.md5(str(new_timestamp).encode()).hexdigest()[:10]}"
    
    # 生成第一个签名
    sign_input = f"{new_timestamp};{fingerprint};{version};{new_token};newBabelAwardCollection;{body_str}"
    signature1 = hashlib.md5(sign_input.encode()).hexdigest()
    
    # 生成加密数据
    encrypted_input = f"{fingerprint}{new_timestamp}{new_token}"
    encrypted_hash = hashlib.md5(encrypted_input.encode()).hexdigest()
    encrypted_data = f"ri_ux{encrypted_hash[:50]}"
    
    # 生成第二个签名
    signature2_input = f"{signature1}{encrypted_data}{body_str}"
    signature2 = hashlib.sha256(signature2_input.encode()).hexdigest()
    
    # 生成设备ID
    device_input = f"{fingerprint}{new_timestamp}"
    device_hash = hashlib.md5(device_input.encode()).hexdigest()
    device_id = f"ri_u{device_hash[:30]}"
    
    # 构造新h5st
    new_h5st_parts = [
        str(new_timestamp)[:14],  # 时间戳（14位）
        fingerprint,              # 指纹
        version,                  # 版本
        new_token,               # 新token
        signature1,              # 第一个签名
        '5.1',                   # SDK版本
        str(new_timestamp),      # 完整时间戳
        encrypted_data,          # 加密数据
        signature2,              # 第二个签名
        device_id                # 设备ID
    ]
    
    new_h5st = ';'.join(new_h5st_parts)
    return quote(new_h5st)

def main():
    """命令行接口"""
    print("=== 京东h5st签名生成器 ===")
    
    # 示例数据
    example_body = '{"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554838"}'
    example_h5st = "1749061453285%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wc261032869%3Bb018b6f40ecfc21d8ba7b48f2f2b9246%3B5.1%3B1749061453285%3Bri_uxf17ef90ed3379163727ba5a6e16a6856%3B586369ee79ec35544e7588382f5aed28a730f4e5161c2967dc022a582962f854%3Bri_uf17ef90ed3379163727ba5a6e16a68"
    
    print("\n选择输入方式:")
    print("1. 手动输入")
    print("2. 使用示例数据")
    
    choice = input("请选择 (1/2): ").strip()
    
    if choice == "2":
        body_str = example_body
        existing_h5st = example_h5st
        print(f"\n使用示例数据:")
        print(f"Body: {body_str}")
        print(f"现有h5st: {existing_h5st[:50]}...")
    else:
        print("\n请输入body参数 (JSON格式):")
        body_str = input().strip()
        
        print("\n请输入现有的h5st参数:")
        existing_h5st = input().strip()
    
    # 验证body格式
    try:
        json.loads(body_str)
    except:
        print("错误: body必须是有效的JSON格式")
        return
    
    # 生成新h5st
    try:
        new_h5st = generate_new_h5st(body_str, existing_h5st)
        
        print(f"\n=== 生成结果 ===")
        print(f"新的h5st: {new_h5st}")
        
        # 生成完整的cURL命令
        print(f"\n=== 完整cURL命令 ===")
        print(f"curl -X POST 'https://api.m.jd.com/client.action' \\")
        print(f"  -H 'User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D' \\")
        print(f"  -H 'Content-Type: application/x-www-form-urlencoded' \\")
        print(f"  -H 'Cookie: YOUR_COOKIES_HERE' \\")
        print(f"  -d 'functionId=newBabelAwardCollection' \\")
        print(f"  -d 'client=wh5' \\")
        print(f"  -d 'clientVersion=1.0.0' \\")
        print(f"  -d 'body={body_str}' \\")
        print(f"  -d 'h5st={new_h5st}' \\")
        print(f"  -d 'appid=babelh5'")
        
        # 保存结果
        result = {
            'input_body': body_str,
            'input_h5st': existing_h5st,
            'output_h5st': new_h5st,
            'timestamp': int(time.time()),
            'curl_command': f"curl -X POST 'https://api.m.jd.com/client.action' -H 'Content-Type: application/x-www-form-urlencoded' -H 'Cookie: YOUR_COOKIES_HERE' -d 'functionId=newBabelAwardCollection' -d 'client=wh5' -d 'body={body_str}' -d 'h5st={new_h5st}' -d 'appid=babelh5'"
        }
        
        with open('h5st_result.json', 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        print(f"\n结果已保存到 h5st_result.json")
        
    except Exception as e:
        print(f"生成失败: {e}")

# 快速测试函数
def quick_test():
    """快速测试函数"""
    test_body = '{"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","floorId":"117554838"}'
    test_h5st = "1749061453285%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05wc261032869%3Bb018b6f40ecfc21d8ba7b48f2f2b9246%3B5.1%3B1749061453285%3Bri_uxf17ef90ed3379163727ba5a6e16a6856%3B586369ee79ec35544e7588382f5aed28a730f4e5161c2967dc022a582962f854%3Bri_uf17ef90ed3379163727ba5a6e16a68"
    
    new_h5st = generate_new_h5st(test_body, test_h5st)
    print(f"测试结果: {new_h5st}")
    return new_h5st

if __name__ == "__main__":
    main()
