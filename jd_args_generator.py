#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
京东预约抢购商品args参数生成器
基于逆向分析结果的最终实现版本
"""

import hashlib
import time
import json
import requests
from urllib.parse import quote

class JDArgsGenerator:
    def __init__(self, activity_id="4P9a2T9osR9JvtzHVaYTPvsecRtg"):
        self.activity_id = activity_id
        
        # 从逆向分析中获得的模块ID列表（4个预约抢购商品）
        self.module_ids = [
            "117554838",  # 商品1
            "117554839",  # 商品2  
            "117554841",  # 商品3
            "117554842"   # 商品4
        ]
        
        # 设备指纹和会话信息（可以动态生成或使用固定值）
        self.device_fingerprint = "6053bf3c-44eb-5a8d-9a3b-afb7dcf08753"
        self.agent_sid = "a0778fe2-7fb2-4f95-93fa-a4982b5c3747"
        
        # 从tk算法中提取的关键信息
        self.tk_token = "tk03wa1b51be918nRDctPOLklnOpcIs1_9UZMdP60XQj8lINPlW7Jab5LL3uczj3nd3URFgmXgdv07uW79H3YcU4_vgl"
        self.random_salt = "Tox130bEp5sI"
        
    def generate_key(self, module_id, timestamp, strategy=1):
        """生成key参数"""
        if strategy == 1:
            # 策略1: MD5(module_id + timestamp)
            input_str = f"{module_id}{timestamp}"
        elif strategy == 2:
            # 策略2: MD5(activity_id + module_id)
            input_str = f"{self.activity_id}{module_id}"
        elif strategy == 3:
            # 策略3: MD5(device_fingerprint + module_id)
            input_str = f"{self.device_fingerprint}{module_id}"
        elif strategy == 4:
            # 策略4: 基于tk算法的生成
            input_str = f"{self.tk_token}{self.device_fingerprint}{timestamp}{self.activity_id}{self.random_salt}"
        else:
            # 默认策略
            input_str = f"{module_id}{timestamp}"
            
        return hashlib.md5(input_str.encode()).hexdigest().upper()
        
    def generate_role_id(self, module_id, timestamp, strategy=1):
        """生成roleId参数（长度约80个字符）"""
        if strategy == 1:
            base_input = f"{self.activity_id}{module_id}{timestamp}"
        elif strategy == 2:
            base_input = f"{self.device_fingerprint}{module_id}{self.agent_sid}"
        elif strategy == 3:
            base_input = f"{module_id}{timestamp}{self.activity_id}{self.device_fingerprint}"
        else:
            base_input = f"{self.activity_id}{module_id}{timestamp}"
            
        # 生成多个哈希并拼接以达到所需长度
        hash1 = hashlib.md5(base_input.encode()).hexdigest().upper()
        hash2 = hashlib.md5((base_input + "salt1").encode()).hexdigest().upper()
        hash3 = hashlib.md5((base_input + "salt2").encode()).hexdigest().upper()
        
        # 拼接并截取到合适长度（约80字符）
        combined = hash1 + hash2 + hash3
        return combined[:80]
        
    def generate_strengthen_key(self, module_id, timestamp, strategy=1):
        """生成strengthenKey参数（64位）"""
        if strategy == 1:
            input_str = f"{module_id}{self.activity_id}"
        elif strategy == 2:
            input_str = f"{timestamp}{module_id}"
        elif strategy == 3:
            input_str = f"{self.device_fingerprint}{module_id}"
        else:
            input_str = f"{module_id}{self.activity_id}"
            
        # 使用SHA256生成64位哈希
        return hashlib.sha256(input_str.encode()).hexdigest().upper()
        
    def generate_args_for_product(self, product_index, strategy=1):
        """为指定商品生成args参数"""
        if product_index < 0 or product_index >= len(self.module_ids):
            raise ValueError(f"商品索引必须在0-{len(self.module_ids)-1}之间")
            
        module_id = self.module_ids[product_index]
        timestamp = int(time.time() * 1000)
        
        # 生成三个参数
        key = self.generate_key(module_id, timestamp, strategy)
        role_id = self.generate_role_id(module_id, timestamp, strategy)
        strengthen_key = self.generate_strengthen_key(module_id, timestamp, strategy)
        
        # 构造args字符串
        args_string = f"key={key}_bingo,roleId={role_id}_bingo,strengthenKey={strengthen_key}_bingo"
        
        return {
            'product_index': product_index,
            'module_id': module_id,
            'strategy': strategy,
            'timestamp': timestamp,
            'key': key,
            'roleId': role_id,
            'strengthenKey': strengthen_key,
            'args': args_string
        }
        
    def generate_all_products_args(self, strategy=1):
        """为所有4个商品生成args参数"""
        results = []
        
        for i in range(len(self.module_ids)):
            product_args = self.generate_args_for_product(i, strategy)
            results.append(product_args)
            
        return results
        
    def test_args_parameter(self, args_string):
        """测试args参数是否有效"""
        # 构造API请求
        body = {
            "activityId": self.activity_id,
            "scene": "1",
            "args": args_string
        }
        
        body_json = json.dumps(body, separators=(',', ':'))
        body_encoded = quote(body_json)
        
        url = f"https://api.m.jd.com/client.action?functionId=newBabelAwardCollection&client=wh5&body={body_encoded}"
        
        headers = {
            'User-Agent': 'jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D',
            'Referer': f'https://pro.m.jd.com/mall/active/{self.activity_id}/index.html',
            'Content-Type': 'application/x-www-form-urlencoded',
        }
        
        try:
            response = requests.get(url, headers=headers, timeout=10)
            
            if response.status_code == 200:
                try:
                    result = response.json()
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response': result
                    }
                except:
                    return {
                        'success': True,
                        'status_code': response.status_code,
                        'response': response.text
                    }
            else:
                return {
                    'success': False,
                    'status_code': response.status_code,
                    'error': f"HTTP {response.status_code}"
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }
            
    def generate_and_test_all(self):
        """生成并测试所有商品的args参数"""
        print("=== 京东预约抢购商品args参数生成器 ===")
        print(f"活动ID: {self.activity_id}")
        print(f"商品数量: {len(self.module_ids)}")
        
        all_results = []
        
        # 为每个商品生成多种策略的参数
        for product_index in range(len(self.module_ids)):
            print(f"\n--- 商品 {product_index + 1} (模块ID: {self.module_ids[product_index]}) ---")
            
            product_results = []
            
            # 测试多种策略
            for strategy in range(1, 4):
                print(f"\n策略 {strategy}:")
                
                try:
                    # 生成参数
                    args_data = self.generate_args_for_product(product_index, strategy)
                    
                    print(f"  Key: {args_data['key']}")
                    print(f"  RoleId: {args_data['roleId']}")
                    print(f"  StrengthenKey: {args_data['strengthenKey']}")
                    
                    # 测试参数
                    test_result = self.test_args_parameter(args_data['args'])
                    
                    if test_result['success']:
                        if 'response' in test_result and isinstance(test_result['response'], dict):
                            if test_result['response'].get('code') == '3':
                                print(f"  ✅ 参数格式正确 (需要登录)")
                            else:
                                print(f"  ✅ API调用成功: {test_result['response']}")
                        else:
                            print(f"  ✅ API调用成功")
                    else:
                        print(f"  ❌ API调用失败: {test_result.get('error', 'Unknown error')}")
                    
                    # 保存结果
                    combined_result = {
                        **args_data,
                        'test_result': test_result
                    }
                    product_results.append(combined_result)
                    
                except Exception as e:
                    print(f"  ❌ 生成失败: {e}")
                    
            all_results.append({
                'product_index': product_index,
                'module_id': self.module_ids[product_index],
                'strategies': product_results
            })
            
        # 保存所有结果
        output = {
            'activity_id': self.activity_id,
            'generation_time': time.time(),
            'products': all_results
        }
        
        with open('jd_final_args_results.json', 'w', encoding='utf-8') as f:
            json.dump(output, f, indent=2, ensure_ascii=False)
            
        print(f"\n=== 生成完成 ===")
        print(f"所有结果已保存到 jd_final_args_results.json")
        
        # 输出最佳参数
        print(f"\n=== 推荐使用的args参数 ===")
        for i, product in enumerate(all_results):
            print(f"\n商品 {i+1}:")
            # 选择第一个成功的策略
            for strategy in product['strategies']:
                if strategy['test_result']['success']:
                    print(f"  Args: {strategy['args']}")
                    break
                    
        return all_results

def main():
    # 创建生成器实例
    generator = JDArgsGenerator()
    
    # 生成并测试所有参数
    results = generator.generate_and_test_all()
    
    print(f"\n处理完成！共生成了 {len(results)} 个商品的args参数")

if __name__ == "__main__":
    main()
