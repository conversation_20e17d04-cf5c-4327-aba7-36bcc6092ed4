# 京东预约抢购 - 基于JavaScript分析发现
# 生成时间: 2025-06-05 02:49:52
# 关键发现: args使用join(';')连接，参数格式为key=value_bingo

# 商品 1 - 基于JavaScript发现
# 楼层ID: 117554838
# Args模式: join(';') 连接
curl -X POST "https://api.m.jd.com/client.action" \
  -H "User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "X-Babel-ActId: 4P9a2T9osR9JvtzHVaYTPvsecRtg" \
  -H "x-referer-page: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html" \
  -H "x-rp-client: h5_1.0.0" \
  -H "Cookie: YOUR_COOKIES_HERE" \
  -d "functionId=newBabelAwardCollection" \
  -d "client=wh5" \
  -d "clientVersion=1.0.0" \
  -d "body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","args":"key=5ac806c6aa009865_bingo;roleId=0715f52c5239c8a19e71_bingo;strengthenKey=a6478a3d63e1b68632_bingo","floorId":"117554838"}" \
  -d "h5st=1749062992966%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w38fc09e446%3Bba636064793feab9946ae8358268e324%3B5.1%3B1749062992966%3Bri_uxa7885fc2be6603d0118c0e30a90d6755%3B13c1ba9b19755ed3f0b99a171e7be47d2893be01d63ccf425a6d460c96f87e21%3Bri_ua764e802ca899ffa53034ed251c0b5" \
  -d "appid=babelh5"

# Args详情: key=5ac806c6aa009865_bingo;roleId=0715f52c5239c8a19e71_bingo;strengthenKey=a6478a3d63e1b68632_bingo

================================================================================

# 商品 2 - 基于JavaScript发现
# 楼层ID: 117554839
# Args模式: join(';') 连接
curl -X POST "https://api.m.jd.com/client.action" \
  -H "User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "X-Babel-ActId: 4P9a2T9osR9JvtzHVaYTPvsecRtg" \
  -H "x-referer-page: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html" \
  -H "x-rp-client: h5_1.0.0" \
  -H "Cookie: YOUR_COOKIES_HERE" \
  -d "functionId=newBabelAwardCollection" \
  -d "client=wh5" \
  -d "clientVersion=1.0.0" \
  -d "body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","args":"key=27b5f66ecfe1ef3b_bingo;roleId=ac03e5ace0932907ae4d_bingo;strengthenKey=c6880f60727d86d46e_bingo","floorId":"117554839"}" \
  -d "h5st=1749062992966%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w38fc09e446%3Bc9f1eb1a5b53113d57bd44f75ed9eca3%3B5.1%3B1749062992966%3Bri_uxa7885fc2be6603d0118c0e30a90d6755%3B1b89b5b5ba2ece2ef94e280775bb197caeeab05624d79ebea3065a0ab6c8454b%3Bri_ua764e802ca899ffa53034ed251c0b5" \
  -d "appid=babelh5"

# Args详情: key=27b5f66ecfe1ef3b_bingo;roleId=ac03e5ace0932907ae4d_bingo;strengthenKey=c6880f60727d86d46e_bingo

================================================================================

# 商品 3 - 基于JavaScript发现
# 楼层ID: 117554841
# Args模式: join(';') 连接
curl -X POST "https://api.m.jd.com/client.action" \
  -H "User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "X-Babel-ActId: 4P9a2T9osR9JvtzHVaYTPvsecRtg" \
  -H "x-referer-page: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html" \
  -H "x-rp-client: h5_1.0.0" \
  -H "Cookie: YOUR_COOKIES_HERE" \
  -d "functionId=newBabelAwardCollection" \
  -d "client=wh5" \
  -d "clientVersion=1.0.0" \
  -d "body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","args":"key=78c0feb261fcce77_bingo;roleId=132debc7a6013650a31e_bingo;strengthenKey=8abdeacbcf699aa61a_bingo","floorId":"117554841"}" \
  -d "h5st=1749062992967%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w12ad59902d%3B1295ca0bad3c39073cca558317e6682e%3B5.1%3B1749062992967%3Bri_ux190ea36e95e5221b3a225f6dcf0ed89e%3B5c99aa7a0ce3d3d45cd554f42dce25eb08f28580d8802578ffce71b325d05032%3Bri_u61c2c862667822ed130c32f2a285bc" \
  -d "appid=babelh5"

# Args详情: key=78c0feb261fcce77_bingo;roleId=132debc7a6013650a31e_bingo;strengthenKey=8abdeacbcf699aa61a_bingo

================================================================================

# 商品 4 - 基于JavaScript发现
# 楼层ID: 117554842
# Args模式: join(';') 连接
curl -X POST "https://api.m.jd.com/client.action" \
  -H "User-Agent: jdapp;iPhone;13.2.8;;;M/5.0;appBuild/169498;jdSupportDarkMode/0;ef/1;ep/%7B%22ciphertype%22%3A5%2C%22cipher%22%3A%7B%22ud%22%3A%22Ctq0EJK0ZwCzC2C4D2HsC2YnZwVvZNSmEJS3ZWO3ZJvuZJHtZtKnCq%3D%3D%22%2C%22sv%22%3A%22CJGkCG%3D%3D%22%2C%22iad%22%3A%22%22%7D%2C%22ts%22%3A1727445601%2C%22hdid%22%3A%22JM9F1ywUPwflvMIpYPok0tt5k9kW4ArJEU3lfLhxBqw%3D%22%2C%22version%22%3A%221.0.3%22%2C%22appname%22%3A%22com.360buy.jdmobile%22%2C%22ridx%22%3A-1%7D" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -H "X-Babel-ActId: 4P9a2T9osR9JvtzHVaYTPvsecRtg" \
  -H "x-referer-page: https://pro.m.jd.com/mall/active/4P9a2T9osR9JvtzHVaYTPvsecRtg/index.html" \
  -H "x-rp-client: h5_1.0.0" \
  -H "Cookie: YOUR_COOKIES_HERE" \
  -d "functionId=newBabelAwardCollection" \
  -d "client=wh5" \
  -d "clientVersion=1.0.0" \
  -d "body={"activityId":"4P9a2T9osR9JvtzHVaYTPvsecRtg","scene":"1","args":"key=5dc6ebb2e34e1695_bingo;roleId=3756ae40600701a991ce_bingo;strengthenKey=1f9ebcd7cfb1935b6d_bingo","floorId":"117554842"}" \
  -d "h5st=1749062992967%3Bp9xwi33igjhmpm05%3B35fa0%3Btk05w12ad59902d%3Bad5153e5466be38150a8bcf273fa84c7%3B5.1%3B1749062992967%3Bri_ux190ea36e95e5221b3a225f6dcf0ed89e%3Bf114f48dae88052ecc1bd623067c8405fa49c6748c52ca9a1e6d136622c8b8ec%3Bri_u61c2c862667822ed130c32f2a285bc" \
  -d "appid=babelh5"

# Args详情: key=5dc6ebb2e34e1695_bingo;roleId=3756ae40600701a991ce_bingo;strengthenKey=1f9ebcd7cfb1935b6d_bingo

================================================================================

